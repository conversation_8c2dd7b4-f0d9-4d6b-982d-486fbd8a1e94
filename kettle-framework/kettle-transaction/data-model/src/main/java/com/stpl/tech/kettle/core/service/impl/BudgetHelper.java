package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.data.vo.PnlRecord;
import com.stpl.tech.kettle.data.dao.impl.BudgetUtils;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.domain.model.UnitExpenditure;
import com.stpl.tech.master.Counter;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public class BudgetHelper {

    private BudgetHelper() {
    }

    private static final Logger LOG = LoggerFactory.getLogger(BudgetHelper.class);

    public static void createPnlDetailSheet(List<UnitExpenditureDetail> expenditureDetails, Workbook workbook) {
        Sheet sheet = workbook.createSheet("MTD PNL Details For Month");
        Counter columnCount = new Counter(0);
        generateSheet(workbook, sheet, columnCount);

        if (!expenditureDetails.isEmpty()) {
            for (UnitExpenditureDetail detail : expenditureDetails) {
                createColumn(sheet, columnCount.increment(), detail);
            }
        }
    }

    private static void createColumn(Sheet sheet, Counter columnCount, UnitExpenditureDetail detail) {
        Map<String, Map<String, UnitExpenditure>> resultMap = BudgetUtils.getAnnotations(UnitExpenditureDetail.class);
        Map<Integer, List<String>> orderMap = new TreeMap<>();

        for (Map.Entry<String, Map<String, UnitExpenditure>> map : resultMap.entrySet()) {
            switch (map.getKey()) {
                case "SALES":
                    for (Map.Entry<String, UnitExpenditure> salesMap : map.getValue().entrySet()) {
                        UnitExpenditure expenditure = salesMap.getValue();
                        updateOrderMap(orderMap, expenditure.getOrder(),
                            BudgetUtils.getReadMethodValue(expenditure.getEntitys().get(0), detail).toString());
                    }
                    break;
                case "KEY_FIELDS":
                    for (Map.Entry<String, UnitExpenditure> salesMap : map.getValue().entrySet()) {
                        UnitExpenditure expenditure = salesMap.getValue();
                        updateOrderMap(orderMap, expenditure.getOrder(),
                            BudgetUtils.getReadMethodValue(expenditure.getEntitys().get(0), detail).toString());
                    }
                    break;
                case "PROFIT":
                    for (Map.Entry<String, UnitExpenditure> salesMap : map.getValue().entrySet()) {
                        UnitExpenditure expenditure = salesMap.getValue();
                        updateOrderMap(orderMap, expenditure.getOrder(),
                            BudgetUtils.getReadMethodValue(expenditure.getEntitys().get(0), detail).toString());
                    }
                    break;
                default:
                    UnitExpenditure expenditure = map.getValue().get(map.getKey());
                    BigDecimal value = BigDecimal.ZERO;
                    for (String method : expenditure.getEntitys()) {
                        BigDecimal temp = BudgetUtils.getReadMethodValue(method, detail) != null
                            ? (BigDecimal) BudgetUtils.getReadMethodValue(method, detail)
                            : BigDecimal.ZERO;
                        if (expenditure.isSplit()) {
                            updateOrderMap(orderMap, expenditure.getOrder(), temp.toString());
                        }
                        value = AppUtils.add(value, temp);
                    }
                    updateOrderMap(orderMap, expenditure.getOrder(), value.toString());
                    break;
            }
        }

        int rowCount = 0;
        try {
            for (Map.Entry<Integer, List<String>> entry : orderMap.entrySet()) {
                for (String item : entry.getValue()) {
                    sheet.getRow(rowCount++).createCell(columnCount.getC()).setCellValue(item);
                }
            }
        } catch (Exception e) {
            LOG.error("Error while creating column {} at row {}", columnCount.getC(), rowCount, e);
        }
    }

    private static Sheet generateSheet(Workbook workbook, Sheet sheet, Counter columnCounter) {
        try {
            int row = 0;
            sheet.setDefaultColumnWidth(20);
            CellStyle style = generateHeaderStyle(workbook);
            Map<String, Map<String, UnitExpenditure>> resultMap = BudgetUtils
                .getAnnotations(UnitExpenditureDetail.class);
            Map<Integer, List<String>> orderMap = new TreeMap<>();
            for (Map.Entry<String, Map<String, UnitExpenditure>> map : resultMap.entrySet()) {
                switch (map.getKey()) {
                    case "SALES":

                        for (Map.Entry<String, UnitExpenditure> salesMap : map.getValue().entrySet()) {
                            UnitExpenditure expenditure = salesMap.getValue();
                            updateOrderMap(orderMap, expenditure.getOrder(), expenditure.getLabel());
                        }
                        break;
                    case "KEY_FIELDS":
                        for (Map.Entry<String, UnitExpenditure> salesMap : map.getValue().entrySet()) {
                            UnitExpenditure expenditure = salesMap.getValue();
                            updateOrderMap(orderMap, expenditure.getOrder(), expenditure.getLabel());
                        }
                        break;
                    case "PROFIT":
                        for (Map.Entry<String, UnitExpenditure> salesMap : map.getValue().entrySet()) {
                            UnitExpenditure expenditure = salesMap.getValue();
                            updateOrderMap(orderMap, expenditure.getOrder(), expenditure.getLabel());
                        }
                        break;
                    default:
                        UnitExpenditure expenditure = map.getValue().get(map.getKey());
                        String label = expenditure.getLabel();

                        for (String method : expenditure.getEntitys()) {
                            StringBuilder builder = new StringBuilder(label);
                            builder.append(".");
                            builder.append(getReadableFormat(method));
                            if (expenditure.isSplit()) {
                                updateOrderMap(orderMap, expenditure.getOrder(), builder.toString());
                            }
                        }
                        updateOrderMap(orderMap, expenditure.getOrder(), expenditure.getLabel());
                        break;
                }
            }

            for (Map.Entry<Integer, List<String>> entry : orderMap.entrySet()) {
                for (String item : entry.getValue()) {
                    createHeader(sheet, row++, item, style, columnCounter);
                }
            }
        } catch (Exception e) {
            LOG.error("Error While creating Headers", e);
        }
        return sheet;
    }


    private static void updateOrderMap(Map<Integer, List<String>> orderMap, Integer key, String item) {
        if (!orderMap.containsKey(key)) {
            orderMap.put(key, new ArrayList<String>());
        }
        orderMap.get(key).add(item);
    }

    private static String getReadableFormat(String name) {
        return StringUtils.capitalize(StringUtils.join(StringUtils.splitByCharacterTypeCamelCase(name), ' '));
    }

    private static void createHeader(Sheet sheet, int number, String name, CellStyle style, Counter columnCounter) {
        sheet.createRow(number).createCell(columnCounter.getC()).setCellValue(name);
        sheet.getRow(number).getCell(columnCounter.getC()).setCellStyle(style);
    }

    private static CellStyle generateHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        style.setFillForegroundColor(IndexedColors.BLACK.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);
        return style;
    }

    public static void createPnlAggregateDetailSheet(List<PnlRecord> record, Workbook workbook, Boolean flag, int row) {
        Sheet sheet = null;
        if (row == 0) {
            sheet = workbook.createSheet("MTD PNL Details For Month");
        } else {
            sheet = workbook.getSheetAt(0);
        }
        Counter columnCount = new Counter(0);
        Counter rowCount = new Counter(row);
        generateAggregateSheet(workbook, sheet, columnCount, record, flag, row);

        if (!record.isEmpty()) {
            for (PnlRecord detail : record) {
                createAggregateColumn(sheet, rowCount.increment(), detail, flag);
            }
        }
    }


    private static Sheet generateAggregateSheet(Workbook workbook, Sheet sheet, Counter columnCounter, List<PnlRecord> record, Boolean flag, int r) {
        try {
            int row = r;
            int col = 0;
            sheet.setDefaultColumnWidth(20);
            CellStyle style = generateHeaderStyle(workbook);
            List<String> names = !Boolean.TRUE.equals(flag) ? Arrays.asList("HEADERS", "TOTAL BUDGET", "MTD BUDGET", "MTD ACTUAL", "MTD") : Arrays.asList("HEADERS", "FINALIZED");
            Row headerRow = sheet.createRow(row++);
            r++;
            // creating headers
            for (String name : names) {
                Cell cell = headerRow.createCell(col++);
                cell.setCellValue(name);
                cell.setCellStyle(style);
            }

            // creating multiple columns for multiple mtds value
            if (!Boolean.TRUE.equals(flag)) {
                for (int i = 1; i < record.get(0).getMtdValue().size(); i++) {
                    Cell cell = headerRow.createCell(col++);
                    cell.setCellValue(names.get(4));
                    cell.setCellStyle(style);
                }
            }
            row = r;
            for (PnlRecord item : record) {
                createHeader(sheet, row++, item.getKey(), style, columnCounter);
                if (!item.getDrilldowns().isEmpty()) {
                    for (PnlRecord drilldown : item.getDrilldowns()) {
                        createHeader(sheet, row++, drilldown.getKey(), style, columnCounter);
                    }
                }
            }

        } catch (Exception e) {
            LOG.error("Error While creating Headers", e);
        }
        return sheet;
    }

    private static void createAggregateColumn(Sheet sheet, Counter rowCount, PnlRecord record, Boolean flag) {
        int colCount = 1;
        try {
            if (!Boolean.TRUE.equals(flag)) {
                sheet.getRow(rowCount.getC()).createCell(colCount++).setCellValue(record.getBudget() != null ? record.getBudget() : "");
                sheet.getRow(rowCount.getC()).createCell(colCount++).setCellValue(record.getBudgetMtd() != null ? record.getBudgetMtd() : "");
            }
            sheet.getRow(rowCount.getC()).createCell(colCount++).setCellValue(record.getCurrentValue() != null ? record.getCurrentValue() : "");
            if (!Boolean.TRUE.equals(flag)) {
                for (String mtd : record.getMtdValue()) {
                    sheet.getRow(rowCount.getC()).createCell(colCount++).setCellValue(mtd != null ? mtd : "");
                }
            }

            colCount = 1;
            for (PnlRecord item : record.getDrilldowns()) {
                try {
                    createAggregateColumn(sheet, rowCount.increment(), item, flag);
                } catch (Exception e) {
                    LOG.error("Error while creating  drill down column {} at row {}", colCount, rowCount.getC(), e);
                }
            }
        } catch (Exception e) {
            LOG.error("Error while creating column {} at row {}", colCount, rowCount.getC(), e);
        }
    }
}
