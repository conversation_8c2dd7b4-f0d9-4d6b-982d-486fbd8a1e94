package com.stpl.tech.kettle.core.service.impl;

import com.itextpdf.html2pdf.HtmlConverter;
import com.stpl.tech.kettle.core.FeedbackEventStatus;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.FeedbackStatus;
import com.stpl.tech.kettle.core.data.vo.FeedbackEventInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.core.notification.OrderEmailReceipt;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.ReceiptNotification;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.OrderNotificationService;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.OrderManagementDao;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderFeedbackMetadata;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.jms.JMSException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.Objects;

@Service
public class OrderNotificationServiceimpl implements OrderNotificationService {

	private static final Logger LOG = LoggerFactory.getLogger(OrderNotificationServiceimpl.class);

	@Autowired
	private OrderManagementDao dao;

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private FeedbackManagementService feedbackManagementService;

	@Autowired
	private CustomerOfferManagementService offerService;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private FeedbackManagementService feedbackService;

	@Autowired
	private TokenService<FeedbackTokenInfo> tokenService;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void createOrderNotificationData(OrderNotification orderNotification, OrderInfo info)
			throws DataUpdationException {
		dao.createOrderNotificationData(orderNotification, info);
	}

	@Override

	public void generateOrderFeedbackNotification(OrderInfo info, OrderFeedbackMetadata orderFeedbackMetadata) {
		if (!props.getAutomatedNPSSMS()) {
			LOG.info("Skipping send automated NPS sms");
			return;
		}
		boolean isCustomerAvailable = info.getCustomer().isSmsSubscriber() && !info.getCustomer().isBlacklisted()
				&& !info.getCustomer().isDND()
				&& !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(info.getOrder().getCustomerId());
		if (isCustomerAvailable) {
			if ((Objects.isNull(info.getCustomer().getOptWhatsapp())
					|| (Objects.nonNull(info.getCustomer().getOptWhatsapp())
					&& AppConstants.NO.equalsIgnoreCase(info.getCustomer().getOptWhatsapp())))) {
				boolean sendOrderFeedbackNotification = AppUtils
						.sendOrderFeedbackNotification(AppUtils.getCurrentTimestamp());
				if (sendOrderFeedbackNotification) {
					generateOrderFeedbackDetails(FeedbackSource.SMS, info, true, false, orderFeedbackMetadata);
				}
			} else if (Objects.nonNull(info.getCustomer().getOptWhatsapp())
					&& AppConstants.YES.equalsIgnoreCase(info.getCustomer().getOptWhatsapp())) {
				//generateOrderFeedbackDetails(FeedbackSource.WHATS_APP, info, true, true, orderFeedbackMetadata);
				generateOrderFeedbackDetails(FeedbackSource.SMS, info, true, true, orderFeedbackMetadata);
			}
		}
		
		if (orderFeedbackMetadata != null && Objects.nonNull(orderFeedbackMetadata.getFeedbackAndReceiptDetails())) {
			if (!props.getSystemGeneratedNotifications()) {
				info.getOrderNotification().setOrderFeedBackUrl(orderFeedbackMetadata.getFeedbackAndReceiptDetails().getShortCode());
				info.getOrderNotification().setOrderRecieptUrl(orderFeedbackMetadata.getFeedbackAndReceiptDetails().getName());
			}else {
				//TODO Ishman - Send Notification
			}
		}
	}

	@Override
	public OrderFeedbackMetadata generateOrderFeedbackDetails(FeedbackSource source, OrderInfo orderInfo, boolean feedback,
															  boolean generateReceipt,OrderFeedbackMetadata orderFeedbackMetadata) {
		IdCodeName codeName = new IdCodeName();
		if (generateReceipt) {
			String fromEmail = (AppUtils.isDev(props.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
					+ AppConstants.CHAAYOS_RECEIPT;
			CustomerEmailData customerEmailData = offerService.getCustomerEmailData(orderInfo.getCustomer().getId(),
					orderInfo.getOrder().getBrandId());
			ReceiptNotification notification = new ReceiptNotification(new OrderEmailReceipt("webapps/kettle-service",
					orderInfo.getUnit(), orderInfo, AppUtils.getFormattedEmail(fromEmail, props.getRecieptEmail()),
					orderInfo.getCustomer().getEmailId(), props.getBasePath(),
					orderInfo.getCustomer().isEmailVerified(), null, null, props.getBillPromotion(), false,
					customerEmailData), sendEmailToCustomer(orderInfo.getOrder()));
			try {
				createReceiptPdf(notification.body(), orderInfo.getOrder().getGenerateOrderId(), orderInfo.getUnit(),
						codeName);
			} catch (EmailGenerationException e) {
				LOG.error("Exception while order reciept email generation for orderid ::{}",
						orderInfo.getOrder().getOrderId(), e);
			}
		}
		if (feedback) {
			try {
				getOrderFeedBackUrl(source, codeName, orderInfo,orderFeedbackMetadata);
			} catch (Exception e) {
				LOG.info("Exception Faced While Generating Order Feedback URL::{}", orderInfo.getOrder().getOrderId(),
						e);
			}
		}
		if(generateReceipt && Objects.nonNull(codeName.getName())){
			if (orderFeedbackMetadata.getFeedbackAndReceiptDetails() != null) {
				orderFeedbackMetadata.getFeedbackAndReceiptDetails().setName(codeName.getName());
			} else {
				orderFeedbackMetadata.setFeedbackAndReceiptDetails(codeName);
			}
		}else{
			orderFeedbackMetadata.setFeedbackAndReceiptDetails(codeName);
		}
		return orderFeedbackMetadata;
	}

	private void createReceiptPdf(String receiptContent, String orderId, Unit unit, IdCodeName codeName) {
		try {
			if (StringUtils.isNotBlank(receiptContent) && StringUtils.isNotBlank(orderId)) {
				String kioskPath = props.getBasePath() + "/" + unit.getId() + "/whatsapp/orders";
				File kioskFolder = new File(kioskPath);
				if (!kioskFolder.exists()) {
					kioskFolder.mkdirs();
				}
				String fileName = "orderReceipt-" + orderId + ".pdf";
				String receiptPath = kioskPath + "/" + fileName;
				File pdfFile = new File(receiptPath);
				if (!pdfFile.exists()) {
					pdfFile.createNewFile();
				}
				try (OutputStream outputStream = new FileOutputStream(pdfFile)) {
					HtmlConverter.convertToPdf(receiptContent, outputStream);
					outputStream.flush();

					String baseDir = "whatsapp/" + unit.getId() + "/orders";
					try {
						FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), baseDir, pdfFile, true,
								7);
						if (s3File != null) {
//                            codeName.setName(SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(s3File.getUrl()).getUrl());
							codeName.setName(SolsInfiniWebServiceClient.getTransactionalClient()
									.getShortUrl(props.getOrderReceipt() + "/" + unit.getId() + "/orders/" + fileName)
									.getUrl());
						} else {
							LOG.error("Error uploading report to S3.");
						}
						pdfFile.delete();
					} catch (Exception e) {
						LOG.error("Encountered error while uploading report to S3", e);
						if(pdfFile.exists()) {
							pdfFile.delete();
						}
					}

				} catch (IOException e) {
					/*
					 * String errorMsg = "Unable to create receipt pdf "; LOG.error(errorMsg, e);
					 */
					LOG.error("Exception Occurred while converting html to pdf");
					if(pdfFile.exists()) {
						pdfFile.delete();
					}
				}
			}
		} catch (Exception ex) {
			LOG.error("Exception Occurred while creating PDF of Receipt");
		}
	}

	private void getOrderFeedBackUrl(FeedbackSource source, IdCodeName codeName, OrderInfo orderInfo, OrderFeedbackMetadata orderFeedbackMetadata) {
		FeedbackEventInfo event = feedbackManagementService.getPendingNPSForCustomer(source,
				orderInfo.getOrder().getOrderId(), orderInfo.getOrder().getCustomerId());
		if(Objects.nonNull(event)){
			try {
				String unitName = masterDataCache.getUnitBasicDetail(event.getUnitId()).getName();
				FeedbackTokenInfo token = new FeedbackTokenInfo(event.getContactNumber(), unitName, event.getCustomerName(),
						event.getOrderId(), event.getOrderSource(), event.getEventSource(), event.getFeedbackId(),
						event.getFeedbackEventId());
				String jwtToken = tokenService.createToken(token, -1L);
				String feedbackUrl = null;
				ShortUrlData shortUrl = null;
				boolean result =false;
			if (props.getOrderFeedbackType().equals("internal")) {
				feedbackUrl = event.getBrand().getInternalOrderFeedbackUrl();
			} else {
				feedbackUrl = AppConstants.COD.equals(event.getOrderSource()) ?
				// feedback.endpoint.nps.delivery
						event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointNPSDelivery()
						// feedback.endpoint.nps.cafe
						: event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointNPSCafe();
			}
			URIBuilder builder = new URIBuilder(feedbackUrl);
			builder.addParameter("name", event.getCustomerName());
			builder.addParameter("unit", unitName);
			builder.addParameter("token", jwtToken);
			SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient
					.getTransactionalClient(event.getBrand());
			shortUrl = smsWebServiceClient.getShortUrl(builder.build().toURL().toString());
			event.setEventLongUrl(feedbackUrl);
			if(Objects.nonNull(shortUrl)){
				event.setEventShortUrl(shortUrl.getUrl());
				codeName.setShortCode(shortUrl.getUrl());
			}
			feedbackUrl = builder.build().toURL().toString();
			codeName.setCode(feedbackUrl);
			getOrderFeedbackMetadata(event,codeName,feedbackUrl,orderFeedbackMetadata);
			/*if(props.getSystemGeneratedNotifications()){
				result = sendNPSMessage(props.getAutomatedNPSSMS(), event.getContactNumber(),
						event, smsWebServiceClient);
				feedbackUrl = builder.build().toURL().toString();
				*//*if (result) {
					Date updateTime = feedbackService.updateFeedbackEventStatus(event.getFeedbackId(),
							event.getFeedbackEventId(), shortUrl, feedbackUrl, FeedbackEventStatus.NOTIFIED,
							FeedbackStatus.NOTIFIED);
					feedbackService.updateLastNPSTime(updateTime, event.getCustomerId());
				} else {
					feedbackService.updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(),
							shortUrl, feedbackUrl, FeedbackEventStatus.FAILED, FeedbackStatus.FAILED);
				}*//*
				updateFeedbackEventStatus(orderFeedbackMetadata,result);
			}*/
			} catch (IOException | URISyntaxException e) {
				LOG.error("Error while generating the feedback url to " + event.getContactNumber(), e);
			}
		}
	}

	private void getOrderFeedbackMetadata(FeedbackEventInfo event, IdCodeName codeName, String feedbackUrl, OrderFeedbackMetadata orderFeedbackMetadata){
		if(Objects.nonNull(event)&& Objects.nonNull(codeName)){
			orderFeedbackMetadata.setFeedbackId(event.getFeedbackId());
			orderFeedbackMetadata.setFeedbackEventId(event.getFeedbackEventId());
			orderFeedbackMetadata.setFeedbackAndReceiptDetails(codeName);
			orderFeedbackMetadata.setFeedbackUrl(feedbackUrl);
			orderFeedbackMetadata.setCustomerId(event.getCustomerId());
		}
	}

	private boolean sendNPSMessage(boolean sendNotification, String contactNumber, FeedbackEventInfo token,
								   SMSWebServiceClient smsWebServiceClient) {
		try {
			if (token.getType().equals(FeedbackEventType.NPS)) {
				String message = CustomerSMSNotificationType.NPS_MESSAGE.getMessage(token);
				return notificationService.sendNotification(CustomerSMSNotificationType.NPS_MESSAGE.name(),
						message, contactNumber, smsWebServiceClient, sendNotification,null);
			} else if (token.getType().equals(FeedbackEventType.NPS_OFFER)) {
				String message = CustomerSMSNotificationType.NPS_OFFER_MESSAGE.getMessage(token);
				return notificationService.sendNotification(CustomerSMSNotificationType.NPS_OFFER_MESSAGE.name(),
						message, contactNumber, smsWebServiceClient, sendNotification,null);
			}else if(token.getType().equals(FeedbackEventType.ORDER_FEEDBACK)){
				String message = CustomerSMSNotificationType.ORDER_FEEDBACK_MESSAGE.getMessage(token);
				return notificationService.sendNotification(CustomerSMSNotificationType.ORDER_FEEDBACK_MESSAGE.name(),
						message, contactNumber, smsWebServiceClient, sendNotification,null);
			}
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the NPS message to " + contactNumber, e);
		}

		return false;
	}

	private boolean sendEmailToCustomer(Order order) {
		return order.getSubscriptionDetail() == null
				|| (order.getSubscriptionDetail() != null && order.getSubscriptionDetail().isEmailNotification());
	}

	@Override
	public void updateFeedbackEventStatus(OrderFeedbackMetadata orderFeedbackMetadata,
			boolean isOrderNotificationSent) {
		if (Objects.nonNull(orderFeedbackMetadata) && Objects.nonNull(orderFeedbackMetadata.getFeedbackId())
				&& Objects.nonNull(orderFeedbackMetadata.getFeedbackEventId())) {
			Date updateTime = null;
			if (props.sendFeedBackSMSFromClevertap()) {
				if (isOrderNotificationSent) {
					updateTime = feedbackService.updateFeedbackEventStatus(orderFeedbackMetadata.getFeedbackId(),
							orderFeedbackMetadata.getFeedbackEventId(), null, orderFeedbackMetadata.getFeedbackUrl(),
							FeedbackEventStatus.NOTIFIED, FeedbackStatus.NOTIFIED);
					feedbackService.updateLastNPSTime(updateTime, orderFeedbackMetadata.getCustomerId());
				} else {
					feedbackService.updateFeedbackEventStatus(orderFeedbackMetadata.getFeedbackId(),
							orderFeedbackMetadata.getFeedbackEventId(), null, orderFeedbackMetadata.getFeedbackUrl(),
							FeedbackEventStatus.FAILED, FeedbackStatus.FAILED);
				}
			}
		}
	}
}
