/**
 * 
 */
package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.domain.model.UnitCategory;

/**
 * <AUTHOR>
 *
 */
public class StatusData {

	private int orderId;
	private UnitCategory source;
	private OrderStatus status;

	public StatusData() {

	}

	public StatusData(int orderId, UnitCategory type, OrderStatus status) {
		super();
		this.orderId = orderId;
		this.source = type;
		this.status = status;
	}

	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	public OrderStatus getStatus() {
		return status;
	}

	public void setStatus(OrderStatus status) {
		this.status = status;
	}

	public UnitCategory getSource() {
		return source;
	}

	public void setSource(UnitCategory type) {
		this.source = type;
	}

}
