/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * OrderSettlement generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ORDER_RE_PRINT_DETAIL")
public class OrderRePrintDetail implements java.io.Serializable {

	private Integer orderPrintId;
	private OrderDetail orderDetail;
	private String printReason;
	private int generatedBy;
	private int approvedBy;
	private Date reprintTime;

	public OrderRePrintDetail() {
	}

	public OrderRePrintDetail(OrderDetail orderDetail, String printReason) {
		this.orderDetail = orderDetail;
		this.printReason = printReason;
	}

	public OrderRePrintDetail(OrderDetail orderDetail, String printReason, int generatedBy, int approvedBy,
			Date reprintTime) {
		this.orderDetail = orderDetail;
		this.printReason = printReason;
		this.generatedBy = generatedBy;
		this.approvedBy = approvedBy;
		this.reprintTime = reprintTime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "ORDER_PRINT_ID", unique = true, nullable = false)
	public Integer getOrderPrintId() {
		return this.orderPrintId;
	}

	public void setOrderPrintId(Integer orderPrintId) {
		this.orderPrintId = orderPrintId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	public OrderDetail getOrderDetail() {
		return this.orderDetail;
	}

	public void setOrderDetail(OrderDetail orderDetail) {
		this.orderDetail = orderDetail;
	}

	@Column(name = "PRINT_REASON", nullable = false)
	public String getPrintReason() {
		return printReason;
	}

	public void setPrintReason(String printReason) {
		this.printReason = printReason;
	}

	@Column(name = "GENERATED_BY", nullable = false)
	public int getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(int employeeId) {
		this.generatedBy = employeeId;
	}

	@Column(name = "APPROVED_BY", nullable = false)
	public int getApprovedBy() {
		return approvedBy;
	}

	public void setApprovedBy(int approvedBy) {
		this.approvedBy = approvedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REPRINT_TIME", nullable = false, length = 19)
	public Date getReprintTime() {
		return reprintTime;
	}

	public void setReprintTime(Date billStartTime) {
		this.reprintTime = billStartTime;
	}

}
