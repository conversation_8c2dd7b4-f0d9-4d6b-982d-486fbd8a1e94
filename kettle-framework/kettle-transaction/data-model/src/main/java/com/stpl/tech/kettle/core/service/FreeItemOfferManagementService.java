/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import java.util.Date;

import com.stpl.tech.kettle.domain.model.Order;

public interface FreeItemOfferManagementService {

	/**
	 * @param order
	 * @return
	 */
	Order applyFreeItemOffer(Date businessDate, boolean newCustomer, Order order);

	/**
	 * @return
	 */
	boolean hasFreeItemOffer(Date businessDate);
	
	int freeItemOfferProductId(Date businessDate);

	/**
	 * @return 
	 * 
	 */
	boolean refreshCache();


}
