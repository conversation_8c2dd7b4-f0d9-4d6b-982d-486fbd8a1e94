/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.notification.SubscriptionInfo;
import com.stpl.tech.kettle.data.model.SubscriptionEventDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Subscription;
import com.stpl.tech.kettle.domain.model.SubscriptionEvent;
import com.stpl.tech.kettle.domain.model.SubscriptionEventStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionStatusEvents;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;

import java.util.Date;
import java.util.List;

public interface SubscriptionManagementService {

	/**
	 * Create subscription with the given order details
	 *
	 * @param order
	 * @return
	 * @throws DataUpdationException
	 * @throws DataNotFoundException
	 */
	public String createSubscription(Order order) throws DataUpdationException, DataNotFoundException;

	public Subscription updateSubscription(Subscription subscription);

	public Order getOrder(int subscriptionId) throws DataNotFoundException;

	public List<Order> getSubscriptions(String contactNumber) throws DataNotFoundException;

	public List<SubscriptionEvent> getSubscriptionOrders(String contactNumber) throws DataNotFoundException;

	public List<SubscriptionEvent> getSubscriptionOrders(int unitId) throws DataNotFoundException;

	public List<SubscriptionEventDetail> getOrdersToBeCreated(Date currentTimestamp);

	public List<SubscriptionEventDetail> createAllSubscriptionsForToday(int subscriptionId);

	public boolean updateEventStatus(Integer subscriptionEventDetailId, Integer orderId,
			SubscriptionEventStatus status);

	public SubscriptionEventDetail cloneEventStatus(Integer cloneEventDetailId, SubscriptionEventStatus status);

	public void putSubscriptionsOnHoldForToday();

	public void takeSubscriptionsOffHoldForToday();

	public void cancelSubscriptionsForToday();

	public List<SubscriptionEventDetail> createAllSubscriptionsForToday();

	public SubscriptionInfo getSubscriptionInfo(int subscriptionId) throws DataNotFoundException;

	public boolean holdOrCancelSubscription(int subscriptionId, SubscriptionStatus status, boolean effectiveImmediate,
			Date startDate, Date endDate, int generatedBy, String reasonText);

	public boolean cancelHold(int subscriptionEventDetailId);

	public boolean updateHold(int subscriptionEventDetailId, Date endDate, boolean withImmediateEffect);

	boolean updateHold(int subscriptionEventDetailId, Date startDate, Date endDate, boolean withImmediateEffect,
			String reason);

	public List<SubscriptionStatusEvents> getStatusEventsForSubscription(Integer subscriptionId)
			throws DataNotFoundException;

	public SubscriptionEvent updateEventData(SubscriptionEvent event)
			throws DataUpdationException, DataNotFoundException;

	public boolean updateCancel(int subscriptionEventStatusId, Date startDate, String reason);

    void sendReminderForNotUsingSubscriptionNthDay(int nthDay, String type);

    void sendChaayosSubscriptionExpiryReminder(Integer nThDay);
}
