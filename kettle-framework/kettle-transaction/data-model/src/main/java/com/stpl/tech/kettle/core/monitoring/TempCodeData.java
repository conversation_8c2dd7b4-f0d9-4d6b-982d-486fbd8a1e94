/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.monitoring;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

public class TempCodeData implements Delayed {

	private String code;
	private int accessCodeId;
	private long startTime;

	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}

	public TempCodeData() {
		super();
	}

	public TempCodeData(String code, int accessCodeId, long delay) {
		super();
		this.code = code;
		this.accessCodeId = accessCodeId;
		this.startTime = System.currentTimeMillis() + delay;
	}

	public int compareTo(Delayed o) {
		if (this.startTime < ((TempCodeData) o).startTime) {
			return -1;
		}
		if (this.startTime > ((TempCodeData) o).startTime) {
			return 1;
		}
		return 0;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public long getDelay(TimeUnit unit) {
		long diff = startTime - System.currentTimeMillis();
		return unit.convert(diff, TimeUnit.MILLISECONDS);
	}

	public long getStartTime() {
		return startTime;
	}

	public int getAccessCodeId() {
		return accessCodeId;
	}

	public void setAccessCodeId(int accessCodeId) {
		this.accessCodeId = accessCodeId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + accessCodeId;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		TempCodeData other = (TempCodeData) obj;
		if (accessCodeId != other.accessCodeId)
			return false;
		return true;
	}

}
