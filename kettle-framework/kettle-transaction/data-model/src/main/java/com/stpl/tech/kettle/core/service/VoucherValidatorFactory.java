package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.kettle.core.service.impl.DreamFolksVoucherValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class VoucherValidatorFactory {

    @Autowired
    private DreamFolksVoucherValidator dreamFolksVoucherValidator;

    @Autowired
    private EnvironmentProperties props;

    public VoucherValidator getValidator(String couponCode) {
        if (props.getPartnerPaymentOfferCode().equalsIgnoreCase(couponCode)) {
            return dreamFolksVoucherValidator;
        }
        throw new IllegalArgumentException("No validator for coupon code: " + couponCode);
    }
} 