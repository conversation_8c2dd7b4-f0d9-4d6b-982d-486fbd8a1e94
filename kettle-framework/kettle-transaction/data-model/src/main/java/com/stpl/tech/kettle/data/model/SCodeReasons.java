package com.stpl.tech.kettle.data.model;

import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;


public enum SCodeReasons {
    ERROR_NO_WATER(112),
    ERROR_NO_MILK(113),
     ERROR_FATAL_POWER_OUTAGE (116),
     ERROR_STEEPED_WINDOW_TIME_ELASPED (118),
    ERROR_NO_PATTI_NO_SUGAR_ADDED (119),
     ACTION_CHAI_ABOUT_TO_COMPLETE (-233),
     REFILLING_PAN_UP (-235),
     ACTION_LEVEL_SENSOR_CLEAN_ALERT (-234),
     ERROR_NEED_BIG_PAN (-232),
     ERROR_NEED_SMALL_PAN (-231),
     FATAL_MARGIN_CODE (100),
     NO_STATUS_CODE (-230),
    LEVEL_SENSOR_NOT_CLEANED(134),
    LEVEL_SENSOR_NOT_CLEANED_OR_DISCONNECTED(133),
    QUANTITY_NOT_WITHIN_EXPECTED_RANGE(122),
    QUANTITY_NOT_WITHIN_EXPECTED_RANGES(123);

    private final int code;

    SCodeReasons(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static String getNameBySCode(int code) {
        for (SCodeReasons reasons : SCodeReasons.values()) {
            if (reasons.getCode() == code) {
                return reasons.name();
            }
        }
        return null;
    }

}
