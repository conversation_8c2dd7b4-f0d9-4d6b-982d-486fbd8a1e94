package com.stpl.tech.kettle.core.config;

import lombok.extern.log4j.Log4j2;
import org.drools.decisiontable.DecisionTableProviderImpl;
import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.KieModule;
import org.kie.api.io.Resource;
import org.kie.api.runtime.KieContainer;
import org.kie.internal.io.ResourceFactory;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;


@Configuration("droolsConfig")
@Log4j2
public class DroolsConfig {

    private static final String OFFER_DECISION_DROOL_PATH= "/drools/offer_decision/";
    private static final String OFFER_DECISION_DROOL_FILE= "offer_decision.xls";
    private static final String RECOM_OFFER_DECISION_PATH= "/drools/recom_offer_decision/";
    private static final String RECOM_OFFER_DECISION_FILE= "recom_offer_decision.xls";
    private static final String WALLET_RECOMMENDATION_PATH= "/drools/wallet_recommendation/";
    private static final String SUPERU_PATH= "/drools/superu_parameters/";
    private static final String SUPERU_FILE= "superu_parameters.xls";
    private static final String WALLET_RECOMMENDATION_FILE= "wallet_recommendation.xls";
    private static final KieServices kieServices = KieServices.Factory.get();
    private Map<String, KieContainer> kieContainerForOfferDecision = new ConcurrentHashMap<>();
    private Map<String, KieContainer> kieContainerForRecomOfferDecision = new ConcurrentHashMap<>();
    private Map<String, KieContainer> kieContainerForWalletRecommendation = new ConcurrentHashMap<>();

    private Map<String, KieContainer> kieContainerForSuperU = new ConcurrentHashMap<>();

    public KieContainer getKieContainerForOfferDecision(String version){
        return kieContainerForOfferDecision.get(Objects.nonNull(version) ? version : "default");
    }

    public KieContainer getKieContainerForRecomOfferDecision(String version) {
        return kieContainerForRecomOfferDecision.get(Objects.nonNull(version) ? version : "default");
    }

    public KieContainer getKieContainerForWalletRecommendation(String version){
        return this.kieContainerForWalletRecommendation.get(Objects.nonNull(version) ? version : "default");
    }

    public KieContainer getKieContainerForSuperU(String version){
        return this.kieContainerForSuperU.get(Objects.nonNull(version) ? version : "default");
    }

    public void initDroolConfigForOfferDecision(String version){
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",OFFER_DECISION_DROOL_FILE,version);
                initializeOfferDecisionDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeOfferDecisionDrool("default");
        }
    }

    public void initializeOfferDecisionDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + OFFER_DECISION_DROOL_PATH + version + "/";
        String filePath = baseDir + OFFER_DECISION_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForOfferDecision.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }
    public void initDroolConfigForRecomOfferDecision(String version){
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",RECOM_OFFER_DECISION_FILE,version);
                initializeRecomOfferDecisionDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeRecomOfferDecisionDrool("default");
        }
    }

    public void initializeRecomOfferDecisionDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + RECOM_OFFER_DECISION_PATH + version + "/";
        String filePath = baseDir + RECOM_OFFER_DECISION_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForRecomOfferDecision.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }

    public void initWalletRecommendationDroolConfig(String version){
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",WALLET_RECOMMENDATION_FILE,version);
                initializeWalletRecomDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeWalletRecomDrool("default");
        }
    }

    public void initializeWalletRecomDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + WALLET_RECOMMENDATION_PATH + version + "/";
        String filePath = baseDir + WALLET_RECOMMENDATION_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForWalletRecommendation.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }

    public void initSuperUDroolConfig(String version){
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",SUPERU_FILE,version);
                initializeSuperUDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeSuperUDrool("default");
        }
    }

    public void initializeSuperUDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + SUPERU_PATH + version + "/";
        String filePath = baseDir + SUPERU_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForSuperU.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }
}
