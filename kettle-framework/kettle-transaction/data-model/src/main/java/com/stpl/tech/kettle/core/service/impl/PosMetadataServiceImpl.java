/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.ReportStatus;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.PosMetadataDao;
import com.stpl.tech.kettle.data.model.CreditAccountDetail;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetail;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetailDefinition;
import com.stpl.tech.kettle.data.model.CrmPathDefinition;
import com.stpl.tech.kettle.data.model.CrmScreenResponse;
import com.stpl.tech.kettle.data.model.ItemConsumptionEstimate;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.data.model.PullDetailReasons;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.data.model.UnitExpenditureAggregateDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.data.model.UnitPullDetail;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.domain.model.GoalsDataClearRequest;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.domain.model.UnitPartnerStatusVO;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimate;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimateRequest;
import com.stpl.tech.spring.exception.FileArchiveServiceException;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.slack.Slack;
import com.stpl.tech.util.endpoint.Endpoints;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.Part;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class, transactionManager = "TransactionDataSourceTM")
public class PosMetadataServiceImpl implements PosMetadataService  {

	private static final Logger LOG = LoggerFactory.getLogger(PosMetadataServiceImpl.class);
	public static final String INVENTORY_DOWN = "Inventory Down";
	public static final String SEND_KNOCK_NOTIFICATION = "send-knock-notification";

	@Autowired
	private PosMetadataDao dao;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Autowired
   private  EnvironmentProperties environmentProperties;

    @Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public TransactionMetadata getTransactionData() throws DataNotFoundException {
		return dao.getTransactionData();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public TransactionMetadata getTransactionData(UnitCategory category) throws DataNotFoundException {
		return dao.getTransactionData(category);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.PosMetadataService#getAllChannelPartner ()
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ChannelPartnerDetail> getAllChannelPartner(Date businessDate) throws DataNotFoundException {
		return new ArrayList<>(masterDataCache.getAllChannelPartners());
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.PosMetadataService#closeDay(int, int,
	 * java.lang.String, int)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public int closeDay(int unitId, int employeeId, String comment, int startOrderId, int lastOrderId, Date currentDate)
			throws DataUpdationException {
		return dao.closeDay(unitId, employeeId, comment, startOrderId, lastOrderId, currentDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void callProductEsimateUpdateProc(int unitId, Date currentDate) throws DataUpdationException {
		dao.callProductEsimateUpdateProc(unitId, currentDate);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.PosMetadataService#closeDay(int, int,
	 * java.lang.String, int)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void markDayCloseAsCancelled(int dayClosureId) {
		dao.markDayCloseAsCancelled(dayClosureId);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.PosMetadataService#isDayClosed(int)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean isDayClosed(int unitId, Date date) throws DataUpdationException {
		return dao.isDayClosed(unitId, date);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.PosMetadataService#
	 * getComplimentaryCodes()
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public ListData getComplimentaryCodes(boolean getAll) throws DataNotFoundException {
		return dao.getComplimentaryCodes(getAll);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<IdCodeName> getAllDeliveryPartner() throws DataNotFoundException {
		return dao.getAllDeliveryPartner();
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitClosureDetails> getClosureFromBusinessDate(Date businessDate) {
		return dao.getClosureFromBusinessDate(businessDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitClosureDetails> getClosureForBusinessDate(Date businessDate) {
		return dao.getClosureForBusinessDate(businessDate);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitClosureDetails> getClosureForBusinessDate(Date businessDate, Set<Integer> units) {
		return dao.getClosureForBusinessDate(businessDate, units);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateDayCloseDate(int closureId, Date businessDate) throws DataUpdationException {
		return dao.updateDayCloseDate(closureId, businessDate);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateSalesData(Date businessDate) {
		dao.updateSalesData(businessDate);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateDailySalesData(Date businessDate) {
		dao.updateDailySalesData(businessDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitToDeliveryPartnerMappings> getAllDeliveryPartnerMappings() {
		return dao.getAllDeliveryPartnerMappings();
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public CreditAccountDetail addCreditAccount(CreditAccountDetail detail) {
		return dao.addCreditAccount(detail);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public CreditAccountDetail updateCreditAccount(CreditAccountDetail detail) {
		return dao.updateCreditAccount(detail);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CreditAccountDetail> getAllCreditAccounts(String status) {
		return dao.getAllCreditAccounts(status);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateConsumptionEstimates(Date businessDate) {
		dao.updateConsumptionEstimates(businessDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ItemConsumptionEstimate> getAllActiveItemConsumptionEstimate() {
		return dao.getAllActiveItemConsumptionEstimate();
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitClosure getUnitsClosure(int unitId, Date date) {
		return dao.getUnitsClosure(unitId, date);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<PartnerAttributes> addDSRConfig(List<PartnerAttributes> partnerData) {
		for (PartnerAttributes l : partnerData) {
			dao.add(l);
		}
		return new ArrayList<>();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public PartnerAttributes updatePartnerAttributes(PartnerAttributes partner)
			throws DataNotFoundException, DataUpdationException {
		return dao.updatePartnerAttributes(partner);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public PartnerAttributes addPartnerAttributes(PartnerAttributes partner)
			throws DataNotFoundException, DataUpdationException {
		PartnerAttributes partnerAttributes = dao.find(PartnerAttributes.class, partner.getId());
		if(partnerAttributes == null){
			partner.setPartnerType("SALES_REPORT");
			return dao.add(partner);
		}else {
			partnerAttributes.setPartnerId(partner.getPartnerId());
			partnerAttributes.setMappingType(partner.getMappingType());
			partnerAttributes.setMappingValue(partner.getMappingValue());
			partnerAttributes.setPartnerType("SALES_REPORT");
			return dao.update(partnerAttributes);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitDetail> getDSRConfigpartnerId() throws DataNotFoundException {
		return dao.getDSRConfigpartnerId();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PartnerAttributes> getDSRConfig(Integer partnerId) throws DataNotFoundException {
		return dao.getDSRConfig(partnerId);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.PosMetadataService#updateReportGenerated(
	 * int)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateReportStatus(int closureId, ReportStatus status) {
		return dao.updateReportStatus(closureId, status);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitClosureDetails> getClosures(Date businessDate, ReportStatus status) {
		return dao.getClosures(businessDate, status);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateSuggestiveConsumptionEstimates(Date businessDate, int unitId) {
		dao.updateSuggestiveConsumptionEstimates(businessDate, unitId);

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateUptsForPreviousDate(Date businessDate, int unitId) {
		dao.updateUptsForPreviousDate(businessDate, unitId);

	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public boolean setCrmAppScreenDetail(CrmAppScreenDetailDefinition detail) {
		try {
			for (Integer unit : detail.getUnitId()) {
				CrmAppScreenDetail crmAppScreenDetail = new CrmAppScreenDetail();
				crmAppScreenDetail.setScreenType(detail.getScreenType());
				crmAppScreenDetail.setCity(masterDataCache.getUnitBasicDetail(unit).getRegion());
				crmAppScreenDetail.setImagePath(detail.getImagePath());
				crmAppScreenDetail.setContentType(detail.getContentType());
				crmAppScreenDetail.setCityType(crmAppScreenDetail.getCity() + "_" + detail.getScreenType());
				crmAppScreenDetail.setUpdatedOn(AppUtils.getCurrentTimestamp());
				crmAppScreenDetail.setUpdatedBy(detail.getUpdatedBy());
				crmAppScreenDetail.setUnitId(unit);
				crmAppScreenDetail.setUnitName(masterDataCache.getUnitBasicDetail(unit).getName());
				crmAppScreenDetail.setStatus(detail.getStatus());
				dao.markPreviousCrmScreenInactive(crmAppScreenDetail);
				dao.add(crmAppScreenDetail);
			}
		} catch (Exception e) {
			LOG.info("Exception Caught :", e);
			return false;
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
	public FileDetail saveOfferImage(MimeType mimeType, String couponCode, String imageType, MultipartFile file,
									 String s3OfferBucket, String hostURL) {

		try {
			String baseDir = "master-service/crmapp";
			String fileName = file.getOriginalFilename();
//            String extension = FilenameUtils.getExtension(fileName);

			fileName = fileName.replaceAll(" ", "_").toLowerCase();

			LOG.info(":::::: Request to upload New App Offer Image ::::::");

			FileDetail s3File = fileArchiveService.saveFileToS3(s3OfferBucket, baseDir, fileName, file, true);
			return s3File;
		} catch (FileArchiveServiceException e) {
			LOG.error("Encountered error while uploading Offer Image to S3", e);
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
	public FileDetail saveRevenueCertificate(MimeType mimeType, String couponCode, String imageType, MultipartFile file,
											 String s3OfferBucket, String hostURL) {

		try {
			String baseDir = "revenue-certificate";
			String fileName = file.getOriginalFilename();
//            String extension = FilenameUtils.getExtension(fileName);

			fileName = fileName.replaceAll(" ", "_").toLowerCase();

			LOG.info(":::::: Request to upload Revenue Certificate ::::::");

			FileDetail s3File = fileArchiveService.saveFileToS3(s3OfferBucket, baseDir, fileName, file, true);
			return s3File;
		} catch (FileArchiveServiceException e) {
			LOG.error("Encountered error while uploading to S3", e);
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CrmScreenResponse getCrmScreenUrl(String data) throws DataNotFoundException {
		List<CrmAppScreenDetail> detail = dao.getCrmScreenUrl(data);
		if (detail == null) {
			data = "ALL";
			detail = dao.getCrmScreenUrl(data);
		}

		CrmScreenResponse map = new CrmScreenResponse();
		map.setBusinessDate(AppUtils.getBusinessDate());
		map.setCity(data);
		Map<String, CrmPathDefinition> objectMap = new HashMap<>();

		for (CrmAppScreenDetail screenDetail : detail) {
			CrmPathDefinition name = new CrmPathDefinition(screenDetail.getImagePath(), screenDetail.getContentType());
			objectMap.put(screenDetail.getCityType(), name);
		}
		map.setData(objectMap);
		return map;
	}

	@Override
	public List<IdCodeName> getPnLMap(Integer unitId) throws DataNotFoundException {
		UnitExpenditureAggregateDetail aggregateDetails = dao.getPnLMap(unitId);
		List<IdCodeName> resultList = new ArrayList<>();
		if (aggregateDetails != null) {
			resultList.add(new IdCodeName("MTD Wastage %", AppUtils.multiply(
					AppUtils.divideWithScale10(aggregateDetails.getWastageExpiry(), aggregateDetails.getRevenue()),
					new BigDecimal(100)).toString()));
			resultList.add(new IdCodeName("MTD Consumable  %",
					AppUtils.multiply(
							AppUtils.divideWithScale10(aggregateDetails.getConsumable(), aggregateDetails.getRevenue()),
							new BigDecimal(100)).toString()));
		}
		return resultList;
	}

	@Override
	public List<UnitExpenditureDetail> getAllPnlListForClosureId(Integer dayClosureId) {
		return dao.getAllPnlListForClosureId(dayClosureId);
	}

	@Override
	public boolean resetMeterReading(Integer unitId) {
		return dao.resetMeterReading(unitId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Map<String, Map<String, String>> getCrmScreenUrlForUnit(Integer unitId) {
		List<CrmAppScreenDetail> list = dao.getCrmScreenUrlForUnit(unitId);
		Map<String, Map<String, String>> map = new HashMap<>();
		if (list != null && !list.isEmpty()) {
			for (CrmAppScreenDetail screen : list) {
				if (!map.containsKey(screen.getContentType())) {
					map.put(screen.getContentType(), new HashMap<>());
				}
				map.get(screen.getContentType()).put(screen.getScreenType(), screen.getImagePath());
			}

		}
		return map;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Map<String, Map<String, String>> getCrmScreenUrlForUnitV1(Integer unitId) {
		List<CrmAppScreenDetail> list = dao.getCrmScreenUrlForUnitV1(unitId);
		Map<String, Map<String, String>> map = new HashMap<>();
		if (list != null && !list.isEmpty()) {
			for (CrmAppScreenDetail screen : list) {
				if (!map.containsKey(screen.getContentType())) {
					map.put(screen.getContentType(), new HashMap<>());
				}
				map.get(screen.getContentType()).put(screen.getScreenType(), screen.getImagePath());
			}
		}
		return map;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Map<String, Map<String, List<String>>> getCrmScreenUrlForUnitV2(Integer unitId) {
		List<CrmAppScreenDetail> list = dao.getCrmScreenUrlForUnitV1(unitId);
		Map<String, Map<String, List<String>>> map = new HashMap<>();
		if (list != null && !list.isEmpty()) {
			for (CrmAppScreenDetail screen : list) {
				if (!map.containsKey(screen.getContentType())) {
					map.put(screen.getContentType(), new HashMap<>());
				}
				List<String> imagePathlist = new ArrayList<>();
				imagePathlist.add(screen.getImagePath());
				map.get(screen.getContentType()).put(screen.getScreenType(), imagePathlist);
			}
			if (environmentProperties.getShowOfferFlag()) {
				LOG.info("Adding shopify url to Result with offer Flag: {} and offerUrl: {}", environmentProperties.getShowOfferFlag(), environmentProperties.getOfferBannerUrl());
				Map<String, List<String>> offerMap = new HashMap<>();
				List<String> imageList = new ArrayList<>();
				for (String key: map.keySet()) {
					if (Objects.nonNull(map.get(key)) && Objects.nonNull(map.get(key).get("IDLE_SCREEN"))) {
                        if (!key.equalsIgnoreCase("VIDEO")) {
                            for (String imagePath: map.get(key).get("IDLE_SCREEN")) {
                                imageList.add(imagePath);
                            }
                        }
					}
				}
				imageList.add(environmentProperties.getOfferBannerUrl());
				offerMap.put("IDLE_SCREEN", imageList);
				map.put("IMAGELIST", offerMap);
			}
		}
		return map;
	}

	@Override
	public List<CrmAppScreenDetail> getCrmAppScreenDetail(String region) {
		return dao.getCrmAppScreenDetail(region);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean cloneCrmAppScreenDetail(IdCodeName detail) {
		try {
			return dao.cloneCrmAppScreenDetail(detail);
		} catch (Exception e) {
			LOG.info("Exception Caught while Cloning CrmAppScreenDetail ::: ", e);
			return false;
		}

	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean sendSlackNotificationForInventoryDown(IdCodeName request) {
        UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(request.getId());
        String message = "Inventory service is down for UNIT:" + request.getId() +
            " But order has been punched for customer id: " + request.getCode() + " without inventory at " + AppUtils.getCurrentTimestamp() + ", please get this sorted ASAP";
        knockNotifyToManagerId(unit,message);
		slackToManagerId(unit, message);
        return true;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Map<Integer, List<Date>> getUnitClosingTimeMap(Date previousDate){
		return   dao.getUnitClosingTimeMap(previousDate);
	}

	@Override
	public void createPreviousDateStockOutEntry(Date previousDate){
		dao.createPreviousDateStockOutEntry(previousDate);
	}

	private void knockNotifyToManagerId(UnitBasicDetail ubd, String message) {
		Map<String,String> params = new HashMap<>();
		params.put("message",message);
		params.put("title", INVENTORY_DOWN);
		try {
			if (ubd.getUnitManagerId() != null) {
				params.put("userId", ubd.getUnitManagerId() + "");
				WebServiceHelper.postRequestWithParamAndHeader(environmentProperties.getKnockBaseUrl() + AppConstants.KNOCK_NOTIFICATION_ENDPOINT + SEND_KNOCK_NOTIFICATION, environmentProperties.getKnockMasterToken(), params, Boolean.class, false);
			}
		} catch (Exception e) {
			LOG.error("Error while publishing notification on knock for inventory down and order punched", e);
		}

		try {
			if (ubd.getCafeManagerId() != null) {
				params.put("userId", ubd.getCafeManagerId() + "");
				WebServiceHelper.postRequestWithParamAndHeader(environmentProperties.getKnockBaseUrl() + AppConstants.KNOCK_NOTIFICATION_ENDPOINT + SEND_KNOCK_NOTIFICATION, environmentProperties.getKnockMasterToken(), params, Boolean.class, false);
			}
		} catch (Exception e) {
			LOG.error("Error while publishing notification on knock for inventory down and order punched", e);
		}

	}

	private void slackToManagerId(UnitBasicDetail ubd, String message) {
		try {
			SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvironmentType(),
				"Kettle", SlackNotification.ORDER_INVENTORY_STATUS, message);
			if (ubd.getUnitManagerId() != null) {
				SlackNotificationService.getInstance()
					.sendNotification(environmentProperties.getEnvironmentType(), "Kettle", null,
						!AppUtils.isProd(environmentProperties.getEnvironmentType())
							? environmentProperties.getEnvironmentType().name().toLowerCase() + "_"
							+ ubd.getUnitManagerId() + "_notify"
							: ubd.getUnitManagerId() + "_notify",
						message);
			}
			if (ubd.getCafeManagerId() != null) {
				SlackNotificationService.getInstance()
					.sendNotification(environmentProperties.getEnvironmentType(), "Kettle", null,
						!AppUtils.isProd(environmentProperties.getEnvironmentType())
							? environmentProperties.getEnvironmentType().name().toLowerCase() + "_"
							+ ubd.getCafeManagerId() + "_notify"
							: ubd.getCafeManagerId() + "_notify",
						message);
			}
		} catch (Exception e) {
			LOG.error("Error while publishing slack for inventory down and order punched", e);
		}
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitWiseDayOfWeekWiseItemEstimate getConsumptionEstimates(
			UnitWiseDayOfWeekWiseItemEstimateRequest request) {
		return dao.getConsumptionEstimates(request);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PullDetailReasons> fetchPullDetailReasonMetadata() {
		return dao.getPullDetailReasons();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	 public List<UnitPullDetail> fetchAllPendingUnitPullDetails(int unitId) {
		return dao.getPendingUnitPullDetails(unitId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitClosureDetails getKettleDayClose(Integer unitId, Date previousDate){
		List<UnitHours> unitHours = masterDataCache.getOperationalHoursForUnit(unitId);
		int retryCount =0;
		boolean retry = true;
		while (retry && retryCount<7) {
			try {
				UnitHours hours = unitHours.get(AppUtils.getDayOfWeek(previousDate)-1);
				if(Objects.nonNull(hours) && hours.isIsOperational()){
					retry = false;
				} else {
					previousDate = AppUtils.getPreviousBusinessDate(previousDate);
				}
			} catch (Exception e) {
				previousDate = AppUtils.getPreviousBusinessDate(previousDate);
			}
			retryCount++;
		}
		UnitClosureDetails unitClosureDetails = dao.getKettleDayClose(unitId,previousDate);
		if (Objects.isNull(unitClosureDetails)) {
			unitClosureDetails = dao.getKettleDayClose(unitId,AppUtils.getCurrentDate());
		}
		return unitClosureDetails;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitClosureDetails getLastClosureDetail(int unitId){
		return dao.getLastClosureDetail(unitId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Boolean validateKettleDayClose(Integer dayClosureId) {
		try {
			UnitClosureDetails unitClosureDetails = dao.find(UnitClosureDetails.class, dayClosureId);
			if (Objects.nonNull(unitClosureDetails)) {
				return unitClosureDetails.getCurrentStatus().equalsIgnoreCase("INITIATED");
			}
			return Boolean.FALSE;
		} catch (Exception e) {
			LOG.info("Exception Occurred while Validating the Kettle Day Close Id : {}", dayClosureId, e);
			return Boolean.FALSE;
		}
	}

	@Override
	public Boolean clearGoalsData(Integer unitId , Integer dayClousreId){
		try {
			LOG.info("Clearing Goals Data For Unit Id :::: {} ",unitId);
			GoalsDataClearRequest goalsDataClearRequest = new GoalsDataClearRequest(unitId,AppUtils.getDate(AppUtils.getCurrentDate()),dayClousreId);
			WebServiceHelper.postRequestWithAuthInternal(environmentProperties.getKnockBaseUrl()+
							Endpoints.GOAL_DATA_CLEAR, environmentProperties.getKnockMasterToken(),
					goalsDataClearRequest);
			return  true;
		} catch (Exception e) {
			LOG.error("Exception caught while Clearing Goals Data For Unit Id  {} ::: ",unitId,e);
			return false;
		}
	}

}
