/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification.sms;

import com.stpl.tech.kettle.core.data.vo.CashCardNotificationData;
import com.stpl.tech.master.core.notification.sms.Messenger;
import com.stpl.tech.master.core.notification.sms.SMSType;
import com.stpl.tech.master.domain.model.UnitBasicDetail;

public enum CashCardSMSNotificationType {

	CASH_CARD_PURCHASE(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof CashCardNotificationData) {
				CashCardNotificationData data = (CashCardNotificationData) token;
				return String.format(
						"Hi %s, Congrats on topup of Chaayos Wallet for Rs.%s. Bal. in your Wallet is Rs %s\r\n"
								+ "Details at https://chaayos.onelink.me/Thm6/cp",
						data.getCustomerName(), data.getPurchaseAmount().intValue(),
						data.getPendingAmount().intValue());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	},false), CASH_CARD_REDEMPTION(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof CashCardNotificationData) {
				CashCardNotificationData data = (CashCardNotificationData) token;
				String message = String.format(
						"Thank you for paying Rs.%s from your Chaayos Wallet at %s. Balance left is Rs. %s\r\n"
								+ "Track Details at https://chaayos.onelink.me/Thm6/wr",
						data.getUsedAmount().intValue(), data.getUnitName(), data.getPendingAmount().intValue());
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	},false),CASH_CARD_CASH_BACK_ALLOTMENT(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof CashCardNotificationData) {
				CashCardNotificationData data = (CashCardNotificationData) token;
				String message = String.format("Hurray! You've just won Rs %s Cashback. Use it from %s - %s at your nearest Chaayos Cafe. With love Team Chaayos", data.getCashBackAmount(), data.getStartDate(), data.getEndDate());
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	},false), CASH_CARD_CANCELLATION(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			// not implemented
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	},false), CASH_CARD_REFUND(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof CashCardNotificationData) {
				CashCardNotificationData data = (CashCardNotificationData) token;
				String message = String.format(
						"Dear %s your Chaayos Wallet has been refunded with Rs.%s for your order cancellation. Bal. in your Wallet is Rs.%s",
						data.getCustomerName(), data.getRefundAmount().intValue(), data.getPendingAmount().intValue());
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	},false);

	private final Messenger<Object, String> template;
	private final boolean whatsapp;

	private CashCardSMSNotificationType(Messenger<Object, String> template, boolean whatsapp) {
		this.whatsapp = whatsapp;
		this.template = template;
	}
	public boolean isWhatsapp() {
		return whatsapp;
	}
	public Messenger<Object, String> getTemplate() {
		return template;
	}

	public String getMessage(Object object) {
		return template.getMessage(object);
	}

	public String getMessage(Object object, UnitBasicDetail detail) {
		return template.getMessage(object);
	}

}
