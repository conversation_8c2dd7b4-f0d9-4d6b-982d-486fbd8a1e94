package com.stpl.tech.kettle.core.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.rules.RuleEngine;
import com.stpl.tech.kettle.core.service.RuleService;
import com.stpl.tech.kettle.data.dao.RulesDao;
import com.stpl.tech.kettle.data.model.RulesData;
import com.stpl.tech.kettle.data.model.RulesOptionResultData;
import com.stpl.tech.kettle.offer.model.Option;
import com.stpl.tech.kettle.offer.model.OptionResponseData;
import com.stpl.tech.kettle.offer.model.OrderData;
import com.stpl.tech.kettle.offer.model.RecommendationDetail;
import com.stpl.tech.kettle.offer.model.RuleData;

@Service
public class RulesServiceImpl implements RuleService {

	private final RuleEngine engine = new RuleEngine();

	@Autowired
	private RulesDao dao;

	public RuleData getRule(OrderData orderDetail) {
		return engine.findRule(orderDetail);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void createRules() {
		for (RuleData rule : RuleData.values()) {
			dao.createRule(rule);
		}
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public RulesOptionResultData getOrCreateRulesOptionResultData(int unitId, RuleData rule, Option option) {
		return dao.getOrCreateRulesOptionResultData(unitId,rule, option);
	}
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<RulesData> getRules(String status) {
		return dao.getRules(status);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public RecommendationDetail create(OptionResponseData response) {
		return dao.create(response);

	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void update(RecommendationDetail response) {
		dao.update(response);
	}

	
}
