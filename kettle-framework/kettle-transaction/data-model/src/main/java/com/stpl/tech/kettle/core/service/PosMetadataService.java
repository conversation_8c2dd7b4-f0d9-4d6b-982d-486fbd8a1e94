/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.ReportStatus;
import com.stpl.tech.kettle.data.model.CreditAccountDetail;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetail;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetailDefinition;
import com.stpl.tech.kettle.data.model.CrmScreenResponse;
import com.stpl.tech.kettle.data.model.ItemConsumptionEstimate;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.data.model.PullDetailReasons;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.data.model.UnitPullDetail;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimate;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimateRequest;
import com.stpl.tech.spring.model.FileDetail;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Interface for accessing all metadata related to POS. This is the read only
 * API for the metadata.
 *
 * <AUTHOR>
 *
 */
public interface PosMetadataService {

	public TransactionMetadata getTransactionData() throws DataNotFoundException;

	public List<PartnerAttributes> addDSRConfig(List<PartnerAttributes> PartnerData);

	public List<UnitDetail> getDSRConfigpartnerId() throws DataNotFoundException;

	public List<UnitExpenditureDetail> getAllPnlListForClosureId(Integer dayClosureId);

	public List<PartnerAttributes> getDSRConfig(Integer partnerId) throws DataNotFoundException;

	public PartnerAttributes addPartnerAttributes(PartnerAttributes partner) throws DataUpdationException, DataNotFoundException;

	public PartnerAttributes updatePartnerAttributes(PartnerAttributes partner) throws DataUpdationException, DataNotFoundException;

	public int closeDay(int unitId, int employeeId, String comment, int startOrderId, int lastOrderId,
						Date currentDate) throws DataUpdationException;

	public void markDayCloseAsCancelled(int dayClosureId);

	public boolean isDayClosed(int unitId, Date date) throws DataUpdationException;

	public ListData getComplimentaryCodes(boolean getAll) throws DataNotFoundException;

	public List<IdCodeName> getAllDeliveryPartner() throws DataNotFoundException;

	public List<UnitClosureDetails> getClosureFromBusinessDate(Date businessDate);

	public List<UnitClosureDetails> getClosureForBusinessDate(Date businessDate, Set<Integer> units);

	public boolean updateDayCloseDate(int closureId, Date businessDate) throws DataUpdationException;

	public void updateSalesData(Date businessDate);

	public List<UnitToDeliveryPartnerMappings> getAllDeliveryPartnerMappings();

	public CreditAccountDetail addCreditAccount(CreditAccountDetail detail);

	public CreditAccountDetail updateCreditAccount(CreditAccountDetail detail);

	public List<CreditAccountDetail> getAllCreditAccounts(String status);

	public List<ChannelPartnerDetail> getAllChannelPartner(Date businessDate) throws DataNotFoundException;

	public List<ItemConsumptionEstimate> getAllActiveItemConsumptionEstimate();

	public void updateConsumptionEstimates(Date businessDate);

	public UnitClosure getUnitsClosure(int unitId, Date date);

	public TransactionMetadata getTransactionData(UnitCategory category) throws DataNotFoundException;

	/**
	 * @param businessDate
	 */
	public void updateDailySalesData(Date businessDate);

	/**
	 * @param closureId
	 * @param status
	 * @return
	 * @throws DataUpdationException
	 */
	boolean updateReportStatus(int closureId, ReportStatus status);

	/**
	 * @param businessDate
	 * @param status
	 * @return
	 */
	List<UnitClosureDetails> getClosures(Date businessDate, ReportStatus status);

	/**
	 * @param businessDate
	 * @return
	 */
	List<UnitClosureDetails> getClosureForBusinessDate(Date businessDate);

	public void updateSuggestiveConsumptionEstimates(Date businessDate, int unitId);

	public void updateUptsForPreviousDate(Date businessDate, int unitId);

	void callProductEsimateUpdateProc(int unitId, Date currentDate) throws DataUpdationException;

	public boolean setCrmAppScreenDetail(CrmAppScreenDetailDefinition detail);

	FileDetail saveOfferImage(MimeType mimeType, String couponCode, String imageType, MultipartFile file, String s3OfferBucket, String hostURL);

    FileDetail saveRevenueCertificate(MimeType mimeType, String couponCode, String imageType, MultipartFile file,
                                      String s3OfferBucket, String hostURL);

    public CrmScreenResponse getCrmScreenUrl(String data) throws DataNotFoundException;

	public List<IdCodeName> getPnLMap(Integer unitId) throws DataNotFoundException;

    boolean resetMeterReading(Integer unitId);

	Map<String,Map<String,String>> getCrmScreenUrlForUnit(Integer unitId);

	Map<String,Map<String,String>> getCrmScreenUrlForUnitV1(Integer unitId);

	Map<String,Map<String, List<String>>> getCrmScreenUrlForUnitV2(Integer unitId);

	List<CrmAppScreenDetail> getCrmAppScreenDetail(String region);

    boolean cloneCrmAppScreenDetail(IdCodeName detail);

	boolean sendSlackNotificationForInventoryDown(IdCodeName request);

	Map<Integer, List<Date>> getUnitClosingTimeMap(Date previousDate);

	void createPreviousDateStockOutEntry(Date previousDate);

	UnitWiseDayOfWeekWiseItemEstimate getConsumptionEstimates(UnitWiseDayOfWeekWiseItemEstimateRequest request);

	List<PullDetailReasons> fetchPullDetailReasonMetadata();

	public List<UnitPullDetail> fetchAllPendingUnitPullDetails(int unitId);

	UnitClosureDetails getKettleDayClose(Integer unitId, Date previousDate);

	UnitClosureDetails getLastClosureDetail(int unitId);

    Boolean validateKettleDayClose(Integer dayClosureId);

	public Boolean clearGoalsData(Integer unitId , Integer dayClousreId);
}
