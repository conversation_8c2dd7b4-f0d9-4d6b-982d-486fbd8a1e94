/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.util.notification.AbstractTemplate;

public class DeliveryNotificationReceipt extends AbstractTemplate implements Notification{

	private OrderInfo detail;

	private String basePath;

	private String fromEmail;

	private String toEmail;

	private OrderEmailEntryType type;
	
	private Address deliveryAddress = null;

	Map<String, Object> data = new HashMap<String, Object>();

	public DeliveryNotificationReceipt(OrderInfo detail, String basePath, String fromEmail, String toEmail,
			OrderEmailEntryType type) {
		super();
		this.detail = detail;
		this.basePath = basePath;
		this.fromEmail = fromEmail;
		this.toEmail = toEmail;
		this.type = type;
		if (detail.getOrder().getDeliveryAddress() != null && detail.getCustomer().getAddresses() != null) {
			for (Address address : detail.getCustomer().getAddresses()) {
				if (detail.getOrder().getDeliveryAddress() == address.getId()) {
					this.deliveryAddress = address;
				}
			}
		}
	}

	public OrderInfo getDetail() {
		return detail;
	}

	public void setDetail(OrderInfo detail) {
		this.detail = detail;
	}

	@Override
	public String getTemplatePath() {
		switch (type) {
		case DISPATCH_DELAY:
			return "template/DispatchDelayReceipt.html";
		case DELIVERY_DELAY:
			return "template/DeliveryDelayReceipt.html";
		default:
			break;
		}
		return null;
	}

	public Map<String, Object> getData() {
		// Build the data-model
		data.put("order", detail.getOrder());
		data.put("customer", detail.getCustomer());
		data.put("unitName", detail.getUnit().getName());
		data.put("deliveryAddress", deliveryAddress);
		data.put("deliveryDetail", detail.getDeliveryDetails());
		return data;
	}

	@Override
	public String getFilepath() {
		switch (type) {
		case DISPATCH_DELAY:
			return basePath + "/" + detail.getOrder().getUnitId() + "/orders/" + detail.getOrder().getOrderId()
					+ "/DispatchDelay-" + detail.getOrder().getOrderId() + ".html";
		case DELIVERY_DELAY:
			return basePath + "/" + detail.getOrder().getUnitId() + "/orders/" + detail.getOrder().getOrderId()
					+ "/DeliveryDelay-" + detail.getOrder().getOrderId() + ".html";
		default:
			break;
		}
		return null;
	}

	public String getBasePath() {
		return basePath;
	}

	public String getFromEmail() {
		return fromEmail;
	}

	public String getToEmail() {
		return toEmail;
	}

	@Override
	public String getNotificationMessage() {
		StringBuilder sb = new StringBuilder();
		sb.append("Hi Team\nThere is an order where SDP has not marked Delivered. "
				+ "Please speak with the SDP and update the reason for the delay."
				+ "*Please find below the order detail*");
		sb.append("\nUnit Name :  "+detail.getUnit().getName());               
		sb.append("\nCustomer Name :  "+detail.getCustomer().getFirstName());
		sb.append("\nContact Number :  "+detail.getCustomer().getContactNumber());
		sb.append("\nAddress :  "+this.deliveryAddress);
		sb.append("\nOrder Id :  "+detail.getOrder().getGenerateOrderId()+" - Status  :  "+detail.getOrder().getStatus());
		sb.append("\nOrder Time :  "+detail.getOrder().getBillingServerTime());
		
		sb.append("\n*Delivery Details *");
		if(detail.getDeliveryDetails() != null){
			sb.append("\nSDP Id :  "+detail.getDeliveryDetails().getDeliveryBoyId());
			sb.append("\nSDP Name :  "+detail.getDeliveryDetails().getDeliveryBoyName());
			sb.append("\nSDP Contact Number :  "+detail.getDeliveryDetails().getDeliveryBoyPhoneNum());
		}else{
			sb.append("\nNo SDP Assigned");
		}
		return sb.toString();
	}

}