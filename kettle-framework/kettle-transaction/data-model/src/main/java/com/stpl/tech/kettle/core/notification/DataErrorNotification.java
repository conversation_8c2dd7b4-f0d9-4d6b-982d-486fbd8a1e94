/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.core.TransactionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;

public class DataErrorNotification extends EmailNotification implements Notification {

	private static final Logger LOG = LoggerFactory.getLogger(DataErrorNotification.class);

	private String subject;
	private String error;
	private EnvType env;
	private Date errorDate;
	private List<String> errorMessages;
	private List<String> toEmails;

	public DataErrorNotification(String subject, String error, EnvType env, List<String> toEmails) {
		super();
		this.subject = subject;
		this.error = error;
		this.env = env;
		this.errorDate = AppUtils.getCurrentTimestamp();
		this.toEmails = toEmails;
	}

	public DataErrorNotification(String subject, String error, List<String> errorMessages, EnvType env,
			List<String> toEmails) {
		this.subject = subject;
		this.error = error;
		this.errorMessages = errorMessages;
		this.env = env;
		this.errorDate = AppUtils.getCurrentTimestamp();
		this.toEmails = toEmails;
	}

	public String subject() {
		return (TransactionUtils.isDev(getEnvironmentType()) ? "DEV : " : "") + "Data Error Notification : " + subject + " on "
				+ new SimpleDateFormat("yyyy-MM-dd").format(errorDate);
	}

	public String body() {

		StringBuffer body = new StringBuffer(
				"<html><p><b>Data Error Notification : </b>" + subject + "<br/> <b>Error Generation Timestamp: </b>"
						+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(errorDate) + "</p>");

		body.append("<p><b> Error Summary: </b>" + error + "<br/></p>");

		if (errorMessages == null) {
			body.append("<p><b> Error: </b>" + error + "<br/>"
					+ "<b>Please connect to concerned authorites.</b><br/></p>" + "</html>");
		} else {
			body = body.append("<p><b>Error Messages</b><br/></p>");
			for (String msg : errorMessages) {
				body.append(msg + "<br/>");
			}
		}

		return body.toString();
	}

	public String getFromEmail() {
		return "<EMAIL>";
	}

	public String[] getToEmails() {
		return TransactionUtils.isDev(getEnvironmentType()) ? new String[] { "<EMAIL>" } : toEmails.toArray(new String[toEmails.size()]);
	}

	@Override
	public void sendEmail() {
		try {
			super.sendEmail();
			SlackNotificationService.getInstance().sendNotification(env, "Kettle", SlackNotification.SYSTEM_ERRORS,
					this);
		} catch (Exception e) {
			LOG.error("Error Notification Failure", e);
		}
	}

	@Override
	public String getNotificationMessage() {
		StringBuffer body = new StringBuffer("Data Error Notification : " + subject + "\nError Generation Timestamp: "
				+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(errorDate));

		body.append("\nError Summary: " + error + "");

		if (errorMessages == null) {
			body.append("\nError: " + error + "\nPlease connect to concerned authorites.");
		} else {
			body = body.append("\nError Messages\n");
			for (String msg : errorMessages) {
				body.append(msg + "\n");
			}
		}

		return body.toString();
	}

	@Override
	public EnvType getEnvironmentType() {
		return env;
	}
}
