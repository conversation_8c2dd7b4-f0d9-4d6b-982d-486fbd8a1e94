/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.data.model.AutoDayCloseVO;
import com.stpl.tech.util.notification.AbstractTemplate;

public class AutoDayCloseMailReceipt extends AbstractTemplate {

	private List<AutoDayCloseVO> incorrectDayCloses;
	private List<AutoDayCloseVO> dayCloseDefaulters;
	private List<AutoDayCloseVO> dayCloseFailures;
	private String basePath;
	private Date dayCloseDate;

	private Map<String, Object> data = new HashMap<String, Object>();

	public AutoDayCloseMailReceipt(List<AutoDayCloseVO> incorrectDayCloses, List<AutoDayCloseVO> dayCloseDefaulters,
			List<AutoDayCloseVO> dayCloseFailures, Date dayCloseDate, String basePath) {
		this.incorrectDayCloses = incorrectDayCloses;
		this.dayCloseDefaulters = dayCloseDefaulters;
		this.dayCloseFailures = dayCloseFailures;
		this.basePath = basePath;
		this.dayCloseDate = dayCloseDate;
	}

	public String getTemplatePath() {
		return "template/AutoDayCloseMail.html";
	}

	public Map<String, Object> getData() {
		// Build the data-model
		data.put("incorrectDayCloses", incorrectDayCloses.isEmpty() ? null : incorrectDayCloses);
		data.put("dayCloseDefaulters", dayCloseDefaulters.isEmpty() ? null : dayCloseDefaulters);
		data.put("dayCloseFailures", dayCloseFailures.isEmpty() ? null : dayCloseFailures);
		data.put("dayCloseDate", dayCloseDate);
		return data;
	}

	@Override
	public String getFilepath() {
		return basePath + "/AutoDayClose/" + new SimpleDateFormat("MMddyyyy").format(dayCloseDate) + ".html";
	}

	public Date getDayCloseDate() {
		return dayCloseDate;
	}

}
