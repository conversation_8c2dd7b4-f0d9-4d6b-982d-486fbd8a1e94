package com.stpl.tech.kettle.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.service.TableService;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.OrderManagementDao;
import com.stpl.tech.kettle.data.dao.TableDataDao;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderSettlement;
import com.stpl.tech.kettle.data.model.TableOrderMappingDetail;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.TableResponse;
import com.stpl.tech.kettle.domain.model.TableSettlement;
import com.stpl.tech.kettle.domain.model.TableStatus;
import com.stpl.tech.kettle.domain.model.UnitTableMapping;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.PrintType;
import com.stpl.tech.util.TemplateRenderingException;

@Service
public class TableServiceImpl implements TableService {

	@Autowired
	private TableDataDao tableDataDao;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private OrderManagementDao orderManagementDao;
/*
	@Autowired
	private LoyaltyDao loyaltyDao;*/
	
    @Autowired
    private EnvironmentProperties props;
    
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitTableMapping> getTablesForUnit(int unitId) {
		List<UnitTableMappingDetail> tablesList = tableDataDao.findTablesForUnit(unitId);
		List<UnitTableMapping> tables = new ArrayList<>();
		Map<Integer, UnitTableMapping> tablesMap = new HashMap<>();
		for (UnitTableMappingDetail t : tablesList) {
			UnitTableMapping u = convert(t);
			tablesMap.put(u.getTableNumber(), u);
		}
		Unit unit = masterDataCache.getUnit(unitId);
		for (int i = 1; i <= unit.getNoOfTables(); i++) {
			UnitTableMapping utm = tablesMap.get(i);
			if (utm == null) {
				tables.add(createOpenTable(unitId, i));
			} else {
				tables.add(utm);
			}
		}
		Collections.sort(tables);
		return tables;
	}

	private UnitTableMapping createOpenTable(int unitId, int tableNumber) {
		return new UnitTableMapping(unitId, tableNumber, TableStatus.OPEN);
	}

	private UnitTableMapping convert(UnitTableMappingDetail t) {
		return new UnitTableMapping(t.getTableRequestId(), t.getUnitId(), t.getTableNumber(), t.getCustomerId(),
				t.getCustomerName(), t.getTotalOrders(), t.getTotalAmount(), TableStatus.OCCUPIED, t.getContact());
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitTableMapping getTableSummary(int tableRequestId) {
		if (tableRequestId == 0) {
			return null;
		}
		UnitTableMappingDetail table = tableDataDao.find(UnitTableMappingDetail.class, tableRequestId);
		return table == null ? null : DataConverter.convert(table);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public UnitTableMapping reserveTableForUnit(int unitId, int tableNumber) throws DataNotFoundException {
		if (unitId == 0 || tableNumber == 0) {
			throw new DataNotFoundException("Invalid Unit and Table");
		}
		UnitTableMappingDetail table = tableDataDao.searchTableForUnit(unitId, tableNumber);
		if (table != null) {
			throw new DataNotFoundException("Table Already Reserved");
		}
		table = tableDataDao.reserveTableForUnit(unitId, tableNumber);
		return DataConverter.convert(table);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean closeTableForUnit(int tableRequestId) {
		if (tableRequestId == 0) {
			return false;
		}
		UnitTableMappingDetail table = tableDataDao.find(UnitTableMappingDetail.class, tableRequestId);
		if (table.getOrders().isEmpty()) {
			table.setTableStatus(TableStatus.CLOSED.name());
		}
		tableDataDao.update(table);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void tableCheckout(TableSettlement settlement) throws DataUpdationException {
		UnitTableMappingDetail table = tableDataDao.find(UnitTableMappingDetail.class, settlement.getTableRequestId());
		if(TableStatus.CLOSED.name().equals(table.getTableStatus())) {
			throw new DataUpdationException("Table already closed");
		}
		processSettlements(table, settlement, true);
		processSettlements(table, settlement, false);
		checkSettlementIsComplete(settlement);
		//closed as flow not working correctly
		//awardLoyalty(table);
		table.setTableStatus(TableStatus.CLOSED.name());
		tableDataDao.update(table);
	}

	private void checkSettlementIsComplete(TableSettlement settlement) throws DataUpdationException {
		for (Settlement s : settlement.getSettlements()) {
			if (s.getAmount().intValue() > 0) {
				throw new DataUpdationException("Settlement Amount does not match.");
			}
		}
	}

/*	private void awardLoyalty(UnitTableMappingDetail table) {
		for (TableOrderMappingDetail orderMap : table.getOrders()) {
			OrderDetail order = orderMap.getOrder();
			if (!AppConstants.getValue(order.getGiftCardOrder()) && order.getCustomerId() > 5) {
				// award loyalty
				loyaltyDao.updateScore(order.getCustomerId(), LoyaltyEventType.OUTLET_VISIT,
						AppConstants.CAFE_VISIT_LOYALTY_POINT, order.getOrderId(), false, false);
				// complete when points awarded
				break;
			}
		}
	}*/

	private void processSettlements(UnitTableMappingDetail table, TableSettlement settlement, boolean giftCardOrders)
			throws DataUpdationException {
		List<Settlement> settlementList = null;
		for (TableOrderMappingDetail orderMap : table.getOrders()) {
			if (TransactionUtils.isCancelled(orderMap.getOrder().getOrderStatus())) {
				// skip cancelled orders as amount is not distributed here
				continue;
			}
			if (giftCardOrders && !AppConstants.getValue(orderMap.getOrder().getGiftCardOrder())) {
				// processing gift cards only
				continue;
			}
			if (!giftCardOrders && AppConstants.getValue(orderMap.getOrder().getGiftCardOrder())) {
				// processing non gift cards only
				continue;
			}
			settlementList = new ArrayList<>();
			BigDecimal paidAmount = orderMap.getOrder().getSettledAmount();
			
			for (Settlement s : settlement.getSettlements()) {
				if (giftCardOrders && s.getMode() == AppConstants.PAYMENT_MODE_GIFT_CARD) {
					// skip settlement by gift card when order is gift card
					continue;
				}
				if (s.getAmount().intValue() > 0 && paidAmount.intValue() > 0) {
					if (paidAmount.intValue() <= s.getAmount().intValue()) {
						s.setAmount(s.getAmount().subtract(paidAmount));
						addToMap(orderMap.getOrder(), s, paidAmount, settlementList);
						paidAmount = BigDecimal.ZERO;
					} else {
						paidAmount = paidAmount.subtract(s.getAmount());
						addToMap(orderMap.getOrder(), s, s.getAmount(), settlementList);
						s.setAmount(BigDecimal.ZERO);
					}
				}
			}
			
			if (settlementList.isEmpty() && paidAmount.intValue() == 0) {
				// for orders with Zero amount
				Settlement s = new Settlement();
				s.setAmount(BigDecimal.ZERO);
				s.setMode(AppConstants.PAYMENT_MODE_CASH);
				s.setModeDetail(masterDataCache.getPaymentMode(AppConstants.PAYMENT_MODE_CASH));
				settlementList.add(s);
			}
			
			int i = 0;
			for (Settlement s : settlementList) {
				if (i == 0) {
					if (orderMap.getOrder().getOrderSettlements() != null
							&& !orderMap.getOrder().getOrderSettlements().isEmpty()) {
						OrderSettlement os = orderMap.getOrder().getOrderSettlements().get(0);
						orderManagementDao.addSettlement(orderMap.getOrder(), s, os);
					}
					i++;
					continue;
				}
				orderManagementDao.addSettlement(orderMap.getOrder(), s, null);

			}
		}
	}

	private void addToMap(OrderDetail orderDetail, Settlement s, BigDecimal amount, List<Settlement> settlementList) {
		Settlement s1 = new Settlement();
		s1.setAmount(amount);
		s1.setMode(s.getMode());
		s1.setModeDetail(s.getModeDetail());
		BigDecimal currentSettlement = BigDecimal.valueOf(amount.doubleValue());
		if (!s.getExternalSettlements().isEmpty()) {
			for (ExternalSettlement es : s.getExternalSettlements()) {
				if (currentSettlement.intValue() > 0 && es.getAmount().intValue() > 0) {
					if (currentSettlement.intValue() <= es.getAmount().intValue()) {
						s1.getExternalSettlements().add(new ExternalSettlement(es.getExternalSettlementId(),
								currentSettlement, es.getExternalTransactionId()));
						es.setAmount(es.getAmount().subtract(currentSettlement));
						currentSettlement = BigDecimal.ZERO;
					} else {
						s1.getExternalSettlements().add(new ExternalSettlement(es.getExternalSettlementId(),
								es.getAmount(), es.getExternalTransactionId()));
						currentSettlement = currentSettlement.subtract(es.getAmount());
						es.setAmount(BigDecimal.ZERO);
					}
				}
			}
		}
		settlementList.add(s1);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public TableResponse generateSettlementReceipt(int tableRequestId) throws TemplateRenderingException {
		if (tableRequestId <= 0) {
			return null;
		}
		UnitTableMappingDetail table = tableDataDao.find(UnitTableMappingDetail.class, tableRequestId);
		Map<Integer, BigDecimal> settlementMap = new HashMap<>();
		for (TableOrderMappingDetail map : table.getOrders()) {
			OrderDetail od = map.getOrder();
			if (TransactionUtils.isCancelled(map.getOrder().getOrderStatus())) {
				continue;
			}
			for (OrderSettlement os : od.getOrderSettlements()) {
				if (!settlementMap.containsKey(os.getPaymentModeId())) {
					settlementMap.put(os.getPaymentModeId(), BigDecimal.ZERO);
				}
				;
				settlementMap.put(os.getPaymentModeId(),
						settlementMap.get(os.getPaymentModeId()).add(os.getAmountPaid()));
			}
		}
		String receipt = TransactionUtils.getTableSettlementReceipt(settlementMap, masterDataCache.getPaymentModes(),
				table, props.getBasePath());
		TableResponse tr = new TableResponse();
		tr.setPrintType(PrintType.RAW);
		tr.setSettlementReceipt(receipt);
		return tr;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void refreshTableSummary(Integer tableRequestId) {
		tableDataDao.refreshTableSummary(tableRequestId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public UnitTableMapping changeTable(int tableRequestId, int tableNumber) throws DataUpdationException {
		if (tableRequestId == 0 || tableNumber == 0) {
			throw new DataUpdationException("Incorrect Table");
		}
		UnitTableMappingDetail utm1 = tableDataDao.find(UnitTableMappingDetail.class, tableRequestId);
		UnitTableMappingDetail utm2 = tableDataDao.searchTableForUnit(utm1.getUnitId(), tableNumber);

		if (TableStatus.CLOSED.name().equals(utm1.getTableStatus())) {
			throw new DataUpdationException("Table already closed");
		}

		if (utm2 != null) {
			throw new DataUpdationException("Table already occupied");
		}

		utm1.setTableNumber(tableNumber);
		tableDataDao.update(utm1);
		return DataConverter.convert(utm1);

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getTableOrders(int tableRequestId) {
		List<Integer> list = new ArrayList<Integer>();
		UnitTableMappingDetail utm = tableDataDao.find(UnitTableMappingDetail.class, tableRequestId);
		for (TableOrderMappingDetail map : utm.getOrders()) {
			OrderDetail od = map.getOrder();
			if (TransactionUtils.isCancelled(map.getOrder().getOrderStatus())) {
				continue;
			}
			list.add(od.getOrderId());
		}
		return list;
	}
	
}
