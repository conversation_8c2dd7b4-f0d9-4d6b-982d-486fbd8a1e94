package com.stpl.tech.kettle.data.dao.impl;

import java.math.BigDecimal;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.google.common.collect.Tables;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.data.dao.TableDataDao;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.TableOrderMappingDetail;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.TableStatus;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.AppUtils;

@Repository
public class TableDataDaoImpl extends AbstractDaoImpl implements TableDataDao {

	@SuppressWarnings("unchecked")
	@Override
	public List<UnitTableMappingDetail> findTablesForUnit(int unitId) {
		Query query = manager
				.createQuery("From UnitTableMappingDetail where unitId = :unitId AND tableStatus <> :tableStatus");
		query.setParameter("unitId", unitId);
		query.setParameter("tableStatus", TableStatus.CLOSED.name());
		return query.getResultList();
	}

	@Override
	public UnitTableMappingDetail searchTableForUnit(int unitId, int tableNumber) {
		Query query = manager.createQuery("From UnitTableMappingDetail where unitId = :unitId "
				+ "AND tableNumber = :tableNumber AND tableStatus <> :tableStatus");
		query.setParameter("unitId", unitId);
		query.setParameter("tableNumber", tableNumber);
		query.setParameter("tableStatus", TableStatus.CLOSED.name());

		List<UnitTableMappingDetail> results = query.getResultList();
		return results != null && !results.isEmpty() ? results.get(0) : null;
	}

	@Override
	public UnitTableMappingDetail reserveTableForUnit(int unitId, int tableNumber) {
		UnitTableMappingDetail table = new UnitTableMappingDetail();
		table.setUnitId(unitId);
		table.setTableNumber(tableNumber);
		table.setTableStatus(TableStatus.OCCUPIED.name());
		add(table);
		return table;
	}

	@Override
	public void addOrderTableMapping(OrderDetail orderDetail, Integer tableRequestId, Customer customer)
			throws DataUpdationException {

		UnitTableMappingDetail table = find(UnitTableMappingDetail.class, tableRequestId);

		if (table == null) {
			return;
		}
		if (TableStatus.CLOSED.name().equals(table.getTableStatus())) {
			throw new DataUpdationException("Table already closed.");
		}
		if (TableStatus.OPEN.name().equals(table.getTableStatus())) {
			table.setTableStatus(TableStatus.OCCUPIED.name());
		}
		if (orderDetail.getCustomerId() > 5 && table.getCustomerId() != null && table.getCustomerId() > 5
				&& !orderDetail.getCustomerId().equals(table.getCustomerId())) {
			throw  new DataUpdationException("Table already booked with other customer please check table allotments.");
		}

		// if there is no customer associated with table
		if ((table.getCustomerId() == null || table.getCustomerId() <= 5) && orderDetail.getCustomerId() > 5) {
			table.setCustomerId(orderDetail.getCustomerId());
			if (customer != null) {
				table.setContact(customer.getContactNumber());
			}
			table.setCustomerName(orderDetail.getCustomerName());
			if (orderDetail.getCustomerName() == null && customer != null) {
				table.setCustomerName(customer.getFirstName());
			}
			// set customer Id to all Orders
			for (TableOrderMappingDetail to : table.getOrders()) {
				if (to.getOrder().getCustomerId() <= 5) {
					to.getOrder().setCustomerId(customer.getId());
				}
			}
		}

		if (table.getCustomerName() == null) {
			table.setCustomerName(orderDetail.getCustomerName());
		}

		table.setTotalAmount(
				AppUtils.add(new BigDecimal(table.getTotalAmount()), orderDetail.getSettledAmount()).intValue());
		table.setTotalOrders(AppUtils.add(new BigDecimal(table.getTotalOrders()), BigDecimal.ONE).intValue());
		TableOrderMappingDetail tom = new TableOrderMappingDetail(tableRequestId, orderDetail);
		add(tom);

	}

	@Override
	public void refreshTableSummary(Integer tableRequestId) {
		UnitTableMappingDetail table = find(UnitTableMappingDetail.class, tableRequestId);
		if (table == null) {
			return;
		}
		table.setTotalAmount(BigDecimal.ZERO.intValue());
		table.setTotalOrders(BigDecimal.ZERO.intValue());
		for (TableOrderMappingDetail map : table.getOrders()) {
			OrderDetail orderDetail = map.getOrder();
			if (!TransactionUtils.isCancelled(orderDetail.getOrderStatus())) {
				table.setTotalAmount(AppUtils
						.add(new BigDecimal(table.getTotalAmount()), orderDetail.getSettledAmount()).intValue());
				table.setTotalOrders(AppUtils.add(new BigDecimal(table.getTotalOrders()), BigDecimal.ONE).intValue());
			}
		}
		update(table);
	}

	@Override
	public UnitTableMappingDetail findUnitTableMappingForTableRequestId(Integer tableRequestId){
		Query query = manager
				.createQuery("From UnitTableMappingDetail where tableRequestId = :tableRequestId");
		query.setParameter("tableRequestId", tableRequestId);
		UnitTableMappingDetail unitTableMappingDetail = (UnitTableMappingDetail) query.getSingleResult();
		return unitTableMappingDetail;
	}


}
