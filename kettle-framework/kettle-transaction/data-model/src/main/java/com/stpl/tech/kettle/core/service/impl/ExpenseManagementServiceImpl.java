package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.data.budget.vo.ElectricityStaticData.ElectricityBillType;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ExpenseRecordCategory;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.RestrictionType;
import com.stpl.tech.kettle.core.data.vo.PnlRecord;
import com.stpl.tech.kettle.core.service.ExpenseManagementService;
import com.stpl.tech.kettle.core.service.UnitBudgetService;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.ExpenseManagementDao;
import com.stpl.tech.kettle.data.dao.UnitBudgetDao;
import com.stpl.tech.kettle.data.dao.impl.BudgetUtils;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.ExpenseDetailData;
import com.stpl.tech.kettle.data.model.MeterDetailsData;
import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetExceededData;
import com.stpl.tech.kettle.data.model.UnitBudgetoryDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.domain.model.CalculationIndexStatus;
import com.stpl.tech.kettle.domain.model.ExpenseDetail;
import com.stpl.tech.kettle.domain.model.ExpenseStatus;
import com.stpl.tech.kettle.domain.model.MeterDetail;
import com.stpl.tech.kettle.domain.model.MeterDetailEntryType;
import com.stpl.tech.kettle.domain.model.UnitBudgetExceeded;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.MeterDetailEntryException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.util.AppUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.hibernate.exception.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ExpenseManagementServiceImpl implements ExpenseManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(ExpenseManagementServiceImpl.class);

    @Autowired
    private ExpenseManagementDao dao;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private UnitBudgetDao budgetDao;
    @Autowired
    private UnitBudgetService budgetService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ExpenseDetail addExpense(ExpenseDetail detail) {
        if (detail.getBudgetCategory() != null) {
            applyBudgetConstraint(detail);

            if (RestrictionType.ERROR.name().equals(detail.getErrorType())) {
                return detail;
            }
        }

        ExpenseDetailData data = new ExpenseDetailData();

        data.setUnitId(detail.getUnitId().getId());
        data.setExpenseCategory(detail.getExpenseCategory());
        data.setExpenseType(detail.getExpenseType());
        data.setAmount(detail.getAmount());
        data.setExpenseTypeId(detail.getExpenseTypeId());
        data.setBudgetCategory(detail.getBudgetCategory());
        data.setAccountableInPnL(AppUtils.setStatus(detail.isAccountableInPnL()));
        if (detail.getComment() != null) {
            data.setComment(detail.getComment());
        }

        data.setCreatedOn(AppUtils.getCurrentTimestamp());
        data.setStatus(ExpenseStatus.ACTIVE.name());
        data.setCreatedBy(detail.getCreatedBy().getId());
        if (dao.add(data) != null) {
            return detail;
        }
        return null;
    }

    private void applyBudgetConstraint(ExpenseDetail detail) {

        UnitBudgetoryDetail budgetoryDetail = budgetDao.getUnitBudgetDetail(detail.getUnitId().getId(),
            AppUtils.getCurrentMonth(), AppUtils.getCurrentYear());
        if (budgetoryDetail != null) {
            BigDecimal budgetAmount = BudgetUtils.getBudgetAmount(detail.getBudgetCategory(), budgetoryDetail);
            BigDecimal currentAmount = dao.getAllExpenses(detail.getUnitId().getId(), AppUtils.getCurrentMonth(),
                AppUtils.getCurrentYear(), detail.getBudgetCategory());
            BigDecimal requestedAmount = detail.getAmount();
            LOG.info("budgetAmount :" + budgetAmount + " currentAmount : " + currentAmount + " requestedAmount : "
                + requestedAmount);
            if (budgetAmount.compareTo(BigDecimal.ZERO) > 0
                && AppUtils.add(currentAmount, requestedAmount).compareTo(budgetAmount) == 1) {
                Map<String, String> map = BudgetUtils
                    .getBudgetRestrictionType(ExpenseRecordCategory.valueOf(detail.getBudgetCategory()));
                RestrictionType restrictionType = RestrictionType
                    .valueOf(map.get("restrictionType") == null ? "NOTHING" : map.get("restrictionType"));

                UnitBudgetExceededData exceededData = new UnitBudgetExceededData();
                exceededData.setUnitId(detail.getUnitId().getId());
                exceededData.setExpenseType(detail.getExpenseType());
                exceededData.setDay(AppUtils.getCurrentDayofMonth());
                exceededData.setExpenseLabel(map.get("label"));
                exceededData.setMonth(AppUtils.getCurrentMonth());
                exceededData.setYear(AppUtils.getCurrentYear());
                exceededData.setBudgetAmount(budgetAmount);
                exceededData.setCurrentAmount(currentAmount);
                exceededData.setRequestedAmount(requestedAmount);
                exceededData.setExpenseSource(
                    detail.getSource() == null ? ApplicationName.KETTLE_SERVICE.name() : detail.getSource());
                exceededData.setBudgetCategory(detail.getBudgetCategory());
                exceededData.setCreatedBy(detail.getCreatedBy().getId());
                exceededData.setCreatedOn(AppUtils.getCurrentTimestamp());
                StringBuilder sb = new StringBuilder(
                    "You have exceeded your allocated budget : " + budgetAmount + " for expense type "
                        + detail.getExpenseType() + " as your current expense is " + currentAmount + " .");
                exceededData.setNotificationType(restrictionType.name());
                if (restrictionType == RestrictionType.ERROR) {
                    sb.append("Requested expense amount : " + detail.getAmount() + " cannot be added !");
                }
                detail.setErrorType(restrictionType.name());
                LOG.info(sb.toString() + " : Caused :  " + restrictionType.name());
                detail.setErrorMsg(sb.toString());
                try {
                    List<UnitBudgetExceededData> budgetExceededDatas = budgetDao.getTodayEntry(exceededData,
                        AppUtils.getCurrentDate());
                    if (budgetExceededDatas.size() == 0) {
                        dao.add(exceededData);
                    }
                } catch (ConstraintViolationException e) {
                    LOG.info(e.getMessage());
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ExpenseDetail> getExpenseDetail(Integer unitId, String expenseCategory, String expenseType,
                                                Date startDate, Date endDate, String status) {
        List<ExpenseDetail> resultList = new ArrayList<ExpenseDetail>();
        List<ExpenseDetailData> list = dao.getExpenseDetail(unitId, expenseCategory, expenseType, startDate,
            endDate != null ? AppUtils.getEndOfDay(endDate) : endDate, status);
        for (ExpenseDetailData data : list) {
            resultList.add(DataConverter.convert(data, masterCache));
        }
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateExpenseStatus(ExpenseDetail expenseDetail) {
        ExpenseDetailData data = dao.find(ExpenseDetailData.class, expenseDetail.getId());
        data.setStatus(ExpenseStatus.CANCELLED.name());
        data.setCancelledBy(expenseDetail.getCancelledBy().getId());
        data.setCancelledOn(AppUtils.getCurrentTimestamp());
        data.setCancellationReason(
            expenseDetail.getCancellationReason() != null ? expenseDetail.getCancellationReason() : "");
        if (dao.update(data) != null) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addMeterReading(List<MeterDetail> details) throws DataNotFoundException, MeterDetailEntryException {

        List<MeterDetailsData> detailsDatas = new ArrayList<>();
        Date creationTime = AppUtils.getCurrentTimestamp();
        String currentTime = AppUtils.getBillPrintTime(creationTime);
        int unitId = details.get(0).getUnitId();
        UnitHours hours = masterCache.getUnit(unitId).getOperationalHours()
            .get(AppUtils.getDayOfWeek(creationTime) - 1);

        LOG.info("dayOfTheWeek : " + hours.getDayOfTheWeek() + " Entry Type : " + details.get(0).getEntryType());

        switch (details.get(0).getEntryType()) {
            case DAY_START:
                LOG.info("Unit dineIn Opening Time  : " + hours.getDineInOpeningTime() + " after 1 hour "
                    + AppUtils.getTimeAfterHour(hours.getDineInOpeningTime(), 1).toString());
                if (AppUtils.timeIsBetween(currentTime, "10:00:00", "11:59:59")
                    || AppUtils.timeIsBetween(currentTime, hours.getDineInOpeningTime().toString(),
                    AppUtils.getTimeAfterHour(hours.getDineInOpeningTime(), 1).toString())) {
                    List<MeterDetailsData> datas = dao.getMeterDetailList(unitId, AppUtils.getCurrentDate(),
                        MeterDetailEntryType.DAY_START.name(), null);
                    if (datas.size() > 0) {
                        MeterDetailsData data = datas.get(0);
                        LOG.info("last entry type : " + data.getEntryType() + " createdOn " + data.getCreatedOn()
                            + " created By : " + data.getCreatedBy() + " unit : " + data.getUnitId());
                        throw new MeterDetailEntryException(
                            "Reading entry has been done for this unit for type : DAY_START by "
                                + masterCache.getEmployee(data.getCreatedBy()) + " on "
                                + AppUtils.getBillPrintFormat(data.getCreatedOn()));
                    }
                } else {
                    throw new MeterDetailEntryException(
                        "Reading entry is not allowed at this time for this unit for type : DAY_START.");
                }
                break;
            case DAY_CLOSE:
                LOG.info("Unit dineIn closing Time  : " + hours.getDineInClosingTime() + " before 1 hour "
                    + AppUtils.getTimeAfterHour(hours.getDineInClosingTime(), -1).toString());
                if (AppUtils.timeIsBetween(currentTime, "22:00:00", "23:59:59") || AppUtils.timeIsBetween(currentTime,
                    AppUtils.getTimeAfterHour(hours.getDineInClosingTime(), -1).toString(),
                    hours.getDineInClosingTime().toString())) {
                    List<MeterDetailsData> datas = dao.getMeterDetailList(unitId, AppUtils.getCurrentDate(),
                        MeterDetailEntryType.DAY_CLOSE.name(), null);
                    if (datas.size() > 0) {
                        MeterDetailsData data = datas.get(0);
                        LOG.info("last entry type : " + data.getEntryType() + " createdOn " + data.getCreatedOn()
                            + " created By : " + data.getCreatedBy() + " unit : " + data.getUnitId());
                        throw new MeterDetailEntryException(
                            "Reading entry has been done for this unit for type : DAY_CLOSE by "
                                + masterCache.getEmployee(data.getCreatedBy()) + " on "
                                + AppUtils.getBillPrintFormat(data.getCreatedOn()));
                    }
                } else {
                    throw new MeterDetailEntryException(
                        "Reading entry is not allowed at this time for this unit for type : DAY_CLOSE.");
                }
                break;
        }

        Map<Integer, Integer> meterLastReadingMap = new HashMap<>();
        for (MeterDetail detail : details) {
            meterLastReadingMap.put(detail.getMeterNo(), detail.getLastReading());
            detailsDatas.add(DataConverter.convert(detail, creationTime));
        }

        String calculationIndex = getCalculationIndex(unitId, AppUtils.getCurrentDate(), details.get(0).getEntryType());

        for (MeterDetailsData data : detailsDatas) {
            /**
             * 1.If new meter is installed at unit after first day of month 2.If entry is
             * started after first day of month
             */
            /*
             * if (meterLastReadingMap.get(data.getMeterNo()) == 0) { calculationIndex =
             * CalculationIndexStatus.FIRST.name(); }
             */
            data.setCalculationIndex(calculationIndex);
            Object obj = dao.add(data);
            if (obj == null) {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Object[]> getLastMeterReading(int unitId) {
        return dao.getLastMeterReading(unitId);
    }

    private String getCalculationIndex(int unitId, Date currentDate, MeterDetailEntryType entryType) {
        String calculationIndex = null;
        List<MeterDetailsData> datas = dao.getMeterDetailList(unitId,
            AppUtils.getFirstDayOfMonth(AppUtils.getCurrentDate()), null, null);
        if (datas.size() == 0) {
            calculationIndex = CalculationIndexStatus.FIRST.name();
        } else {
            dao.updateCalculationIndex(unitId, AppUtils.getFirstDayOfMonth(currentDate),
                CalculationIndexStatus.MID.name());
            calculationIndex = CalculationIndexStatus.LAST.name();
        }
        return calculationIndex;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.ExpenseManagementService#
     * getPnlAccountableExpenses(int, int, int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ExpenseDetailData> getPnlAccountableExpenses(int unitId, int month, int year) {
        return dao.getPnlAccountableExpenses(unitId, month, year);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.ExpenseManagementService#
     * getUnAccountedPnlExpenses(int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ExpenseDetailData> getUnAccountedPnlExpenses(int unitId, int month, int year) {
        return dao.getUnAccountedPnlExpenses(unitId, month, year);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PnlAdjustmentDetail> getAdjustmentAggregate(int unitId, int month, int year, Date businessDate) {
        return dao.getAdjustmentAggregate(unitId, month, year, businessDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PnlAdjustmentDetail> getMTDAdjustmentAggregate(int unitId, int month, int year) {
        return dao.getMTDAdjustmentAggregate(unitId, month, year);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VoucherData> getUnAccountedPnlVoucherExpenses(Integer unitId) {
        return dao.getUnAccountedPnlVoucherExpenses(unitId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VoucherData> getAccountedPnlVoucherExpenses(Integer unitId, int month, int year) {
        return dao.getAccountedPnlVoucherExpenses(unitId, month, year);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.ExpenseManagementService#setPnLDetailId
     * (int, int, int, int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void setPnLDetailId(int unitId, int pnlDetailId, int month, int year) {
        dao.setPnLDetailId(unitId, pnlDetailId, month, year);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.ExpenseManagementService#getMeterReading(
     * int, int, int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<ElectricityBillType, Map<Integer, Integer>> getMeterReading(int unitId, int month, int year) {
        return dao.getMeterReading(unitId, month, year);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitBudgetExceeded> getBudgetExceededDetails(int unitId, String notificationType, Date tillDate) {
        List<UnitBudgetExceededData> datas = dao.getBudgetExceededDetails(unitId, notificationType,
            AppUtils.getDayBeforeOrAfterDay(tillDate, 1));
        List<UnitBudgetExceeded> budgetExceededs = new ArrayList<>();

        for (UnitBudgetExceededData data : datas) {
            budgetExceededs.add(DataConverter.convert(data, masterCache));
        }
        return budgetExceededs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateLastMeterReading(List<MeterDetail> details) {
        boolean result = false;
        for (MeterDetail detail : details) {
            MeterDetailsData detailsData = dao.find(MeterDetailsData.class, detail.getId());
            detailsData.setCurrentUnit(detail.getCurrentReading());
            detailsData.setUpdatedBy(detail.getUpdatedBy().getId());
            detailsData.setUpdatedOn(AppUtils.getCurrentTimestamp());
            detailsData = (MeterDetailsData) dao.update(detailsData);
            if (detailsData != null) {
                result = true;
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<MeterDetail> getLastMeterDetailList(MeterDetail detail) {
        List<MeterDetailsData> datas = dao.getLastMeterDetailList(detail);
        List<MeterDetail> meterDetails = new ArrayList<>();
        for (MeterDetailsData data : datas) {
            meterDetails.add(DataConverter.convert(data, masterCache));
        }
        return meterDetails;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addExpenses(List<ExpenseDetail> details) {
        BigDecimal amount = AppUtils.divide(details.get(0).getAmount(), new BigDecimal(details.size()));
        for (ExpenseDetail detail : details) {
            detail.setAmount(amount);
            addExpense(detail);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getMTDPnlDetailView(Integer unitId, Date date) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.addHeader("Content-Disposition", "attachment; filename=" + getMtdPnlFileName(unitId));
                List<UnitExpenditureDetail> expenditureDetails = budgetService.getAllMTDPnlForUnitForMonth(unitId,
                    date);
                BudgetHelper.createPnlDetailSheet(expenditureDetails, workbook);
            }

        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getMTDPnlAggregateDetailView(Integer unitId, Date date) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.addHeader("Content-Disposition", "attachment; filename=" + getMtdPnlFileName(unitId));
                List<PnlRecord> record = budgetService.getPnlMtdAggregateViewData(unitId,
                    date);
                BudgetHelper.createPnlAggregateDetailSheet(record, workbook, false, 0);
            }

        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getMTDPnlAggregateDetailViewDAM(List<Integer> units, Date date) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.addHeader("Content-Disposition", "attachment; filename=");

                int row = 0;
                for (Integer unit : units) {
                    List<PnlRecord> record = budgetService.getPnlMtdAggregateViewData(unit,
                        date);
                    BudgetHelper.createPnlAggregateDetailSheet(record, workbook, false, row);
                    row = row + 2 + 57;
                }
            }

        };
    }

    private String getMtdPnlFileName(Integer unitId) {
        return new StringBuilder(masterCache.getUnit(unitId).getName()).append("_MTD_Pnl_Details_For_Month").toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getFinalizedPnlDetailView(Integer unitId, Integer month, Integer year) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.addHeader("Content-Disposition", "attachment; filename=" + getMtdPnlFileName(unitId));
                List<UnitExpenditureDetail> expenditureDetails = budgetService.getFinalizedPnlForUnitForMonth(unitId,
                    month, year);
                BudgetHelper.createPnlDetailSheet(expenditureDetails, workbook);
            }
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getFinalizedPnlAggregateDetailView(Integer unitId, Integer month, Integer year) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.addHeader("Content-Disposition", "attachment; filename=" + getMtdPnlFileName(unitId));
                List<PnlRecord> record = budgetService.getFinalizedPnlAggregateForUnitForMonth(unitId,
                    month, year);
                BudgetHelper.createPnlAggregateDetailSheet(record, workbook, true, 0);
            }
        };
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitExpenditureDetail> getMTDPnlDetails(Integer unitId, Date date) {
        return budgetService.getAllMTDPnlForUnitForMonth(unitId, date);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<MeterDetail> getAllMeterDetailList(int unitId) {
        List<MeterDetailsData> datas = dao.getAllMeterDetailList(unitId);
        List<MeterDetail> meterDetails = new ArrayList<>();
        for (MeterDetailsData data : datas) {
            meterDetails.add(DataConverter.convert(data, masterCache));
        }
        return meterDetails;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean resetAllMeterReadingForUnit(UserSessionDetail sessionDetail) {
        return dao.resetAllMeterReadingForUnit(sessionDetail.getUnitId(), sessionDetail.getUserId());
    }

}
