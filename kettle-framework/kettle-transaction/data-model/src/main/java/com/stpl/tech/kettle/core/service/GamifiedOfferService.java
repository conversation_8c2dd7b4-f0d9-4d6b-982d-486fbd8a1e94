package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.data.model.GameLeaderBoardResponse;
import com.stpl.tech.kettle.data.model.GamifiedOfferRequest;
import com.stpl.tech.kettle.data.model.GamifiedOfferResponse;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.Pair;

import javax.jms.JMSException;
import java.io.IOException;
import java.util.List;

public interface GamifiedOfferService {
    public List<GamifiedOfferResponse> getGamifiedOffer(GamifiedOfferRequest  request,String source) throws <PERSON>UpdationException, JMSException, IOException;

    GameLeaderBoardResponse getLeaderBoard(String contact, String token, boolean getRank);

    Pair<Boolean,String> isGamifiedOfferExist(GamifiedOfferRequest request);

    List<GamifiedOfferResponse> getOfferViaDrools(GamifiedOfferRequest request) throws DataUpdationEx<PERSON>, JMSException, IOException;
}
