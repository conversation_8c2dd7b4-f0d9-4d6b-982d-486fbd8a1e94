/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.util;

/*
 * Java signing example
 * Echoes the signed message and exits
 */

// #########################################################
// #             WARNING   WARNING   WARNING               #
// #########################################################
// #                                                       #
// # This file is intended for demonstration purposes      #
// # only.                                                 #
// #                                                       #
// # It is the SOLE responsibility of YOU, the programmer  #
// # to prevent against unauthorized access to any signing #
// # functions.                                            #
// #                                                       #
// # Organizations that do not protect against un-         #
// # authorized signing will be black-listed to prevent    #
// # software piracy.                                      #
// #                                                       #
// # -QZ Industries, LLC                                   #
// #                                                       #
// #########################################################
import java.io.DataInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility for creating an RSA SHA1 signature based on a supplied PEM formatted
 * private key
 */
public class MessageSigner {
	private static Logger LOG = LoggerFactory.getLogger(MessageSigner.class.getName());
	private Signature sig;

	private static MessageSigner INSTANCE = null;

	public static MessageSigner getInstance() {
		try {
			INSTANCE = new MessageSigner("private-key.pem");
		} catch (Exception e) {
			LOG.error("Error while creating message signer", e);
		}
		return INSTANCE;
	}

	/**
	 * Constructs an RSA SHA1 signature object for signing
	 * 
	 * @param keyPath
	 * @throws Exception
	 */
	private MessageSigner(String keyPath) throws Exception {
		byte[] keyData = cleanseKeyData(readData(keyPath));
		PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyData);
		KeyFactory kf = KeyFactory.getInstance("RSA");
		PrivateKey key = kf.generatePrivate(keySpec);
		sig = Signature.getInstance("SHA1withRSA");
		sig.initSign(key);
	}

	/**
	 * Signs the specified data with the provided private key, returning the RSA
	 * SHA1 signature
	 * 
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public String sign(String data) throws Exception {
		sig.update(data.getBytes());
		return Base64.encodeBase64String(sig.sign()).toString();
	}

	/**
	 * Reads the raw byte[] data from a file resource
	 * 
	 * @param resourcePath
	 * @return the raw byte data from a resource file
	 * @throws IOException
	 */
	public static byte[] readData(String resourcePath) throws IOException {
		InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(resourcePath);
		if (is == null) {
			throw new IOException(String.format("Can't open resource \"%s\"", resourcePath));
		}
		DataInputStream dis = new DataInputStream(is);
		byte[] data = new byte[dis.available()];
		dis.readFully(data);
		dis.close();
		return data;
	}

	/**
	 * Parses an X509 PEM formatted base64 encoded private key, returns the
	 * decoded private key byte data
	 * 
	 * @param keyData
	 *            PEM file contents, a X509 base64 encoded private key
	 * @return Private key data
	 * @throws IOException
	 */
	private static byte[] cleanseKeyData(byte[] keyData) throws IOException {
		StringBuilder sb = new StringBuilder();
		String[] lines = new String(keyData).split("\n");
		String[] skips = new String[] { "-----BEGIN", "-----END", ": " };
		for (String line : lines) {
			boolean skipLine = false;
			for (String skip : skips) {
				if (line.contains(skip)) {
					skipLine = true;
				}
			}
			if (!skipLine) {
				sb.append(line.trim());
			}
		}
		return Base64.decodeBase64(sb.toString());
	}

}
