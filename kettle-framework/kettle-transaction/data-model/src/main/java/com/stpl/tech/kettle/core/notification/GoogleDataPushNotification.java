package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Slf4j
public class GoogleDataPushNotification extends EmailNotification {
    private String subject;
    private EnvType env;
    private Date eventDate;
    private List<String> successMessageList;
    private List<String> toEmailList;

    public GoogleDataPushNotification(String subject, List<String> successMessages, EnvType env) {
        this.subject = subject;
        this.successMessageList = successMessages;
        this.env = env;
        this.eventDate = AppUtils.getCurrentTimestamp();
    }

    public String subject() {
        return (TransactionUtils.isDev(getEnvironmentType()) ? "DEV : " : "") + "GoogleDataPush : " + subject + " on "
                + new SimpleDateFormat("yyyy-MM-dd").format(eventDate);
    }

    public String body() throws EmailGenerationException {

        StringBuffer body = new StringBuffer(
                "<html><p><b>GoogleDataPush Notification : </b>" + subject + "<br/> <b>Push Timestamp: </b>"
                        + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(eventDate) + "</p>");
        body.append("<p><b> Push Summary: Gclids</b>").append("<br/></p>");
        this.successMessageList.forEach(msg -> body.append(msg).append("<br/>"));
        body.append("</html>");
        return body.toString();
    }

    public String getFromEmail() {
        return "<EMAIL>";
    }

    public String[] getToEmails() {
        return toEmailList != null && !toEmailList.isEmpty() ? toEmailList.toArray(new String[toEmailList.size()])
                : new String[]{"<EMAIL>"};
    }

    @Override
    public void sendEmail() {
        try {
            super.sendEmail();
        } catch (Exception e) {
            log.error("GoogleDataPush Notification Failure", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return env;
    }

}
