package com.stpl.tech.kettle.core.budget.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.budget.dao.BudgetMetadataDao;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.UnitBankChargesBudgetDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetoryDetail;
import com.stpl.tech.kettle.data.model.UnitChannelPartnerChargesBudgetDetail;
import com.stpl.tech.kettle.data.model.UnitFacilityChargesBudgetDetail;
import com.stpl.tech.kettle.data.model.UnitManpowerBudgetDetail;
import com.stpl.tech.util.AppConstants;

@Repository
public class BudgetMetadataDaoImpl extends AbstractDaoImpl implements BudgetMetadataDao {

	private static final Logger LOG  = LoggerFactory.getLogger(BudgetMetadataDaoImpl.class);
	@Override
	public void cancelBudgetRecords(int month, int year) {
		try {
			Query query = manager.createQuery(
					" update UnitBudgetoryDetail set status = :inactive where year = :year and month = :month and status =:status");
			query.setParameter("year", year);
			query.setParameter("month", month);
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("inactive", AppConstants.IN_ACTIVE);
			query.executeUpdate();
		} catch (Exception e) {
		}

	}
	
	@Override
	public void cancelManpowerBudgetRecords(int month, int year) {
		try {
			Query query = manager.createQuery(
					" update UnitManpowerBudgetDetail set status = :inactive where year = :year and month = :month and status =:status");
			query.setParameter("year", year);
			query.setParameter("month", month);
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("inactive", AppConstants.IN_ACTIVE);
			query.executeUpdate();
		} catch (Exception e) {
		}

	}
	
	@Override
	public void cancelChannelPartnerChargesBudgetRecords(int month, int year) {
		try {
			Query query = manager.createQuery(
					" update UnitChannelPartnerChargesBudgetDetail set status = :inactive where year = :year and month = :month and status =:status");
			query.setParameter("year", year);
			query.setParameter("month", month);
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("inactive", AppConstants.IN_ACTIVE);
			query.executeUpdate();
		} catch (Exception e) {
		}

	}

	
	@Override
	public void updateBudgetRecords(List<UnitManpowerBudgetDetail> entityList) {

		for (UnitManpowerBudgetDetail detail : entityList) {
			updateManpowerBudgetRecord(detail);
		}
	}
	
	@Override
	public void updateChannelPartnerChargesBudgetRecords(List<UnitChannelPartnerChargesBudgetDetail> entityList) {

		for (UnitChannelPartnerChargesBudgetDetail detail : entityList) {
			updateChannelPartnerChargesBudgetRecord(detail);
		}
	}

	
	
	private void updateManpowerBudgetRecord(UnitManpowerBudgetDetail detail) {
		try {
			Query query = manager.createQuery(
					" update UnitBudgetoryDetail set byodCharges = :byodCharges, " + 
					"carLease = :carLease, " + 
					"driverSalary = :driverSalary, " + 
					"gratuity = :gratuity, " + 
					"insurnaceAccidental = :insurnaceAccidental, " + 
					"insurnaceMedical = :insurnaceMedical, " + 
					"supportsOpsTurnover = :supportsOpsTurnover, " + 
					"employeeFacilitationExpenses = :employeeFacilitationExpenses, " + 
					"telephoneSR = :telephoneSR, " + 
					"vehicleRunningAndMaintSR = :vehicleRunningAndMaintSR, " + 
					"employeeStockOptionExpense = :employeeStockOptionExpense, " + 
					"employerContributionLWF = :employerContributionLWF, " + 
					"esicEmployerCont = :esicEmployerCont, " + 
					"leaveTravelReimbursement = :leaveTravelReimbursement, " + 
					"pfAdministrationCharges = :pfAdministrationCharges, " + 
					"pfEmployerCont = :pfEmployerCont, " + 
					"quarterlyIncentive = :quarterlyIncentive, " + 
					"preOpeningSalary = :preOpeningSalary, " + 
					"bonusAttendance = :bonusAttendance, " + 
					"bonusJoining = :bonusJoining, " + 
					"bonusReferral = :bonusReferral, " + 
					"bonusHoliday = :bonusHoliday, " + 
					"bonusOthers = :bonusOthers, " + 
					"allowanceRemoteLocation = :allowanceRemoteLocation, " + 
					"allowanceCityCompensatory = :allowanceCityCompensatory, " + 
					"allowanceMonk = :allowanceMonk, "+ 
					"allowanceEmployeeBenefit = :allowanceEmployeeBenefit, " + 
					"allowanceOthers = :allowanceOthers, " + 
					"noticePeriodBuyout = :noticePeriodBuyout, " + 
					"noticePeriodDeduction = :noticePeriodDeduction, " + 
					"relocationExpenses = :relocationExpenses, " + 
					"stipendExpenses = :stipendExpenses, " + 
					"trainingCostRecovery = :trainingCostRecovery, " + 
					"severancePay = :severancePay, " + 
					"labourCharges = :labourCharges, " + 
					"salary = :salary,  salesIncentive = :salesIncentive  where year = :year and month = :month and status = :status and unitId = :unitId");
//			salaryIncentive = :salaryIncentive
			query.setParameter("year", detail.getYear());
			query.setParameter("month", detail.getMonth());
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("unitId", detail.getUnitId());
			query.setParameter("salary", detail.getSalary());
//			query.setParameter("salaryIncentive", detail.getSalaryIncentive());
			query.setParameter("salesIncentive", detail.getSalesIncentive());
			query.setParameter("byodCharges", detail.getByodCharges());
			query.setParameter("carLease", detail.getCarLease());
			query.setParameter("driverSalary", detail.getDriverSalary());
			query.setParameter("gratuity", detail.getGratuity());
			query.setParameter("insurnaceAccidental", detail.getInsurnaceAccidental());
			query.setParameter("insurnaceMedical", detail.getInsurnaceMedical());
			query.setParameter("supportsOpsTurnover", detail.getSupportsOpsTurnover());
			query.setParameter("employeeFacilitationExpenses", detail.getEmployeeFacilitationExpenses());
			query.setParameter("telephoneSR", detail.getTelephoneSR());
			query.setParameter("vehicleRunningAndMaintSR", detail.getVehicleRunningAndMaintSR());
			query.setParameter("employeeStockOptionExpense", detail.getEmployeeStockOptionExpense());
			query.setParameter("employerContributionLWF", detail.getEmployerContributionLWF());
			query.setParameter("esicEmployerCont", detail.getEsicEmployerCont());
			query.setParameter("leaveTravelReimbursement", detail.getLeaveTravelReimbursement());
			query.setParameter("pfAdministrationCharges", detail.getPfAdministrationCharges());
			query.setParameter("pfEmployerCont", detail.getPfEmployerCont());
			query.setParameter("quarterlyIncentive", detail.getQuarterlyIncentive());
			query.setParameter("preOpeningSalary", detail.getPreOpeningSalary());
			query.setParameter("bonusAttendance", detail.getBonusAttendance());
			query.setParameter("bonusJoining", detail.getBonusJoining());
			query.setParameter("bonusReferral", detail.getBonusReferral());
			query.setParameter("bonusHoliday", detail.getBonusHoliday());
			query.setParameter("bonusOthers", detail.getBonusOthers());
			query.setParameter("allowanceRemoteLocation", detail.getAllowanceRemoteLocation());
			query.setParameter("allowanceCityCompensatory", detail.getAllowanceCityCompensatory());
			query.setParameter("allowanceEmployeeBenefit", detail.getAllowanceEmployeeBenefit());
			query.setParameter("allowanceMonk", detail.getAllowanceMonk());
			query.setParameter("allowanceOthers", detail.getAllowanceOthers());
			query.setParameter("noticePeriodBuyout", detail.getNoticePeriodBuyout());
			query.setParameter("noticePeriodDeduction", detail.getNoticePeriodDeduction());
			query.setParameter("relocationExpenses", detail.getRelocationExpenses());
			query.setParameter("stipendExpenses", detail.getStipendExpenses());
			query.setParameter("trainingCostRecovery", detail.getTrainingCostRecovery());
			query.setParameter("severancePay", detail.getSeverancePay());
			query.setParameter("labourCharges", detail.getLabourCharges());

			query.executeUpdate();
		} catch (Exception e) {
			LOG.error("Error in updating Manpower Budget Record", e);
		}

	}

	private void updateChannelPartnerChargesBudgetRecord(UnitChannelPartnerChargesBudgetDetail detail) {
		try {
			Query query = manager.createQuery(
					" update UnitBudgetoryDetail set commissionChannelPartners = :commissionChannelPartners, "
							+ "cancellationChargesChannelPartners = :cancellationChargesChannelPartners, royaltyFees=:royaltyFees  where year = :year and month = :month and status = :status and unitId = :unitId");
			query.setParameter("year", detail.getYear());
			query.setParameter("month", detail.getMonth());
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("unitId", detail.getUnitId());
			query.setParameter("commissionChannelPartners", detail.getCommissionChannelPartners());
			query.setParameter("cancellationChargesChannelPartners", detail.getCancellationChargesChannelPartners());
			query.setParameter("royaltyFees", detail.getRoyaltyFees());
			query.executeUpdate();
		} catch (Exception e) {
			LOG.error("Error in updating Channel Partner Charges Budget Record", e);
		}

	}
	
	@Override
	public void cancelBankChargesBudgetRecords(int month, int year) {
		try {
			Query query = manager.createQuery(
					" update UnitBankChargesBudgetDetail set status = :inactive where year = :year and month = :month and status =:status");
			query.setParameter("year", year);
			query.setParameter("month", month);
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("inactive", AppConstants.IN_ACTIVE);
			query.executeUpdate();
		} catch (Exception e) {
		}

	}

	
	@Override
	public void updateBankChargesBudgetRecords(List<UnitBankChargesBudgetDetail> entityList) {

		for (UnitBankChargesBudgetDetail detail : entityList) {
			updateBankChargesBudgetRecord(detail);
		}
	}
	
	private void updateBankChargesBudgetRecord(UnitBankChargesBudgetDetail detail) {
		try {
			Query query = manager.createQuery(
					" update UnitBudgetoryDetail set bankCharges = :bankCharges where year = :year and month = :month and status = :status and unitId = :unitId");
			query.setParameter("year", detail.getYear());
			query.setParameter("month", detail.getMonth());
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("unitId", detail.getUnitId());
			query.setParameter("bankCharges", detail.getBankCharges());
			query.executeUpdate();
		} catch (Exception e) {
			LOG.error("Error in updating Channel Partner Charges Budget Record", e);
		}

	}

	@Override
	public void cancelFacilityChargesBudgetRecords(int month, int year) {
		try {
			Query query = manager.createQuery(
					" update UnitFacilityChargesBudgetDetail set status = :inactive where year = :year and month = :month and status =:status");
			query.setParameter("year", year);
			query.setParameter("month", month);
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("inactive", AppConstants.IN_ACTIVE);
			query.executeUpdate();
		} catch (Exception e) {
		}

	}

	
	@Override
	public void updateFacilityChargesBudgetRecords(List<UnitFacilityChargesBudgetDetail> entityList) {

		for (UnitFacilityChargesBudgetDetail detail : entityList) {
			updateFacilityChargesBudgetRecord(detail);
		}
	}
	
	private void updateFacilityChargesBudgetRecord(UnitFacilityChargesBudgetDetail detail) {
		try {
			Query query = manager.createQuery(
					" update UnitBudgetoryDetail set energyElectricity = :energyElectricity, waterCharges = :waterCharges where year = :year and month = :month and status = :status and unitId = :unitId");
			query.setParameter("year", detail.getYear());
			query.setParameter("month", detail.getMonth());
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("unitId", detail.getUnitId());
			query.setParameter("energyElectricity", detail.getEnergyElectricity());
			query.setParameter("waterCharges", detail.getWaterCharges());
			query.executeUpdate();
		} catch (Exception e) {
			LOG.error("Error in updating Facility Charges Budget Record", e);
		}

	}

	
	@Override
	public List<UnitBudgetoryDetail> getAllActiveBudgets(int year, int month) {
		try {
			Query query = manager
					.createQuery("from UnitBudgetoryDetail where year = :year and month = :month and status =:status");
			query.setParameter("year", year);
			query.setParameter("month", month);
			query.setParameter("status", AppConstants.ACTIVE);
			return query.getResultList();
		} catch (Exception e) {
		}
		return new ArrayList<UnitBudgetoryDetail>();
	}
	
	
}
