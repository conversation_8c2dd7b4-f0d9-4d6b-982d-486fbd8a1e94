/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.service.TempAccessService;
import com.stpl.tech.kettle.data.dao.TempAccessDao;
import com.stpl.tech.kettle.data.model.TempAccessCodeUsageData;

@Service
public class TempAccessServiceImpl implements TempAccessService {

	@Autowired
	private TempAccessDao dao;

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public TempAccessCodeUsageData grantAccess(String code, String contactNumber) {
		return dao.grantAccess(code, contactNumber);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void upadteStatus(int tempAccessCodeId, String status) {
		dao.upadteStatus(tempAccessCodeId, status);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String generateAccessCode(int orderId) {
		return dao.generateAccessCode(orderId);
	}

}
