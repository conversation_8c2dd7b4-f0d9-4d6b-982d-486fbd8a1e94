package com.stpl.tech.kettle.core.data.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DreamFolksTranscationData {
    // Data class for DreamFolks API

        @JsonProperty("transaction_id")
        private String transactionId;

        @JsonProperty("outlet_name")
        private String outletName;

        @JsonProperty("outlet_id")
        private Long outletId;

        @JsonProperty("product_name")
        private String productName;

        @JsonProperty("service_name")
        private String serviceName;

        @JsonProperty("service_id")
        private Integer serviceId;

        @JsonProperty("city")
        private String city;

        @JsonProperty("country")
        private String country;

        @JsonProperty("total_count")
        private Integer totalCount;

        @JsonProperty("transaction_time")
        private String transactionTime;

        @JsonProperty("transaction_time_UTC")
        private String transactionTimeUTC;

        @JsonProperty("status")
        private String status;

        public String getTransactionId() { return transactionId; }
        public void setTransactionId(String transactionId) { this.transactionId = transactionId; }

        public String getOutletName() { return outletName; }
        public void setOutletName(String outletName) { this.outletName = outletName; }

        public Long getOutletId() { return outletId; }
        public void setOutletId(Long outletId) { this.outletId = outletId; }

        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }

        public String getServiceName() { return serviceName; }
        public void setServiceName(String serviceName) { this.serviceName = serviceName; }

        public Integer getServiceId() { return serviceId; }
        public void setServiceId(Integer serviceId) { this.serviceId = serviceId; }

        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }

        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }

        public Integer getTotalCount() { return totalCount; }
        public void setTotalCount(Integer totalCount) { this.totalCount = totalCount; }

        public String getTransactionTime() { return transactionTime; }
        public void setTransactionTime(String transactionTime) { this.transactionTime = transactionTime; }

        public String getTransactionTimeUTC() { return transactionTimeUTC; }
        public void setTransactionTimeUTC(String transactionTimeUTC) { this.transactionTimeUTC = transactionTimeUTC; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
}
