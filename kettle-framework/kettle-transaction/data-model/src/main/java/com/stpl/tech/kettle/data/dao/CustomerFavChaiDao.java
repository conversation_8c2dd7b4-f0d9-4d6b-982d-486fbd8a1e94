package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerFavChaiDao extends JpaRepository<CustomerFavChaiMapping ,Integer > {
    List<CustomerFavChaiMapping> findByProductIdAndSourceIdAndStatus(Integer productId , Integer sourceId , String status);
}
