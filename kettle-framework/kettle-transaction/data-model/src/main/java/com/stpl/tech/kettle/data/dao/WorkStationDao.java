/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.AssemblyLogData;
import com.stpl.tech.kettle.data.model.AssemblyTATData;
import com.stpl.tech.kettle.data.model.MonkLogData;
import com.stpl.tech.kettle.data.model.WorkStationManualTaskDetail;
import com.stpl.tech.kettle.data.model.WorkstationLog;
import com.stpl.tech.kettle.domain.model.MonkCalibrationEvent;
import com.stpl.tech.kettle.domain.model.WorkstationManualTask;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @date 05-Apr-2016 10:51:48 pm
 * 
 */
public interface WorkStationDao {

	int addWorkStationLog(List<WorkstationLog> logList);

	public int addAssemblyLog(List<AssemblyLogData> list);

    public int addMonkLog(List<MonkLogData> monkLogData);

    public List<AssemblyTATData> getAssemblyTAT(Integer unitId, Date businessDate);

    public void processTATForAllUnits();

    List<WorkStationManualTaskDetail> addWorkStationManualTasks(List<WorkstationManualTask> workstationTasks);

    List<WorkstationManualTask> getWorkStationManualTasks(String generatedOrderId);

    MonkCalibrationEvent addMonkCalibrationEvent(MonkCalibrationEvent monkCalibrationEvent);

    MonkCalibrationEvent getLastMonkCalibrationStatus(Integer unitId, Integer monkNo);

    List<MonkCalibrationEvent> getMonkCalibrationStatus(Integer unitId);

    WorkstationLog getWorkStationLog(int orderItemId);
}
