/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.NumberToWord;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.HashMap;
import java.util.Map;

public class OrderReceipt extends AbstractTemplate {

	private OrderInfo detail;

	private String basePath;
	
	private String urlBasePath;
	
	private boolean isReprint = false;

	private Unit unit;

	private String billPromotion;

	private CustomerEmailData customerEmailData;

	Map<String, Object> data = new HashMap<String, Object>();

	private boolean cancellation;


	public OrderReceipt(String urlBasePath, Unit unit, OrderInfo detail, String basePath, String billPromotion, boolean cancellation) {
		super();
		this.detail = detail;
		this.basePath = basePath;
		this.urlBasePath = urlBasePath;
		this.unit = unit;
		this.billPromotion = billPromotion;
		this.cancellation = cancellation;
	}
	public OrderReceipt(String urlBasePath, Unit unit, OrderInfo detail, String basePath, String billPromotion, boolean cancellation, CustomerEmailData customerEmailData) {
		super();
		this.detail = detail;
		this.basePath = basePath;
		this.urlBasePath = urlBasePath;
		this.unit = unit;
		this.billPromotion = billPromotion;
		this.cancellation = cancellation;
		this.customerEmailData = customerEmailData;
	}

	public OrderInfo getDetail() {
		return detail;
	}

	public void setDetail(OrderInfo detail) {
		this.detail = detail;
	}

	@Override
	public String getTemplatePath() {
//		return "template/OrderReceipt.html";
        return "template/NewOrderReceipt.html";
    }

	public Map<String, Object> getData() {
		// Build the data-model
		data.put("customerEmailData",customerEmailData);
		data.put("order", detail.getOrder());
		data.put("urlBasePath", urlBasePath);
		data.put("customer", detail.getCustomer());
		data.put("customerContact", AppUtils.getCoveredCustomerContact(detail.getCustomer().getContactNumber()));
		data.put("discountPercent",
				detail.getOrder().getTransactionDetail().getDiscountDetail() != null
						&& detail.getOrder().getTransactionDetail().getDiscountDetail().getDiscount() != null
								? detail.getOrder().getTransactionDetail().getDiscountDetail().getDiscount()
										.getPercentage()
								: 0.0d);
		data.put("discountValue",
				detail.getOrder().getTransactionDetail().getDiscountDetail() != null
						&& detail.getOrder().getTransactionDetail().getDiscountDetail().getDiscount() != null
								? detail.getOrder().getTransactionDetail().getDiscountDetail().getDiscount().getValue()
								: 0.0d);
		data.put("promotionalDiscount",
				detail.getOrder().getTransactionDetail().getDiscountDetail() != null
						&& detail.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer() != null
								? detail.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer()
								: 0.0d);
		data.put("unit", unit);
		data.put("isOrderReprint", isReprint);
		data.put("isOrderCancelled", detail.getOrder().getStatus().equals(OrderStatus.CANCELLED));
		data.put("isNonProdEnvironment", !AppUtils.isProd(detail.getEnv()));
		Address deliveryAddress = null;
		if (detail.getOrder().getDeliveryAddress() != null && detail.getCustomer().getAddresses() != null) {
			for (Address address : detail.getCustomer().getAddresses()) {
				if (detail.getOrder().getDeliveryAddress() == address.getId()) {
					deliveryAddress = address;
				}
			}
		}
		data.put("deliveryAddress", deliveryAddress);
		data.put("channelPartner", detail.getChannelPartner());
		data.put("deliveryPartner", detail.getDeliveryPartner());
		data.put("settlements", detail.getOrder().getSettlements());
		data.put("billPromotion", billPromotion);
		data.put("paidAmountInWords", NumberToWord.getInstance().convertNumberToWords(detail.getOrder().getTransactionDetail().getPaidAmount().intValue()));
		String orderSource = null;
		if(detail.getBrand() == null || detail.getBrand().getBrandId().equals(AppConstants.CHAAYOS_BRAND_ID)){
			if (UnitCategory.TAKE_AWAY.name().equals(detail.getOrder().getSource())) {
				orderSource ="Chaayos Take Away";
			}
			else if (UnitCategory.COD.name().equals(detail.getOrder().getSource())) {
				orderSource ="Chaayos Chai On Demand";
			}
			else if (UnitCategory.CAFE.name().equals(detail.getOrder().getSource())) {
				orderSource ="Chaayos";
			}
		} else {
			if (UnitCategory.TAKE_AWAY.name().equals(detail.getOrder().getSource())) {
				orderSource = detail.getBrand().getBrandName() + " Take Away";
			}
			else if (UnitCategory.COD.name().equals(detail.getOrder().getSource())) {
				orderSource =detail.getBrand().getBrandName() + " Delivery";
			}
			else if (UnitCategory.CAFE.name().equals(detail.getOrder().getSource())) {
				orderSource = detail.getBrand().getBrandName();
			}
		}

		data.put("orderSource", orderSource);
		data.put("fillWhiteSpace", new WhitespaceMethods());
		return data;
	}

	@Override
	public String getFilepath() {
		return basePath + "/" + getUnit().getId() + "/orders/" + detail.getOrder().getOrderId() + (isCancellation() ? "/CancelledOrderReceipt-" : "/OrderReceipt-")
				+ detail.getOrder().getOrderId() + ".html";
	}

	public String getBasePath() {
		return basePath;
	}

	public void setReprint() {
		isReprint = true;
	}

	public Unit getUnit() {
		return unit;
	}

	public void setUnit(Unit unit) {
		this.unit = unit;
	}

	public boolean isCancellation() {
		return cancellation;
	}


}
