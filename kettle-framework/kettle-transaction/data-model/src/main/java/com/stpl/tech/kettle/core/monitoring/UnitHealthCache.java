/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.monitoring;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.DelayQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.service.model.ScreenType;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;

@Repository
public class UnitHealthCache {

	private static final Logger LOG = LoggerFactory.getLogger(UnitHealthCache.class);
	@Autowired(required = false)
	private MasterDataCache masterCache;
	private BlockingQueue<UnitMonitorData> queue = null;
	private Map<Integer, Map<UnitMonitorData, Integer>> unitStatusData = new TreeMap<Integer, Map<UnitMonitorData, Integer>>();

	public void loadCache() throws DataNotFoundException {
		LOG.info("Created instance of the UnitHealhCache and created the health cache monitor");
		// Creates an instance of blocking queue using the DelayQueue.
		queue = new DelayQueue<UnitMonitorData>();
		for (UnitCategory category : UnitCategory.values()) {
			List<UnitBasicDetail> units = masterCache.getUnits(category);
			for (UnitBasicDetail unit : units) {
				addNewUnitRequest(unit.getId(), unit.getName(), unit.getCategory(), unit.getNoOfTerminal());
			}
		}
		LOG.info("Size of persisted cache map ::::::::: " + this.unitStatusData.size());
	}

	public synchronized int addToCache(UnitMonitorData data) throws DataNotFoundException {
		Map<UnitMonitorData, Integer> map = get(data.getUnitId());
		Integer detail = map.get(data);
		if (detail == null) {
			detail = 1;
		} else {
			if (detail < 3) {
				detail = detail + 1;
			}
		}
		try {
			queue.put(data);
			map.put(data, detail);
			LOG.info(String.format("Added to cache %s : Count %d", data, detail));
		} catch (InterruptedException e) {
			LOG.info(String.format("Failed to add object %s to unit health cache", data));
		}
		return detail;

	}

	public synchronized int removeFromCache(UnitMonitorData data, boolean isLogout) throws DataNotFoundException {
		Map<UnitMonitorData, Integer> map = get(data.getUnitId());
		Integer detail = map.get(data);
		if (detail == null) {
			detail = 1;
		} else {
			if (detail >= 1 && !isLogout) {
				detail = detail - 1;
			} else if (isLogout) {
				detail = 0;
			}

		}
		map.put(data, detail);
		LOG.info(String.format("Removed from cache %s : Count %d", data, detail));
		return detail;
	}

	private Map<UnitMonitorData, Integer> get(int unitId) throws DataNotFoundException {
		Map<UnitMonitorData, Integer> map = unitStatusData.get(unitId);
		if (map == null) {
			Unit unit = masterCache.getUnit(unitId);
			map = addNewUnitRequest(unit.getId(), unit.getName(), unit.getFamily(), unit.getNoOfTerminals());
		}
		return map;
	}

	private Map<UnitMonitorData, Integer> addNewUnitRequest(int unitId, String unitName, UnitCategory unitFamily,
			int noOfTerminals) throws DataNotFoundException {

		Map<UnitMonitorData, Integer> screensInUnit = new HashMap<UnitMonitorData, Integer>();
		for (int terminalId = 1; terminalId <= noOfTerminals; terminalId++) {
			screensInUnit.put(new UnitMonitorData(unitId, unitName, terminalId, ScreenType.POS, unitFamily, 0), 0);
			screensInUnit.put(new UnitMonitorData(unitId, unitName, terminalId, ScreenType.CUSTOMER, unitFamily, 0), 0);
		}
		screensInUnit.put(new UnitMonitorData(unitId, unitName, 0, ScreenType.ASSEMBLY, unitFamily, 0), 0);
		this.unitStatusData.put(unitId, screensInUnit);
		return screensInUnit;
	}

	public boolean isScreenActive(int unitId, ScreenType screenType) {

		return isScreenActive(unitId, screenType, 0);
	}

	public boolean isScreenActive(int unitId, ScreenType screenType, int terminalId) {
		Map<UnitMonitorData, Integer> map = unitStatusData.get(unitId);
		if (map != null) {
			UnitMonitorData data = new UnitMonitorData(unitId, null, terminalId, screenType, null, 0);
			Integer count = map.get(data);
			if (count != null && count > 0) {
				return true;
			}
		}
		return false;
	}

	public BlockingQueue<UnitMonitorData> getQueue() {
		return queue;
	}

	public Map<Integer, Map<UnitMonitorData, Integer>> getUnitStatusData() {
		return unitStatusData;
	}

}
