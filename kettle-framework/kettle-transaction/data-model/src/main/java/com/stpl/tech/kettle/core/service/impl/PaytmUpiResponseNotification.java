package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.master.payment.model.PaymentResponse;
import com.stpl.tech.master.payment.model.ingenico.IngenicoQrResponse;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiS2SResponse;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.pubnub.PushNotification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class PaytmUpiResponseNotification implements PushNotification<Map<String, PaymentResponse>> {

    private static final Logger LOG = LoggerFactory.getLogger(PaytmUpiResponseNotification.class);

    private String channelName;
    private int retries;
    private Map<String, PaymentResponse> message;

    public PaytmUpiResponseNotification(EnvType env, PaytmUpiS2SResponse paytmUpiS2SResponse, PaymentResponse response) {
        this.channelName = initChannel(env, paytmUpiS2SResponse.getPosId());
        Map<String, PaymentResponse> message = new HashMap<>();
        message.put("PAYTM_UPI_PAY_STATUS", response);
        setMessage(message);
    }

    public static String initChannel(EnvType env, String identifier) {
        LOG.info("paytm upi pub nub channel {}", env + "_PAYTM_UPI_" + identifier);
        return env + "_PAYTM_UPI_" + identifier;
    }

    @Override
    public Map<String, PaymentResponse> getMessage() {
        return this.message;
    }

    @Override
    public void setMessage(Map<String, PaymentResponse> message) {
        this.message = message;
    }

    @Override
    public String getChannelName() {
        return this.channelName;
    }

    @Override
    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    @Override
    public int getRetries() {
        return retries;
    }

    @Override
    public void setRetries(int value) {
        this.retries = value;
    }


}
