/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.EnvType;

import java.util.Objects;

public class SubscriptionInfo {

	private EnvType env;
	private Order order;
	private Customer customer;
	private IdCodeName channelPartner;
	private Unit unit;

	public SubscriptionInfo(EnvType env, Order order, Customer customer, Unit unit, IdCodeName channelPartner) {
		super();
		this.order = order;
		this.customer = customer;
		this.env = env;
		this.channelPartner = channelPartner;
		this.unit = unit;
	}

	public Order getOrder() {
		return order;
	}

	public void setOrder(Order order) {
		this.order = order;
	}

	public Customer getCustomer() {
		return customer;
	}

	public void setCustomer(Customer customer) {
		this.customer = customer;
	}

	public EnvType getEnv() {
		return env;
	}

	public void setEnv(EnvType env) {
		this.env = env;
	}

	public IdCodeName getChannelPartner() {
		return channelPartner;
	}

	public void setChannelPartner(IdCodeName channelPartner) {
		this.channelPartner = channelPartner;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		SubscriptionInfo orderInfo = (SubscriptionInfo) o;
		return Objects.equals(getOrder().getOrderId(), orderInfo.getOrder().getOrderId());
	}

	@Override
	public int hashCode() {
		return Objects.hash(getOrder().getOrderId());
	}

	public Unit getUnit() {
		return unit;
	}

	public void setUnit(Unit unit) {
		this.unit = unit;
	}

}
