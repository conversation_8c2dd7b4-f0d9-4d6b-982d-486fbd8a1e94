/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification.receipt;

import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.core.CustomerRepeatType;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.util.RawPrintHelper;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;

import java.util.Date;
import java.util.Objects;

public class CustomerCommunicationRawPrint extends OrderRawPrintReceipt {

	private static final Logger LOG = LoggerFactory.getLogger(CustomerCommunicationRawPrint.class);

	public CustomerCommunicationRawPrint(Unit unit, MasterDataCache metadataCache, OrderInfo detail,
										 String basePath, EnvironmentProperties env) throws DataNotFoundException {
		super(unit, detail, basePath, null, env);
	}

	@Override
	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getOrder().getOrderId() + "/CustomerCommunicationPrint-"
				 + "-" + getOrder().getOrderId() + ".html";
	}

	@Override
	public StringBuilder processData() {
		NextOffer offer = getDetail().getNextOffer();
		NextOffer deliveryOffer =getDetail().getNextDeliveryOffer();
		LOG.info("Raw Print NBO - {}", offer);
		reset();
		lineBreak();
		center(bold(doubleFont("Chaayos")));
		lineBreak();
		left(bold(doubleFont("Hi " + (getOrder().getCustomerId() <= 5 ? "Chai Lover" : getCustomer().getFirstName()) + ",")));
		if ((offer != null && offer.isAvailable()) || (Objects.nonNull(deliveryOffer) && deliveryOffer.isAvailable())) {
			Date validFrom;
			Date validTill;
			if((offer != null && offer.isAvailable())){
				validFrom = AppUtils.getDate(offer.getValidityFrom(), AppUtils.DATE_FORMAT_STRING);
				validTill = AppUtils.getDate(offer.getValidityTill(), AppUtils.DATE_FORMAT_STRING);
				lineBreak();
				center(bold("----------------------------------------"));
				lineBreak();
				center(bold(extraDoubleFont(offer.getText())));
				center(bold("( applicable at all chaayos cafe )"));
				if (CustomerRepeatType.NEW.name().equals(offer.getCustomerType())
						&& AppConstants.LOYAL_TEA_COUPON_CODE.equals(offer.getOfferCode())) {
					lineBreak();
					center(bold(doubleFont("Second Chai Free")));
					center(bold(("valid from "+AppUtils.getDayMonthYear(validFrom)+" to "+ AppUtils.getDayMonthYear(validTill))));
				} else {
					lineBreak();
					center(bold(doubleFont("Use Code : " + offer.getOfferCode())));
					center(bold(("valid from "+AppUtils.getDayMonthYear(validFrom)+" to "+ AppUtils.getDayMonthYear(validTill))));
					lineBreak();
					center(bold("Note : Offer not applicable on Merchandise"));
				}
				lineBreak();
				center(bold("----------------------------------------"));
			}
			if((Objects.nonNull(deliveryOffer) && deliveryOffer.isAvailable())){
				validFrom = AppUtils.getDate(deliveryOffer.getValidityFrom(), AppUtils.DATE_FORMAT_STRING);
				validTill = AppUtils.getDate(deliveryOffer.getValidityTill(), AppUtils.DATE_FORMAT_STRING);
				lineBreak();
				center(bold("----------------------------------------"));
				lineBreak();
				center(bold(extraDoubleFont(deliveryOffer.getText())));
				center(bold(doubleFont("on " + deliveryOffer.getChannelPartner())));
				center(bold("( applicable at all chaayos cafe on "+deliveryOffer.getChannelPartner()+" )"));
				lineBreak();
				center(bold(doubleFont("Use Code : " + deliveryOffer.getOfferCode())));
				center(bold(("valid from "+AppUtils.getDayMonthYear(validFrom)+" to "+ AppUtils.getDayMonthYear(validTill))));
				lineBreak();
				center(bold("----------------------------------------"));
			}
		}
		else {
			lineBreak();
			lineBreak();
			left(bold(
					"Thank you for being a part of not-so-secret club of chai lovers. Here, your Chai will always be piping hot, your snacks yummy, and you Realxed."));
			lineBreak();
		}
//		center(bold(doubleFont("Scan Me!!!")));
//		lineBreak();
//		qrAppend(qr("https://stpl.page.link/bill_print_app_qr"));
		lineBreak();
		center(bold("Download Chaayos Android and iOS App now"));
		center(bold("and avail amazing benefits!!!"));
		lineBreak();
		cut();
		return getSb();
	}

	private void lineBreak() {
		left(RawPrintHelper.LINE_BREAK);
	}


}
