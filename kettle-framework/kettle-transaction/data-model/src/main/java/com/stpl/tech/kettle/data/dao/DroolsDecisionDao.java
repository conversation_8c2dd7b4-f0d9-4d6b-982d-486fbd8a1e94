package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.DroolsDecisionTableData;
import com.stpl.tech.master.data.dao.AbstractDao;

import java.util.List;

public interface DroolsDecisionDao extends AbstractDao {
    public DroolsDecisionTableData getDecisionTableData(String type);

    void deactivateOther(String type);

    void activateCurrentFile(String type, String fileName);

    List<DroolsDecisionTableData> fetchAllFileByType(String fileType);
    public List<DroolsDecisionTableData> getDecisionTableDataByStatus(String type);

    void declineExistingProcessingFile(String type,String filename);

    public void findByTypeAndVersionAndSetStatusAndIsDefault(String type, String version, String status,String isDefault);
    public void findByTypeAndVersionAndSetIsDefault(String type, String version, String isDefault);
}
