package com.stpl.tech.kettle.core.data.vo;

import java.util.List;

import com.stpl.tech.kettle.core.FeedbackStatus;
import com.stpl.tech.master.domain.model.IdCodeName;

/**
 * Created by Chaayos on 27-08-2016.
 */
public class FeedbackOrderMetadata {

	private String unitName;
	private int unitId;
	private List<IdCodeName> products;
	private String orderTime;
	private String orderDay;
	private String feedbackToken;
	private Integer feedbackEventId;
	private String orderSource;
	private FeedbackStatus feedbackStatus;

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public List<IdCodeName> getProducts() {
		return products;
	}

	public void setProducts(List<IdCodeName> products) {
		this.products = products;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public String getOrderDay() {
		return orderDay;
	}

	public void setOrderDay(String orderDay) {
		this.orderDay = orderDay;
	}

	public String getFeedbackToken() {
		return feedbackToken;
	}

	public void setFeedbackToken(String feedbackToken) {
		this.feedbackToken = feedbackToken;
	}

	public Integer getFeedbackEventId() {
		return feedbackEventId;
	}

	public void setFeedbackEventId(Integer feedbackEventId) {
		this.feedbackEventId = feedbackEventId;
	}

	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	public FeedbackStatus getFeedbackStatus() {
		return feedbackStatus;
	}

	public void setFeedbackStatus(FeedbackStatus feedbackStatus) {
		this.feedbackStatus = feedbackStatus;
	}

}
