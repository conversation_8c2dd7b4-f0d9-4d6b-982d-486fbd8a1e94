package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.SuperUOrderLevelData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface SuperUOrderLevelDataDao extends JpaRepository<SuperUOrderLevelData,Integer> {

    List<SuperUOrderLevelData> findByBillingServerTimeGreaterThanEqualAndBillingServerTimeLessThanAndIsApplicable(Date startDate, Date endDate,boolean isApplicable);
    List<SuperUOrderLevelData> findByBillingServerTimeGreaterThanEqualAndBillingServerTimeLessThanAndUnitIdAndIsApplicable(Date startDate, Date endDate,Integer unitId,boolean isApplicable);
}
