package com.stpl.tech.kettle.core.mapper;

import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.domain.model.OrderPaymentDetailData;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderPaymentDetailMapper {
    OrderPaymentDetailMapper INSTANCE = Mappers.getMapper(OrderPaymentDetailMapper.class);

    OrderPaymentDetailData toDomain(OrderPaymentDetail orderPaymentDetail);
}
