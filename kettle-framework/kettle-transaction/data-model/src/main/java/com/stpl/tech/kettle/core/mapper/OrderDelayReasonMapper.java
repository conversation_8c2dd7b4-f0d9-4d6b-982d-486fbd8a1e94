package com.stpl.tech.kettle.core.mapper;

import com.stpl.tech.kettle.data.model.OrderDelayReason;
import com.stpl.tech.kettle.domain.model.OrderDelayReasonData;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderDelayReasonMapper {
    OrderDelayReasonMapper INSTANCE = Mappers.getMapper(OrderDelayReasonMapper.class);

    OrderDelayReasonData toDomain(OrderDelayReason orderDelayReason);

}

