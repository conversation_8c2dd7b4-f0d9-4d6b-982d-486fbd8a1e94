package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmFetchPaymentOptionsRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauth;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParams;
import com.stpl.tech.master.payment.model.patymNew.PaytmStatusResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmValidateSSOTokenResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequestResponseWrapper;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateResponse;
import org.codehaus.jettison.json.JSONException;

import java.net.MalformedURLException;
import java.util.Map;

public interface PayTMNewPaymentService {

    String getSignature(PaytmParams paytmParams) throws Exception;

    PaytmParamResponse initiatePaytmPayment(PaytmParams paytmParams) throws Exception;

    PaytmCreateRequest createRequestDineIn(OrderPaymentRequest order)
        throws PaymentFailureException, DataNotFoundException;

    PaytmOauth getPaytmOauth(PaytmOauthRequest request) throws MalformedURLException;

    PaytmOauthResponse getPaytmOauth2V3(PaytmOauthRequest request);

    boolean revokePaytmToken(PaytmOauthRequest request);

    PaytmValidateSSOTokenResponse validatePaytmSSOToken(String token);

    String getPaytmAccessToken(PaytmFetchPaymentOptionsRequest request);

    String fetchPaytmPaymentOptions(PaytmFetchPaymentOptionsRequest request);

    String fetchPaytmPaymentOptionsV2(PaytmFetchPaymentOptionsRequest request);

    PaytmStatusResponse getPayTmPaymentStatus(String orderId) throws Exception;

    PaytmCreateRequestResponseWrapper createPaymentRequest(OrderPaymentRequest order, Map<String,String> map) throws Exception;

    boolean isPaymentSuccessFul(PaytmCreateResponse response);


    OrderPaymentDetail handleDisassociatedPayment(OrderPaymentDetail paymentDetail) throws PaymentFailureException;

    boolean validateResponse(PaytmCreateResponse response, boolean verifyPaytmStatus);

    Map updatePaytmResponse(PaytmCreateResponse response, boolean verifyPaytmStatus);

    Map updatePaytmResponseWithoutVerification(PaytmCreateResponse response);

    OrderPayment refundRequest(OrderPayment request) ;
}
