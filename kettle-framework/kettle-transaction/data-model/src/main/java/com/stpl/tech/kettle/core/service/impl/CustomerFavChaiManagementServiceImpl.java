package com.stpl.tech.kettle.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.core.service.CustomerFavChaiManagementService;
import com.stpl.tech.kettle.core.service.FavChaiCustomizationManagementService;
import com.stpl.tech.kettle.customer.dao.CustomerFavChaiMappingDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.CustomerFavChaiDao;
import com.stpl.tech.kettle.data.dao.CustomerFavChaiManagementDao;
import com.stpl.tech.kettle.data.dao.FavChaiCustomizationDetailDao;
import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.data.model.FavChaiCustomizationDetail;
import com.stpl.tech.kettle.domain.model.CustomerFavChaiMappingVO;
import com.stpl.tech.kettle.domain.model.FavouriteChai;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiRequest;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiResponse;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiResponseStatus;
import com.stpl.tech.kettle.domain.model.SelectedOrderItem;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class CustomerFavChaiManagementServiceImpl implements CustomerFavChaiManagementService {


    private static final Logger LOG = LoggerFactory.getLogger(CustomerFavChaiManagementServiceImpl.class);
    private static  boolean isFavChaiSavedOrUpdated =false;
    private static final Integer MilkVariantId = 100234 ;
    private static final Integer  DineInDesiChai = 1375 ;

    @Autowired
    private CustomerFavChaiManagementDao customerFavChaiManagementDao;

    @Autowired
    private CustomerFavChaiDao customerFavChaiDao ;

    @Autowired
    private FavChaiCustomizationDetailDao favChaiCustomizationDetailDao;

    @Autowired
    private FavChaiCustomizationManagementService favChaiCustomizationManagementService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private EnvironmentProperties properties;

    @Autowired
    private CustomerFavChaiMappingDao favChaiMappingDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveCustomerFavChai(int customerId, SaveCustomerFavChaiRequest customerFavChaiRequest) {
        SelectedOrderItem newChaiOrderItemDetails = customerFavChaiRequest.getOrderItemDetails();
        List<Integer> eligibleSavedChaisList =  properties.getEligibleSavedChaisList();
        // first check this chai is eligible to save
        if (!eligibleSavedChaisList.contains(customerFavChaiRequest.getOrderItemDetails().getProductId()) || StringUtils.isEmpty(newChaiOrderItemDetails.getTagType())) {
            LOG.info("Not a valid save Chai for customerId {}", customerFavChaiRequest.getCustomerBasicInfo().getCustomerId());
            return false;
        }
        Integer saveChaiThreshold = properties.getNoOfSavedChaiLimit();


        // SAVED CHAIS
        List<CustomerFavChaiMapping> savedChais = customerFavChaiManagementDao.findAllActiveCustomerFavChai(customerFavChaiRequest.getCustomerBasicInfo().getCustomerId(), AppConstants.ACTIVE);

        List<CustomerFavChaiMapping> existingOldSavedChais = savedChais.stream()
                .filter(oldChai -> (
                        StringUtils.equalsIgnoreCase(oldChai.getTagType(), newChaiOrderItemDetails.getTagType())
                                || StringUtils.equalsIgnoreCase(oldChai.getTagType(), newChaiOrderItemDetails.getPreviousTagType())
                )).collect(Collectors.toList());

        if (!customerFavChaiRequest.isFavChaiMarked()) {
            inActiveOldChais(existingOldSavedChais);
//            savedChais.removeAll(existingOldSavedChais);
            return true;
        } else {
            List<CustomerFavChaiMapping> sameChais = chaiWithSameCustomization(savedChais, customerFavChaiRequest);

            if (!CollectionUtils.isEmpty(sameChais)) {
                // same chai exists
                if (!StringUtils.equalsIgnoreCase(newChaiOrderItemDetails.getTagType(), sameChais.get(0).getTagType())) {
                    // name different then change the existing one name
                    CustomerFavChaiMapping updatedChai = sameChais.get(0);
                    inActiveOldChais(existingOldSavedChais);
                    savedChais.removeAll(existingOldSavedChais);
                    updatedChai.setTagType(newChaiOrderItemDetails.getTagType());
                    updatedChai.setStatus(AppConstants.ACTIVE);
                    updatedChai.setIsUpdated(AppConstants.YES);
                    // save chai in dao
                    saveNewCustomerFavChaiMapping(customerFavChaiRequest, true);
                    return true;
                }
                // same name do nothing
            } else {
                //new chai
                inActiveOldChais(existingOldSavedChais);
//                savedChais.removeAll(existingOldSavedChais);
                List<CustomerFavChaiMapping> oldSavedChaisAboveThreshold = new ArrayList<>();
                List<CustomerFavChaiMapping> currentSavedChais = customerFavChaiManagementDao.findAllActiveCustomerFavChai(customerFavChaiRequest.getCustomerBasicInfo().getCustomerId(), AppConstants.ACTIVE);
                for (int index = saveChaiThreshold - 1; index < currentSavedChais.size(); index++) {
                    currentSavedChais.get(index).setStatus(AppConstants.IN_ACTIVE);
                    oldSavedChaisAboveThreshold.add(currentSavedChais.get(index));
//                    currentSavedChais.remove(currentSavedChais.get(index));
//                    savedChais.remove(currentSavedChais.get(index));
                }
                if (!CollectionUtils.isEmpty(oldSavedChaisAboveThreshold)) {
                    for (CustomerFavChaiMapping chaiToMarkInActive : currentSavedChais) {
                        customerFavChaiManagementDao.saveNewCustomerFavChaiMapping(chaiToMarkInActive);
                    }
                }
                return saveNewCustomerFavChaiMapping(customerFavChaiRequest, false);
            }
        }
        return false;
        //Step -1 : Check if CustomerFavChaiMapping exists to so see if customer has previously saved fav chai
//        boolean isUpdated = false;
//        List<CustomerFavChaiMapping> matchingCustomerFavChaiMapping =new ArrayList<>();
//
//        if (Objects.nonNull(customerFavChaiMappings) && !customerFavChaiMappings.isEmpty()) {
////            Case when a saved chai for that consumeType and TagType already exists
//            for (CustomerFavChaiMapping customerFavChaiMapping : customerFavChaiMappings) {
//                if (Objects.nonNull(customerFavChaiMapping.getConsumeType()) && Objects.nonNull(customerFavChaiRequest.getOrderItemDetails().getConsumeType())) {
//                    if (customerFavChaiMapping.getConsumeType().equalsIgnoreCase(customerFavChaiRequest.getOrderItemDetails().getConsumeType()) && customerFavChaiMapping.getTagType().equalsIgnoreCase(customerFavChaiRequest.getOrderItemDetails().getTagType())) {
//                        matchingCustomerFavChaiMapping.add(customerFavChaiManagementDao.findCustomerFavChaiMappingByTagTypeAndConsumeTypeAndStatus(customerId,customerFavChaiMapping.getConsumeType(),customerFavChaiMapping.getTagType(),AppConstants.ACTIVE));
//                    }
//                } else if (Objects.nonNull(customerFavChaiMapping.getTagType()) && Objects.nonNull(customerFavChaiRequest.getOrderItemDetails().getTagType())) {
//                    if (customerFavChaiMapping.getTagType().equalsIgnoreCase(customerFavChaiRequest.getOrderItemDetails().getTagType())) {
//                        matchingCustomerFavChaiMapping.add(customerFavChaiManagementDao.findCustomerFavChaiMappingByTagTypeAndStatus(customerId, customerFavChaiMapping.getTagType(),AppConstants.ACTIVE));
//                    }
//                }
//            }
//            if(Objects.nonNull(matchingCustomerFavChaiMapping) && !matchingCustomerFavChaiMapping.isEmpty()){
//                for(CustomerFavChaiMapping mapping :matchingCustomerFavChaiMapping){
//                    updateExistingCustomerFavChaiMapping(mapping);
//                    isUpdated=true;
//                }
//            }else {
//                saveNewCustomerFavChaiMapping(orderItemDetails,customerId,new CustomerFavChaiMapping(),isUpdated,customerFavChaiRequest);
//            }
//        }else {
//            saveNewCustomerFavChaiMapping(orderItemDetails,customerId,null,isUpdated,customerFavChaiRequest);
//        }
//        return saveFavChaiCustomizationDetail(customerId,orderItemDetails,customerFavChaiRequest);
    }

    private void inActiveOldChais(List<CustomerFavChaiMapping> oldChais) {
        for (CustomerFavChaiMapping customerFavChaiMapping : oldChais) {
            customerFavChaiMapping.setStatus(AppConstants.IN_ACTIVE);
            customerFavChaiManagementDao.saveNewCustomerFavChaiMapping(customerFavChaiMapping);
        }

    }

    private List<CustomerFavChaiMapping> chaiWithSameCustomization(List<CustomerFavChaiMapping> savedChais, SaveCustomerFavChaiRequest newChai) {
        List<CustomerFavChaiMapping> sameChais = new ArrayList<>();
        for (CustomerFavChaiMapping customerFavChaiMapping : savedChais) {
            if (validateCurrentAndExistingCustomerFavChai(customerFavChaiMapping, newChai)) {
                sameChais.add(customerFavChaiMapping);
            }
        }
        return sameChais;
    }

    private boolean validateCurrentAndExistingCustomerFavChai(CustomerFavChaiMapping existingSavedChai, SaveCustomerFavChaiRequest currentChai) {
        CustomerFavChaiMapping newCurrentChai = DataConverter.convert(masterDataCache, currentChai);
        if (currentChai.equals(existingSavedChai)) {
            List<FavChaiCustomizationDetail> existingFavChaiCustomizationDetailList = existingSavedChai.getFavChaiCustomizationDetailList();
            List<FavChaiCustomizationDetail> currentFavChaiCustomizationDetailList = newCurrentChai.getFavChaiCustomizationDetailList();
            if (Objects.nonNull(existingFavChaiCustomizationDetailList) && Objects.nonNull(currentFavChaiCustomizationDetailList) && existingFavChaiCustomizationDetailList.size() == currentFavChaiCustomizationDetailList.size()) {
                Set<String> existingCustomizationProductIds = existingFavChaiCustomizationDetailList.stream().map(favChaiCustomizationDetail -> favChaiCustomizationDetail.getName().trim().toLowerCase()).collect(Collectors.toSet());
                Set<String> currentCustomizationProductIds = currentFavChaiCustomizationDetailList.stream().map(favChaiCustomizationDetail -> favChaiCustomizationDetail.getName().trim().toLowerCase()).collect(Collectors.toSet());
                return existingCustomizationProductIds.containsAll(currentCustomizationProductIds);
            }
        }
        return false;
    }

    private SaveCustomerFavChaiResponse saveFavChaiCustomizationDetail(int customerId, SelectedOrderItem orderItemDetails, SaveCustomerFavChaiRequest customerFavChaiRequest) {
        CustomerFavChaiMapping latestCustomerFavChaiMapping = null;
        if (AppConstants.DINE_IN.equalsIgnoreCase(orderItemDetails.getSourceName())) {
            latestCustomerFavChaiMapping = customerFavChaiManagementDao.findLastCustomerFavChaiMappingByTagType(customerId, orderItemDetails.getTagType());
        } else {
            latestCustomerFavChaiMapping = customerFavChaiManagementDao.findLastCustomerFavChaiMapping(customerId, orderItemDetails.getTagType(), orderItemDetails.getConsumeType());
        }
        Integer customizationId = Objects.nonNull(latestCustomerFavChaiMapping) && Objects.nonNull(latestCustomerFavChaiMapping.getCustomizationId()) ? latestCustomerFavChaiMapping.getCustomizationId() : 0;
//      save customization Detail only if active customer fav Chai Mapping --->
        if (customerFavChaiRequest.isFavChaiMarked()) {
            boolean isSuccess = favChaiCustomizationManagementService.saveFavChaiCustomizationDetail(customizationId, orderItemDetails.getProductId(), orderItemDetails, customerId, latestCustomerFavChaiMapping, customerFavChaiRequest);
            isFavChaiSavedOrUpdated = AppUtils.getStatus(AppUtils.getIsFavChaiSaved(isSuccess)?AppConstants.YES:AppConstants.NO);
            return getSaveCustomerFavChaiResponseStatus(customerFavChaiRequest.isFavChaiMarked(), latestCustomerFavChaiMapping, customerFavChaiRequest,isSuccess);
        } else {
            isFavChaiSavedOrUpdated = true;
            return getSaveCustomerFavChaiResponseStatus(customerFavChaiRequest.isFavChaiMarked(), latestCustomerFavChaiMapping, customerFavChaiRequest,true);// case when fav chai marked inactive , in this case we dont have to store customization details , just mark the existing chai false , so by default isSuccess is true
        }
    }

    private void updateExistingCustomerFavChaiMapping(CustomerFavChaiMapping customerFavChaiMapping){
        customerFavChaiMapping.setStatus(AppConstants.IN_ACTIVE);
        try{
            customerFavChaiManagementDao.update(customerFavChaiMapping);
        }catch(Exception e){
            LOG.error("Error while updating customer fav chai mapping for customerId {} and exception is  ::::::::::{}",customerFavChaiMapping.getCustomerId(),e);
        }
    }
    public boolean saveNewCustomerFavChaiMapping(SaveCustomerFavChaiRequest customerFavChaiRequest,boolean isUpdated){
        if(customerFavChaiRequest.isFavChaiMarked()) {
            CustomerFavChaiMapping newCustomerFavChaiMappping = new CustomerFavChaiMapping();
            newCustomerFavChaiMappping = DataConverter.convert(masterDataCache,customerFavChaiRequest);
            newCustomerFavChaiMappping.setIsUpdated(AppConstants.getValue(isUpdated));
            try {
                customerFavChaiManagementDao.saveNewCustomerFavChaiMapping(newCustomerFavChaiMappping);
                return true;
            } catch (Exception e) {
                LOG.error("Error while saving new customer fav chai mapping for customerId::{} and exception ::::::::::{}", newCustomerFavChaiMappping.getCustomerId(), e);
            }
        }
        return false;
    }



    private SaveCustomerFavChaiResponse getSaveCustomerFavChaiResponseStatus(boolean isCustomerFavChaiSaved, CustomerFavChaiMapping latestCustomerFavChaiMapping, SaveCustomerFavChaiRequest customerFavChaiRequest, boolean isSuccess) {
        if(!AppConstants.DINE_IN.equalsIgnoreCase(customerFavChaiRequest.getOrderItemDetails().getSourceName())){
            SaveCustomerFavChaiResponseStatus saveCustomerFavChaiResponseStatus;
            SaveCustomerFavChaiResponse saveCustomerFavChaiResponse = new SaveCustomerFavChaiResponse();
            if(isCustomerFavChaiSaved){
                if(AppConstants.YES.equals(latestCustomerFavChaiMapping.getIsUpdated())){
                    String updateStatusResponse = AppConstants.updateStatus+"_FOR_"+latestCustomerFavChaiMapping.getConsumeType().toUpperCase();
                    saveCustomerFavChaiResponseStatus = SaveCustomerFavChaiResponseStatus.getFavChaiResponse(updateStatusResponse);
                }else{
                    String saveStatusResponse = AppConstants.saveStatus+"_FOR_"+latestCustomerFavChaiMapping.getConsumeType().toUpperCase();
                    saveCustomerFavChaiResponseStatus = SaveCustomerFavChaiResponseStatus.getFavChaiResponse(saveStatusResponse);
                }
            }else if(!isCustomerFavChaiSaved && isSuccess) {//to mark inactive
                String response = AppConstants.markedInactive+"_FOR_"+latestCustomerFavChaiMapping.getConsumeType();
                saveCustomerFavChaiResponseStatus = SaveCustomerFavChaiResponseStatus.getFavChaiResponse(response);
            }else {
                String failedResponse = AppConstants.FAILED;
                saveCustomerFavChaiResponseStatus=SaveCustomerFavChaiResponseStatus.getFavChaiResponse(failedResponse);
            }
            saveCustomerFavChaiResponse.setCustomerId(latestCustomerFavChaiMapping.getCustomerId());
            saveCustomerFavChaiResponse.setProductId(latestCustomerFavChaiMapping.getProductId());
            saveCustomerFavChaiResponse.setCreatedAt(Objects.nonNull(latestCustomerFavChaiMapping.getCreatedAt())?latestCustomerFavChaiMapping.getCreatedAt():null);
            saveCustomerFavChaiResponse.setCustomizationId(Objects.nonNull(latestCustomerFavChaiMapping.getCustomizationId())?latestCustomerFavChaiMapping.getCustomizationId():null);
            saveCustomerFavChaiResponse.setSaveCustomerFavChaiResponseStatus(saveCustomerFavChaiResponseStatus);
            return saveCustomerFavChaiResponse;
        }
        return null ;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<CustomerFavChaiMappingVO> getAllActiveCustomerFavChaiMappings(int customerId) {
//        List<CustomerFavChaiMappingVO> customerFavChaiMappingVOList = new ArrayList<>();
        List<CustomerFavChaiMapping> customerFavChaiMappings = customerFavChaiManagementDao.findAllActiveCustomerFavChai(customerId,AppConstants.ACTIVE);
        List<CustomerFavChaiMapping> invalidChais = new ArrayList<>();
        List<CustomerFavChaiMappingVO> savedChaiList = new ArrayList<>();

        List<Integer> eligibleSavedChaisList =  properties.getEligibleSavedChaisList();
        Integer savedChaiThreshold = properties.getNoOfSavedChaiLimit();

        customerFavChaiMappings.forEach(chai -> {
            if(Objects.nonNull(chai.getFavChaiCustomizationDetailList()) && chai.getFavChaiCustomizationDetailList().size()> AppConstants.ONE ){
                if( eligibleSavedChaisList.contains(chai.getProductId()) && savedChaiList.size() < savedChaiThreshold ){
                    if(StringUtils.isNotBlank(chai.getDimension())){
                        chai.setDimension(AppConstants.DIMESION_REGULAR_STRING);
                        savedChaiList.add(DataConverter.convert(chai));
                    }
                }else{
                    chai.setStatus(AppConstants.IN_ACTIVE);
                    invalidChais.add(chai);
                }
            }
        });
        if(!CollectionUtils.isEmpty(invalidChais))
        {
            LOG.info("Marked Inactive the chai's more than the save chai threshold value for customer id {}", customerId);
            for(CustomerFavChaiMapping invalidChai : invalidChais)
            {
                customerFavChaiManagementDao.saveNewCustomerFavChaiMapping(invalidChai);
            }
        }
        return savedChaiList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<FavouriteChai> convertActiveFavChaiMappingsToFavChaiDineIN(int customerId, int unitId) {
        List<CustomerFavChaiMappingVO>customerFavChaiMappingVOList= null;
        List<FavouriteChai> favChais = new ArrayList<>();
        try{
            customerFavChaiMappingVOList = getAllActiveCustomerFavChaiMappings(customerId);
        }catch(Exception e){
                LOG.error("Exception thrown while getting active fav chai mappings for customerId::{} and error is ::{} ", customerId,e);
        }
        if(Objects.nonNull(customerFavChaiMappingVOList) && !customerFavChaiMappingVOList.isEmpty()){
            for(CustomerFavChaiMappingVO customerFavChaiMappingVO : customerFavChaiMappingVOList){
                favChais.add(DataConverter.convert(customerFavChaiMappingVO,masterDataCache,unitId));
            }
            int i = 1;
            for (FavouriteChai favouriteChai : favChais) {
                favouriteChai.getCartOrderItem().setId(i);
                i++;
            }
        }
        return favChais;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveAndUpdateFavChaiFromDineIn(FavouriteChai favouriteChai) {
        //Step 1--> Convert FavChai DineIn obj to kettle Customer Fav Chai Request
        SaveCustomerFavChaiRequest customerFavChaiRequest =new SaveCustomerFavChaiRequest() ;
        try{
            customerFavChaiRequest= DataConverter.convert(favouriteChai);
        }catch(Exception e ) {
            LOG.error("Exception while converting favChaiDineIn to kettle SaveCustomerFvaCHaiRequest for customerId :{} and error is :{}", favouriteChai.getCustomerId(), e);
        }
       //Step 2 : save Customer Fav Chai
        return saveUpdateDeleteCustomerFavChai(favouriteChai.getCustomerId(), customerFavChaiRequest);
    }

    @Override
    public boolean updateIncorrectFavChaiFromDineIn() {
        //Step 1 : Get all 1375 desi Chais
        List<CustomerFavChaiMapping> customerFavChaiMappingList =customerFavChaiDao.findByProductIdAndSourceIdAndStatus(DineInDesiChai,AppConstants.DINE_IN_CHANNEL_PARTNER_CODE, AppConstants.ACTIVE);
        //Step 2 : Find the wrong customizations entry
        List<FavChaiCustomizationDetail> favChaiCustomizationDetailList = favChaiCustomizationDetailDao.findAllBySourceAndUomAndTypeAndDimensionIsNullAndCustomerFavChaiMapping("SCM", "L", "VARIANT", customerFavChaiMappingList);

        Map<String, Integer> desiChaiMilkMap = AppUtils.getDesiChaiMilkMap();
        //Step 3 : Update 100234
        try{
            favChaiCustomizationDetailList.stream().filter(favChaiCustomizationDetail -> favChaiCustomizationDetail.getProductId()==MilkVariantId).forEach(favChaiCustomizationDetail -> {
                customerFavChaiMappingList.stream().filter(customerFavChaiMapping -> Objects.equals(customerFavChaiMapping.getCustomizationId(), favChaiCustomizationDetail.getCustomerFavChaiMapping().getCustomizationId())).forEach(customerFavChaiMapping -> {
                    Integer productId  = desiChaiMilkMap.get(favChaiCustomizationDetail.getName());
                    customerFavChaiMapping.setProductName(masterDataCache.getProduct(productId).getName());
                    customerFavChaiMapping.setProductId(masterDataCache.getProduct(productId).getId());
                    RecipeDetail recipe = recipeCache.getRecipe(new ProductRecipeKey(productId, customerFavChaiMapping.getDimension(), customerFavChaiMapping.getRecipeProfile()));
                    if(Objects.nonNull(recipe)){
                        customerFavChaiMapping.setRecipeId(recipe.getRecipeId());
                    }
                });
            });
        }catch (Exception e){
            LOG.error("Exception while updating firlds of customer fav chai mapping List :::::::", e );
        }

        // Remove milk Variant from Fav Chai Customization Detail

        List<Integer> milkVariantCustomizations = favChaiCustomizationDetailList.stream().filter(favChaiCustomizationDetail -> favChaiCustomizationDetail.getProductId()==MilkVariantId)
                .map(FavChaiCustomizationDetail::getFavChaiCustomizationDetailId).collect(Collectors.toList());

        //Update updated Customer fav Chai
        try{
            customerFavChaiDao.saveAll(customerFavChaiMappingList);
            customerFavChaiDao.flush();
        }catch(Exception e ){
            LOG.error("Exeption while saving updated dine in saved chais for customerIds :::::::::{}", new Gson().toJson(customerFavChaiMappingList.stream().map(CustomerFavChaiMapping::getCustomerId).collect(Collectors.toList())));
            return false ;
        }

        // Delete milk variant customizations
        try{
            favChaiCustomizationDetailDao.deleteAllById(milkVariantCustomizations);
        }catch(Exception e ){
            LOG.error("Exeption while saving updated dine in saved chais for customerIds :::::::::{}", new Gson().toJson(customerFavChaiMappingList.stream().map(CustomerFavChaiMapping::getCustomerId).collect(Collectors.toList())));
            return false ;
        }
        return true ;
    }

    private boolean saveUpdateDeleteCustomerFavChai(Integer customerId, SaveCustomerFavChaiRequest customerFavChaiRequest) {
        try{
            saveCustomerFavChai(customerId,customerFavChaiRequest);
        }catch(Exception e){
            LOG.error("Exception while saving Customer Fav Chai :::::", e);
        }
        return isFavChaiSavedOrUpdated;
    }

    @Override
    public void saveOrderCountOfSavedChai(Order order, Integer orderId) {
        try {
            Map<Integer, Integer> itemPreferenceMap = new HashMap<>();
            List<CustomerFavChaiMapping> favChaiMappings = new ArrayList<>();
            List<CustomerFavChaiMapping> mappings = favChaiMappingDao.findByCustomerIdAndStatus(order.getCustomerId(), AppConstants.ACTIVE);
            if (Objects.nonNull(order.getOrders())) {
                order.getOrders().stream().filter(item -> Objects.nonNull(item.getPreferenceDetail())
                        && Objects.nonNull(item.getPreferenceDetail().getPreferenceId())).map(orderItem ->
                        itemPreferenceMap.put(orderItem.getPreferenceDetail().getPreferenceId(),orderItem.getQuantity())
                ).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(mappings)) {
                for (CustomerFavChaiMapping mapping : mappings) {
                    if (itemPreferenceMap.containsKey(mapping.getCustomizationId())) {
                        if(Objects.isNull(mapping.getTotalOrderCount())){
                            mapping.setTotalOrderCount(0);
                        }
                        mapping.setTotalOrderCount(mapping.getTotalOrderCount() + itemPreferenceMap.get(mapping.getCustomizationId()));
                        favChaiMappings.add(mapping);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(favChaiMappings)) {
                favChaiMappingDao.saveAll(favChaiMappings);
            }
        } catch (Exception e) {
            LOG.error("Error while saving the order count of saved chai for order id ::::: {}", orderId, e);
        }
    }
}
