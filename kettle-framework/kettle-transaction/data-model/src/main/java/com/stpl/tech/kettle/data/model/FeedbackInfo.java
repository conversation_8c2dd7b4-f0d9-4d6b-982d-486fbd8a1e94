/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "FEEDBACK_INFO")
public class FeedbackInfo implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2861559093141406508L;
	
	private Integer feedbackInfoId;
	private int feedbackId;
	private int feedbackEventId;
	private String responseId;
	private String responseToken;
	private Integer feedbackRating;
	private FeedbackForm feedbackForm;
	private Date feedbackTime;

	public FeedbackInfo() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "FEEDBACK_INFO_ID", unique = true, nullable = false)
	public Integer getFeedbackInfoId() {
		return this.feedbackInfoId;
	}

	public void setFeedbackInfoId(Integer feedbackInfoId) {
		this.feedbackInfoId = feedbackInfoId;
	}

	@Column(name = "FEEDBACK_ID", nullable = false)
	public int getFeedbackId() {
		return feedbackId;
	}

	public void setFeedbackId(int feedbackId) {
		this.feedbackId = feedbackId;
	}
	
	@Column(name = "FEEDBACK_EVENT_ID", nullable = false)
	public int getFeedbackEventId() {
		return feedbackEventId;
	}

	public void setFeedbackEventId(int feedbackEventId) {
		this.feedbackEventId = feedbackEventId;
	}

	@Column(name = "RESPONSE_ID", nullable = true, length = 100)
	public String getResponseId() {
		return this.responseId;
	}

	public void setResponseId(String responseId) {
		this.responseId = responseId;
	}

	@Column(name = "RESPONSE_TOKEN", nullable = true, length = 100)
	public String getResponseToken() {
		return this.responseToken;
	}

	public void setResponseToken(String responseToken) {
		this.responseToken = responseToken;
	}

	@Column(name = "FEEDBACK_TIME", nullable = true, length = 19)
	public Date getFeedbackTime() {
		return feedbackTime;
	}

	
	public void setFeedbackTime(Date feedbackTime) {
		this.feedbackTime = feedbackTime;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FORM_ID", nullable = false)
	public FeedbackForm getFeedbackForm() {
		return feedbackForm;
	}

	public void setFeedbackForm(FeedbackForm feedbackForm) {
		this.feedbackForm = feedbackForm;
	}
	
	@Column(name = "FEEDBACK_RATING", nullable = true)
	public Integer getFeedbackRating() {
		return feedbackRating;
	}

	public void setFeedbackRating(Integer feedbackRating) {
		this.feedbackRating = feedbackRating;
	}
	
	
	
	
}