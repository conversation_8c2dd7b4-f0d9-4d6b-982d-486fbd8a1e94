/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.util.notification.AbstractTemplate;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;

import java.util.HashMap;
import java.util.Map;

public class SubscriptionReceipt extends AbstractTemplate {

	private SubscriptionInfo detail;

	private String basePath;

	private String fromEmail;

	private String toEmail;

	Map<String, Object> data = new HashMap<String, Object>();

	public SubscriptionReceipt(SubscriptionInfo detail, String basePath, String fromEmail, String toEmail) {
		super();
		this.detail = detail;
		this.basePath = basePath;
		this.fromEmail = fromEmail;
		this.toEmail = toEmail;
	}

	public SubscriptionInfo getDetail() {
		return detail;
	}

	public void setDetail(SubscriptionInfo detail) {
		this.detail = detail;
	}

	@Override
	public String getTemplatePath() {
		return "template/SubscriptionReceipt.html";
	}

	public Map<String, Object> getData() {
		// Build the data-model
		data.put("order", detail.getOrder());
		data.put("customer", detail.getCustomer());
		data.put("discountPercent",
				detail.getOrder().getTransactionDetail().getDiscountDetail() != null
						&& detail.getOrder().getTransactionDetail().getDiscountDetail().getDiscount() != null
								? detail.getOrder().getTransactionDetail().getDiscountDetail().getDiscount()
										.getPercentage()
								: 0.0d);
		data.put("discountValue",
				detail.getOrder().getTransactionDetail().getDiscountDetail() != null
						&& detail.getOrder().getTransactionDetail().getDiscountDetail().getDiscount() != null
								? detail.getOrder().getTransactionDetail().getDiscountDetail().getDiscount().getValue()
								: 0.0d);
		data.put("promotionalDiscount",
				detail.getOrder().getTransactionDetail().getDiscountDetail() != null
						&& detail.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer() != null
								? detail.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer()
								: 0.0d);
		data.put("isOrderCancelled", OrderStatus.CANCELLED.equals(detail.getOrder().getStatus()));
		data.put("isNonProdEnvironment", !AppUtils.isProd(detail.getEnv()));
		Address deliveryAddress = null;
		if (detail.getOrder().getDeliveryAddress() != null && detail.getCustomer().getAddresses() != null) {
			for (Address address : detail.getCustomer().getAddresses()) {
				if (detail.getOrder().getDeliveryAddress() == address.getId()) {
					deliveryAddress = address;
				}
			}
		}
		data.put("deliveryAddress", deliveryAddress);
		data.put("smsNotification",
				TransactionConstants.getValue(detail.getOrder().getSubscriptionDetail().isSmsNotification()));
		data.put("emailNotification",
				TransactionConstants.getValue(detail.getOrder().getSubscriptionDetail().isEmailNotification()));
		data.put("channelPartner", detail.getChannelPartner());
		data.put("settlements", detail.getOrder().getSettlements());
		data.put("subscriptionDays", TransactionUtils.getDeliveryDays(detail.getOrder().getSubscriptionDetail()));
		data.put("subscriptionDeliveryTimes",
				TransactionUtils.getDeliveryTimes(detail.getOrder().getSubscriptionDetail()));
		return data;
	}

	@Override
	public String getFilepath() {
		return basePath + "/" + "/subscriptions/" + "/SubscriptionReceipt-"
				+ detail.getOrder().getSubscriptionDetail().getSubscriptionId() + ".html";
	}

	public String getBasePath() {
		return basePath;
	}

	public String getFromEmail() {
		return fromEmail;
	}

	public String getToEmail() {
		return toEmail;
	}

}