package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.OrderFeedbackMetadata;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.master.core.exception.DataUpdationException;

public interface OrderNotificationService {
	
	public void createOrderNotificationData(OrderNotification orderNotification, OrderInfo info)
			throws DataUpdationException;

	public OrderFeedbackMetadata generateOrderFeedbackDetails(FeedbackSource source, OrderInfo orderInfo, boolean feedback,
															  boolean generateReceipt, OrderFeedbackMetadata orderFeedbackMetadata);

	public void generateOrderFeedbackNotification(OrderInfo info, OrderFeedbackMetadata orderFeedbackMetadata);

	void updateFeedbackEventStatus(OrderFeedbackMetadata orderFeedbackMetadata, boolean isOrderNotificationSent);
}
