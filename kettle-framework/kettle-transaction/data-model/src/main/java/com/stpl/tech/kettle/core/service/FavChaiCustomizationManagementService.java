package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiRequest;
import com.stpl.tech.kettle.domain.model.SelectedOrderItem;

public interface FavChaiCustomizationManagementService {
    boolean saveFavChaiCustomizationDetail(Integer customizationId, int productId, SelectedOrderItem orderItemDetails, int customerId, CustomerFavChaiMapping latestCustomerFavChaiMapping, SaveCustomerFavChaiRequest customerFavChaiRequest);
}
