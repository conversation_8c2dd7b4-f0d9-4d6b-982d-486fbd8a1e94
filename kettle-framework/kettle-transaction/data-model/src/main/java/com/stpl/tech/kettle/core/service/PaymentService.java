package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.data.model.StandaloneTransactionDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.payment.model.AGS.AGSCreateRequest;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMStatus;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SStatus;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatusChangeRequest;
import com.stpl.tech.master.payment.model.PaymentVO;
import com.stpl.tech.master.payment.model.ezetap.EzetapCreateRequest;
import com.stpl.tech.master.payment.model.ezetap.EzetapPaymentResponse;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentStatus;
import com.stpl.tech.master.payment.model.gpay.GPayQRResponse;
import com.stpl.tech.master.payment.model.ingenico.IngenicoQrResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmFetchPaymentOptionsRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauth;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParams;
import com.stpl.tech.master.payment.model.patymNew.PaytmStatusResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmValidateSSOTokenResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequestResponseWrapper;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmPaymentStatus;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiQrResponse;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiS2SResponse;
import com.stpl.tech.master.payment.model.razorpay.BasicTransactionInfo;
import com.stpl.tech.master.payment.model.razorpay.RazorPayCreateRequest;
import com.stpl.tech.master.payment.model.razorpay.RazorPayEventData;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentResponse;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface PaymentService {

	public RazorPayCreateRequest createRazorPayRequest(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;

	@Deprecated
	public PaymentRequest createRequest(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;

	public Map updateRazorPayResponse(RazorPayPaymentResponse response,Integer brandId) throws PaymentFailureException;

    Map updateRazorPayResponseWithoutVerification(RazorPayPaymentResponse response)
            throws PaymentFailureException;

    public Map updateEzetapResponse(EzetapPaymentResponse response) throws PaymentFailureException;


	Map updatePaytmResponse(PaytmCreateResponse response, boolean verifyPatymStatus) throws PaymentFailureException;

    Map updatePaytmResponse(PaytmCreateResponse response) throws PaymentFailureException;

    Map updatePaytmResponseWithoutVerification(PaytmCreateResponse response) throws PaymentFailureException;

    PaytmStatusResponse getPaytmStatusResponse(String orderId) throws Exception;

    public void createPaymentEvent(RazorPayEventData event);

	public RazorPayPaymentResponse fetchRazorPayPayment(String paymentId) throws PaymentFailureException;

    public EzetapCreateRequest createEzetapRequest(OrderPaymentRequest order)
            throws PaymentFailureException, DataNotFoundException;

    public PaytmCreateRequest createPaytmRequest(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;

//    public IngenicoCreateRequest createIngenicoRequest(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    RazorPayPaymentResponse fetchResponseByRazorPayByPartnerOrderId(String partnerOrderId,Integer brandId) throws PaymentFailureException;

    public Boolean cancelPayment(PaymentStatusChangeRequest cancel);

	public Boolean failurePayment(PaymentStatusChangeRequest failure);

	public Boolean checkPaymentStatus(PaymentVO payment);

	public OrderPayment refundPayment(OrderPayment refund) throws IOException, PaymentFailureException;

	public OrderPayment getDisassociatedPayment(String contact) throws PaymentFailureException;

	public List<OrderPayment> getPendingRefunds();

	public OrderPayment refundPayment(Integer orderId) throws IOException, PaymentFailureException;

	public OrderPayment getPaymentStatus(Integer orderId) throws IOException, PaymentFailureException;

	public OrderPayment refundByPaymentId(Integer paymentDetailId) throws IOException, PaymentFailureException;

	public PaytmCreateRequest getPayTMQRCodeIdForKIOSK(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;

	public PaytmPaymentStatus checkKIOSKPaytmQRPaymentStatus(String orderID) throws PaymentFailureException;

	public boolean refundKIOSKPaytmQRPaymentAmount(String orderId, BigDecimal amountToRefund, String refundReason) throws PaymentFailureException;

    public IngenicoQrResponse validateIngenicoCallback(String response) throws PaymentFailureException;

    public void initiateAutoRefunds();

	public void markTransactionCancel(OrderPaymentRequest order) throws Exception;

	public PaytmUpiQrResponse getPayTmUpiQRCodeId(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;

	public void updatePaytmUpiStatus(PaytmUpiS2SResponse response) throws PaymentFailureException;

	public OrderPayment refundPaytmUpiQR(OrderPayment paymentRefundRequest) throws PaymentFailureException;

	AGSCreateRequest createAGSRequest(OrderPaymentRequest order)throws PaymentFailureException;

	Map updateAGSPaymentCMStatus(AGSPaymentCMStatus response) throws PaymentFailureException;

	AGSPaymentS2SResponse updateAGSPaymentS2SStatus(AGSPaymentS2SStatus response) throws PaymentFailureException;

	AGSPaymentCMResponse checkAGSPaymentS2SStatus(String externalOrderId) throws PaymentFailureException;

	public StandaloneTransactionDetail createStandalonePaymentEvent(RazorPayEventData data);

	boolean setNotificationDetail(String paymentId, String smsType, String emailType);

	void sendStandaloneNotification(String paymentId, String event, BasicTransactionInfo info);

	public void updateStatusOfDisassociatedPayments(String contact);

	PaytmCreateRequestResponseWrapper createPaytmRequestForDineIn(OrderPaymentRequest order, String ssoToken)
			throws Exception;

	PaytmParamResponse initiatePaytmPayment(PaytmParams paytmParams) throws Exception;

    PaytmOauth getPaytmOauth(PaytmOauthRequest paytmOauthRequest) throws Exception;

    PaytmOauthResponse getPaytmOauth2V3(PaytmOauthRequest paytmOauthRequest);

    boolean revokePaytmToken(PaytmOauthRequest paytmOauthRequest);

	PaytmValidateSSOTokenResponse validatePaytmSSOToken(String ssoToken);

	String getPaytmAccessToken(PaytmFetchPaymentOptionsRequest request) throws Exception;

    String fetchPaytmPaymentOptions(PaytmFetchPaymentOptionsRequest request);

	String fetchPaytmPaymentOptionsV2(PaytmFetchPaymentOptionsRequest request);

	public GPayQRResponse getGPayQRCode(OrderPaymentRequest order) throws PaymentFailureException;

	GPayPaymentStatus checkGPayQRPaymentStatus(OrderPaymentRequest request) throws PaymentFailureException;
}
