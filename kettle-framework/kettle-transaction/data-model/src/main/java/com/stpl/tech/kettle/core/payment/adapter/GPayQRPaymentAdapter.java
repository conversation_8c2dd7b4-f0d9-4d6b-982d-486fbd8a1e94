package com.stpl.tech.kettle.core.payment.adapter;

import com.stpl.tech.kettle.core.payment.PaymentAdapter;
import com.stpl.tech.kettle.core.service.GooglePaymentService;
import com.stpl.tech.kettle.core.service.impl.GooglePaymentServieImpl;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.gpay.GPayQRResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class GPayQRPaymentAdapter extends PaymentAdapter<OrderPaymentRequest,
        GPayQRResponse> {

    @Autowired
    GooglePaymentService googlePaymentService;

    @Override
    public GPayQRResponse createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {
        return googlePaymentService.getGPayQRCode(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification,Integer brandId) {
        return null;
    }
}
