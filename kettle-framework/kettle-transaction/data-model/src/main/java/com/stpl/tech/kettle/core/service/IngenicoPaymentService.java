package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.ingenico.IngenicoCreateRequest;
import com.stpl.tech.master.payment.model.ingenico.IngenicoQrRequest;
import com.stpl.tech.master.payment.model.ingenico.IngenicoQrResponse;
import com.stpl.tech.master.payment.model.ingenico.IngenicoResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.util.EnvType;

import java.math.BigDecimal;

/**
 * Created by shikhar on 15/7/19.
 */
public interface IngenicoPaymentService {

    public IngenicoQrResponse createRequest(OrderPaymentRequest order) throws PaymentFailureException;

    public PaymentStatus getPaymentStatus(EnvType type, OrderPaymentDetail paymentDetail) throws PaymentFailureException;

    public OrderPayment refundRequest(EnvType envType, OrderPayment request) throws PaymentFailureException;

    public IngenicoQrResponse getValidatedResponse(String response) throws PaymentFailureException;

    public IngenicoQrResponse createIngenicoQRForCRM(String ingenicoEncryptedRequest, String merchantCode) throws Exception;

    public void updatePaymentModeForOrder(OrderPaymentRequest order) throws Exception;

    public IngenicoQrResponse checkIngenicoPaymentStatus(OrderPaymentRequest order) throws Exception;

    Boolean checkIngenicoPaymentStatus(OrderPaymentDetail paymentDetail);

    IngenicoQrResponse validateIngenicoCallback(String response) throws PaymentFailureException;
}
