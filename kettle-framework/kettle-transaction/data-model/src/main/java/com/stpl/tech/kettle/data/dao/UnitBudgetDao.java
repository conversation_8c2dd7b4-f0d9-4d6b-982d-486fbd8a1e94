/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.core.CalculationType;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.CalculationStatus;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.DirectCost;
import com.stpl.tech.kettle.core.data.budget.vo.ElectricityAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.InventoryAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.RentAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.RevenueData;
import com.stpl.tech.kettle.core.data.budget.vo.SalesCommissionData;
import com.stpl.tech.kettle.core.data.budget.vo.WastageAggregate;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetExceededData;
import com.stpl.tech.kettle.data.model.UnitBudgetoryDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureAggregateDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface UnitBudgetDao extends AbstractDao {

	public Map<Integer, RevenueData> getRevenueData(Date businessDate, List<Integer> unitIds);

	public Map<Integer, SalesCommissionData> getSalesCommissionData(Date businessDate, List<Integer> unitIds);

	public Map<Integer, InventoryAggregate> getInventoryAggregate(Date businessDate, List<Integer> unitIds);

	public Map<Integer, WastageAggregate> getWastageAggregate(Date businessDate, List<Integer> unitIds);

	public Map<Integer, ConsumablesAggregate> getConsumablesAggregate(Date businessDate, List<Integer> unitIds);

	public Map<Integer, ExpenseAggregate> getExpenseAggregate(Date businessDate, List<Integer> unitIds);

	public Map<Integer, ElectricityAggregate> getElectricityAggregate(Date businessDate, List<Integer> unitIds);

	public Map<Integer, DirectCost> getDirectCost(Date businessDate, List<Integer> unitIds);

	public Map<Integer, RentAggregate> getRentAggregate(Date businessDate, List<Integer> unitIds);

	/**
	 * @param unitId
	 * @param businessDate 
	 * @return
	 */
	public BigDecimal getCreditCardPercentageForCurrentMonth(int unitId, Date businessDate);

	/**
	 * @param budgetDetail
	 */
	public int savePnL(BudgetDetail budgetDetail);

	/**
	 * @param pnlDetailId
	 */
	public void changePnLStatus(int pnlDetailId, CalculationStatus status);

	public int getPnlDetailFromUnitAndDayCloseId(int unitId, int dayClosureId);

	/**
	 * @param unitId
	 * @param month
	 * @param year
	 * @return
	 */
	public UnitBudgetoryDetail getUnitBudgetDetail(int unitId, int month, int year);
	
	public List<UnitExpenditureDetail> getUnitExpenditureDetail(Date businessDate, CalculationStatus status, CalculationType calculation);

	/**
	 * @param detailId
	 * @param detail
	 */
	public void saveKettleCalculatedExpenses(int detailId, BudgetDetail detail);

	public UnitExpenditureDetail getExpenditureForUnit(Date businessDate, Integer unitId, CalculationStatus status, CalculationType type);

	public UnitExpenditureAggregateDetail getAggregateForUnit(Integer detailId);

    void updatePnLInVoucherData(int detailId, Date businessDate, List<VoucherData> voucherDataList) throws DataUpdationException;

    /**
	 * @param unitId
	 * @param status
	 * @param calculation
	 * @return
	 */
	public UnitExpenditureDetail getLatestUnitExpenditureDetail(int unitId, int month, int year, CalculationStatus status,
			CalculationType calculation);

	/**
	 * @param expenseDetail
	 * @return
	 */
	public int savePnL(UnitExpenditureDetail expenseDetail);
	public List<Integer> getPnlUnitIds(Date d, CalculationStatus pendingSumoCalculation, CalculationType current);

	/**
	 * @param businessDate
	 * @return
	 */
	public List<UnitExpenditureDetail> getAllUnitExpenditureDetail(Date businessDate, CalculationType type);

	/**
	 * @param unitId
	 * @param businessDate
	 * @param calculation
	 * @return
	 */
	public List<UnitExpenditureDetail> getUnitExpenditureDetail(int unitId, Date businessDate,
			CalculationType calculation);

	/**
	 * @param unitId
	 * @param businessDate
	 * @return
	 */
	public List<UnitExpenditureDetail> getAllUnitExpenditureDetail(int unitId, Date businessDate);

	public UnitExpenditureDetail saveSCMPnL(UnitExpenditureDetail d,BudgetDetail budgetDetail,  CalculationStatus status, CalculationType type);
	
	public List<UnitBudgetExceededData> getTodayEntry(UnitBudgetExceededData overflow, Date currentDate);

	List<UnitExpenditureDetail> getExpenditureForUnit(List<Date> businessDates, Integer unitId,
			CalculationStatus status, CalculationType type);

	List<UnitExpenditureAggregateDetail> getExpenditureAggregateForUnit(List<Date> businessDates, Integer unitId,
													  CalculationStatus status, CalculationType type);

	public UnitExpenditureAggregateDetail getExpenditureAggregateForUnit(Date businessDates, Integer unitId,
																		 CalculationStatus status, CalculationType type);

	public List<Integer> getUnitsWithFinalizedEntries(Date businessDate);

	public List<Integer> getUnitsWithClosedEntries(Date businessDate);

	public List<CashCardDetail> getCashCardsForUnitForDay(int unitId, Date businessDate);
	public UnitExpenditureAggregateDetail getLatestUnitExpenditureAggregateDetail(int unitId, int month, int year,
																				  CalculationStatus status, CalculationType calculation);

    List<UnitExpenditureAggregateDetail> getAllUnitExpenditureAggregateDetail(Integer unitId, Date businessDate);

	void changePnLAggregateStatus(int pnlDetailId, CalculationStatus cancelled);

	boolean savePnL(Collection<BudgetDetail> budgets);

	void cancelAllUnitExpenditureAggregateDetail(List<Integer> unitId, Date businessDate);

	void cancelAllUnitExpenditureDetail(List<Integer> unitId, Date businessDate);

}
