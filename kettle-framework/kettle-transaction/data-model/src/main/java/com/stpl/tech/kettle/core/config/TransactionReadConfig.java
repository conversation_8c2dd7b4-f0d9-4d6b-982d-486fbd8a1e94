/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.core.config;

import com.google.common.base.Preconditions;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.util.AppUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Properties;

@Configuration
@EnableTransactionManagement
@ComponentScan({"com.stpl.tech.kettle.stock", "com.stpl.tech.kettle", "com.stpl.tech.kettle.core.payment.adapter"})
@EnableJpaRepositories(basePackages = {"com.stpl.tech.kettle.data.dao.impl", "com.stpl.tech.kettle.webengage.data.dao.impl","com.stpl.tech.kettle.clevertap.data.dao.impl", "com.stpl.tech.kettle.stock.dao.impl" },
        entityManagerFactoryRef = "TransactionReadDataSourceEMFactory", transactionManagerRef = "TransactionReadDataSourceTM")
public class TransactionReadConfig {


    @Autowired
    private EnvironmentProperties environmentProperties;

    public TransactionReadConfig() {
        super();
    }

    @Bean(name = "TransactionReadDataSourceEMFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory() {
        final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource());
        em.setPackagesToScan(new String[]{"com.stpl.tech.kettle.data.model", "com.stpl.tech.kettle.webengage.data.model",
                "com.stpl.tech.kettle.data.expense.model","com.stpl.tech.kettle.clevertap.data.model"});

        final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        em.setJpaProperties(additionalProperties());
        em.setPersistenceUnitName("TransactionReadDataSourcePUName");
        return em;
    }

    @Bean(name = "TransactionReadDataSource")
    public DataSource dataSource() {
        final DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName(Preconditions.checkNotNull(environmentProperties.getEnv().getProperty(AppUtils.isProd(environmentProperties.getEnvironmentType()) ? "kettle.prod.read.jdbc.driverClassName" : "jdbc.driverClassName")));
        dataSource.setUrl(Preconditions.checkNotNull(environmentProperties.getEnv().getProperty(AppUtils.isProd(environmentProperties.getEnvironmentType()) ? "kettle.prod.read.jdbc.url" : "jdbc.url")));
        dataSource.setUsername(Preconditions.checkNotNull(environmentProperties.getEnv().getProperty(AppUtils.isProd(environmentProperties.getEnvironmentType()) ? "kettle.prod.read.jdbc.user" : "jdbc.user")));
        dataSource.setPassword(Preconditions.checkNotNull(environmentProperties.getEnv().getProperty(AppUtils.isProd(environmentProperties.getEnvironmentType()) ? "kettle.prod.read.jdbc.pass" : "jdbc.pass")));

        return dataSource;
    }

    @Bean(name = "TransactionReadDataSourceTM")
    public PlatformTransactionManager transactionManager() {
        final JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory().getObject());
        return transactionManager;
    }

    @Bean(name = "TransactionReadDataSourceET")
    public PersistenceExceptionTranslationPostProcessor exceptionTranslation() {
        return new PersistenceExceptionTranslationPostProcessor();
    }

    final Properties additionalProperties() {
        final Properties hibernateProperties = new Properties();
        hibernateProperties.setProperty("hibernate.hbm2ddl.auto", environmentProperties.getEnv().getProperty("hibernate.hbm2ddl.auto"));
        hibernateProperties.setProperty("hibernate.dialect", environmentProperties.getEnv().getProperty("hibernate.dialect"));
        hibernateProperties.setProperty("hibernate.show_sql", environmentProperties.getEnv().getProperty("hibernate.show_sql"));
        return hibernateProperties;
    }
}
