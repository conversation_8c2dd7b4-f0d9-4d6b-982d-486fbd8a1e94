package com.stpl.tech.kettle.core.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;

public class GooglePaymentServieImplTemp {

	private static final Logger LOG = LoggerFactory.getLogger(GooglePaymentServieImplTemp.class);

	public static void main(String[] args) {
		GooglePaymentServieImplTemp impl = new GooglePaymentServieImplTemp();
		try {
			System.out.println(impl.getAccessToken());
		} catch (Exception e) {
			LOG.error("Error in gpay payment process", e);
		}
	}

	public String getAccessToken() throws IOException {
		String keyFile = "google-chatapi.json";
		final String[] SCOPES = { "https://www.googleapis.com/auth/chat.bot" };
		InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(keyFile);
		GoogleCredential googleCredential = GoogleCredential.fromStream(inputStream)
				.createScoped(Arrays.asList(SCOPES));
		googleCredential.refreshToken();
		return googleCredential.getAccessToken();

	}
}