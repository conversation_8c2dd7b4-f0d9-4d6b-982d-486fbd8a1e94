/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification.receipt;

import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

public class AssemblySlipRawPrintReceipt extends OrderRawPrintReceipt {

	MasterDataCache metadataCache;
	private final List<OrderKOTRawPrintReceipt> templates;

	public AssemblySlipRawPrintReceipt(Unit unit, MasterDataCache metadataCache, OrderInfo detail, String basePath,
									   List<OrderKOTRawPrintReceipt> templates, EnvironmentProperties env) {
		super(unit, detail, basePath, null,env);
		this.metadataCache = metadataCache;
		this.templates = templates;
	}

	@Override
	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getOrder().getOrderId() + "/AssemblySlip-"
				+ getOrder().getOrderId() + ".html";
	}

	@Override
	public StringBuilder processData() {

		reset();
		if (AppConstants.TAKE_AWAY.equals(getOrder().getSource())) {
			center(bold(doubleFont("Chaayos Take Away")));
		} else {
			center(bold(doubleFont("Assembly Slip " + getOrderType(getOrder().getOrderType()))));
		}
		if (hasValue(getOrder().getTokenNumber()) && !isCOD()) {
			left(rpad("Token Number", 20) + getOrder().getTokenNumber());
		}
		if (hasTable() && !isCOD()) {
			left(rpad("Table No", 20) + getOrder().getTableNumber());
		}
		if (getOrder().getCustomerName() != null) {
			left(rpad("Name", 20) + bold(doubleFont(getOrder().getCustomerName())));
		}
		left(rpad("Order No", 20) + getOrder().getGenerateOrderId());
		left(rpad("Order Time", 20) + AppUtils.getBillPrintFormat(getOrder().getBillingServerTime()));
		left(rpad("Source", 20) + bold(orderSource()));
		if (getOrder().getOrderRemark() != null) {
			left(rpad("Order Remark", 20) + getOrder().getOrderRemark());
		}
		if (isCOD()) {
			channelPartnerDetails();
		}
		if (getCustomer().getId() > 5 && getDeliveryPartner() != null && getDeliveryPartner().getName() != null
				&& getDeliveryPartner().getId() > 1) {
			left(rpad("Delivery Partner",20) + getDeliveryPartner().getName());
		}
		separator();
		for (OrderKOTRawPrintReceipt template : templates) {
			left(bold(template.getType().getDescription()));
			template.itemDetails(sb);
			separator();
		}
		cut();
		return getSb();
	}

}
