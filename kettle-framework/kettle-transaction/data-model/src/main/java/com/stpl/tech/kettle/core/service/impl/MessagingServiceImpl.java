/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;

import com.stpl.tech.kettle.core.data.vo.OrderBasicInfo;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.service.MessagingService;

//@Service
public class MessagingServiceImpl implements MessagingService {

	private static final Logger LOG = LoggerFactory.getLogger(MessagingServiceImpl.class);
	// @Autowired
	private RabbitTemplate template;

	@Autowired
	private Queue orderQueue;

	@Override
	public void addOrder(OrderInfo order) {
		LOG.info(String.format("Publishing order with productId %s and %d to the queue", order.getOrder().getGenerateOrderId(),
				order.getOrder().getOrderId()));
		try {
			OrderBasicInfo info = new OrderBasicInfo(order.getOrder().getGenerateOrderId(),
					order.getOrder().getOrderId(), order.getUnit().getName());
			this.template.convertAndSend(orderQueue.getName(), info);
		} catch (Exception e) {
			LOG.error("Error while persisting the order", e);
		}
	}

}
