package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.payment.PaymentApplicableResponse;
import com.stpl.tech.kettle.core.payment.PaymentPartnerType;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.data.model.StandaloneTransactionDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.payment.model.AGS.AGSCreateRequest;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMStatus;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SStatus;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatusChangeRequest;
import com.stpl.tech.master.payment.model.PaymentVO;
import com.stpl.tech.master.payment.model.ezetap.EzetapCreateRequest;
import com.stpl.tech.master.payment.model.ezetap.EzetapPaymentResponse;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentStatus;
import com.stpl.tech.master.payment.model.gpay.GPayQRResponse;
import com.stpl.tech.master.payment.model.ingenico.IngenicoQrResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmFetchPaymentOptionsRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauth;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParams;
import com.stpl.tech.master.payment.model.patymNew.PaytmStatusResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmValidateSSOTokenResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequestResponseWrapper;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmPaymentStatus;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiQrResponse;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiS2SResponse;
import com.stpl.tech.master.payment.model.razorpay.BasicTransactionInfo;
import com.stpl.tech.master.payment.model.razorpay.RazorPayCreateRequest;
import com.stpl.tech.master.payment.model.razorpay.RazorPayEventData;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentResponse;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface PaymentServiceNew {


    Object updatePayment(Object payment, PaymentPartnerType paymentPartnerType,
                         boolean skipSignatureVerification,Integer brandId) throws PaymentFailureException;

    @Deprecated
	public PaymentRequest createRequest(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;


    PaytmStatusResponse getPaytmStatusResponse(String orderId) throws Exception;

    public void createPaymentEvent(RazorPayEventData event);

	public RazorPayPaymentResponse fetchRazorPayPayment(String paymentId) throws PaymentFailureException;

    PaymentRequest createPaymentRequest(OrderPaymentRequest order, PaymentPartnerType paymentPartnerType, Map<String , String> map)
			throws Exception;

    RazorPayPaymentResponse fetchResponseByRazorPayByPartnerOrderId(String partnerOrderId,Integer brandId) throws PaymentFailureException;

    public Boolean cancelPayment(PaymentStatusChangeRequest cancel);

	public Boolean failurePayment(PaymentStatusChangeRequest failure);

	public Boolean checkPaymentStatus(PaymentVO payment);

	public OrderPayment refundPayment(OrderPayment refund) throws IOException, PaymentFailureException;

	public OrderPayment getDisassociatedPayment(String contact) throws PaymentFailureException;

	public OrderPayment getDisassociatedPayment() throws PaymentFailureException;

	public List<OrderPayment> getPendingRefunds();

	public OrderPayment refundPayment(Integer orderId) throws IOException, PaymentFailureException;

	public OrderPayment refundDoublePayments(String partnerTransactionId) throws IOException, PaymentFailureException;

	public OrderPayment getPaymentStatus(Integer orderId) throws IOException, PaymentFailureException;

	public OrderPayment refundByPaymentId(Integer paymentDetailId) throws IOException, PaymentFailureException;

	public PaytmCreateRequest getPayTMQRCodeIdForKIOSK(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;

	public PaytmPaymentStatus checkKIOSKPaytmQRPaymentStatus(String orderID) throws PaymentFailureException;

	public boolean refundKIOSKPaytmQRPaymentAmount(String orderId, BigDecimal amountToRefund, String refundReason) throws PaymentFailureException;


    public void initiateAutoRefunds();

    public void initiateAutoRefundsForDineIn();

	public void markTransactionCancel(OrderPaymentRequest order) throws Exception;

	public OrderPayment refundPaytmUpiQR(OrderPayment paymentRefundRequest) throws PaymentFailureException;

	AGSPaymentS2SResponse updateAGSPaymentS2SStatus(AGSPaymentS2SStatus response) throws PaymentFailureException;

	AGSPaymentCMResponse checkAGSPaymentS2SStatus(String externalOrderId) throws PaymentFailureException;

	public StandaloneTransactionDetail createStandalonePaymentEvent(RazorPayEventData data);

	boolean setNotificationDetail(String paymentId, String smsType, String emailType);

	void sendStandaloneNotification(String paymentId, String event, BasicTransactionInfo info);

	public void updateStatusOfDisassociatedPayments(String contact, Date timeBeforeRefundIsEligible);

	public void updateStatusOfDisassociatedPayments(Date loweDate, Date upperDate);

	PaytmParamResponse initiatePaytmPayment(PaytmParams paytmParams) throws Exception;

    PaytmOauth getPaytmOauth(PaytmOauthRequest paytmOauthRequest) throws Exception;

    PaytmOauthResponse getPaytmOauth2V3(PaytmOauthRequest paytmOauthRequest);

    boolean revokePaytmToken(PaytmOauthRequest paytmOauthRequest);

	PaytmValidateSSOTokenResponse validatePaytmSSOToken(String ssoToken);

	String getPaytmAccessToken(PaytmFetchPaymentOptionsRequest request) throws Exception;

    String fetchPaytmPaymentOptions(PaytmFetchPaymentOptionsRequest request);

	String fetchPaytmPaymentOptionsV2(PaytmFetchPaymentOptionsRequest request);


	GPayPaymentStatus checkGPayQRPaymentStatus(OrderPaymentRequest request) throws PaymentFailureException;

	PaymentApplicableResponse getTransactionsApplicableForRefund(String contact);

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	void updateRefundedPayment(Object payment) throws PaymentFailureException;

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	void updateRefundInitiatedPayment(Object payment) throws PaymentFailureException;

	OrderPaymentDetail getOrderPaymentDetailStatus(String externalOrderId);

	void updateStatusOfDisassociatedPaymentsNeo(Date startTime, Date endTime);

}
