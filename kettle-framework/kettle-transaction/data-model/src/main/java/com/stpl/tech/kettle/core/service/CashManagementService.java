/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.xml.datatype.DatatypeConfigurationException;

import com.stpl.tech.kettle.domain.model.PullPacket;
import com.stpl.tech.kettle.domain.model.PullPacketDenomination;
import com.stpl.tech.kettle.domain.model.PullSettlementDenomination;
import com.stpl.tech.kettle.domain.model.PullSettlementDetail;
import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.PaymentMode;

public interface CashManagementService {

	List<PullPacket> getOpenPullsForUnit(Integer unitId, Integer paymentModeId, Integer resultCount,
			List<String> statusList) throws DataNotFoundException;

	PullPacket submitPull(PullPacket pullPacket) throws DataNotFoundException;

	PullSettlementDetail transferPull(PullSettlementDetail pullSettlementDetail) throws DataNotFoundException;

	public List<PullPacket> getUnitPullsForTransfer(Integer unitId) throws DataNotFoundException;

	List<PullSettlementDenomination> getPullSettlementDenominationsForPaymentMode(PaymentMode paymentMode);

	List<PullSettlementDetail> getOpenPullSettlements(Integer unitId, Integer settlementId)
			throws DataNotFoundException, DatatypeConfigurationException;
	
	List<PullSettlementDetail> getOpenPullSettlementsByType(Integer settlementTypeId)
			throws DataNotFoundException, DatatypeConfigurationException;

	List<PullSettlementDetail> getPullSettlements(Date startDate, Date endDate, int unitId, boolean fetchPulls, boolean fetchDenominations,int start,int batchSize)
			throws DataNotFoundException, DatatypeConfigurationException;
	
	List<PullSettlementDetail> getPullSettlementsByType(Date startDate, Date endDate, int settlementTypeId,int start,int batchSize)
			throws DataNotFoundException, DatatypeConfigurationException;

	public List<PullPacketDenomination> getCouponDenominations(List<Integer> pullList, int paymentModeId)
			throws DataNotFoundException, DatatypeConfigurationException;

	List<PullPacketDenomination> getCouponDenominations(int pullId)
			throws DataNotFoundException, DatatypeConfigurationException;

	PullSettlementDetail closePullSettlement(PullSettlementDetail pullSettlement);

	public void saveClosurePayment(Date businessDate, Collection<SettlementReport> report, int unitId);

	public String getImagePath(String settlementId);


	PullSettlementDetail getPullSettlement(int settlementId) throws DataNotFoundException, DatatypeConfigurationException;

	public boolean validatePullTransferDate(Date slipDate, int unitId, int paymentMode);

	public Map<String, Object> getValidPullTransferDates(int unitId, int paymentMode);

	public Map<String, Object> getValidPullTransferDatesAsString(int unitId, int paymentMode);

	Long getPullSettlementsTotalCount(Date startDate, Date endDate, int unitId, boolean fetchPulls, boolean fetchDenominations)
			throws DataNotFoundException, DatatypeConfigurationException;

	Long getByTypePullSettlementsTotalCount(Date startDate, Date endDate, int settlementTypeId)
			throws DataNotFoundException, DatatypeConfigurationException;


}
