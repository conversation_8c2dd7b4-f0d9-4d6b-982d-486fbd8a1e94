package com.stpl.tech.kettle.core.data.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.kettle.core.service.impl.DreamFolksVoucherValidator;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DreamFolksVoucherResponse {

        @JsonProperty("status")
        private Boolean status;

        @JsonProperty("message")
        private String message;

        @JsonProperty("response_code")
        private Integer responseCode;

        @JsonProperty("data")
        private List<DreamFolksTranscationData> data;

        public Boolean getStatus() { return status; }
        public void setStatus(Boolean status) { this.status = status; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public Integer getResponseCode() { return responseCode; }
        public void setResponseCode(Integer responseCode) { this.responseCode = responseCode; }

        public List<DreamFolksTranscationData> getData() { return data; }
        public void setData(List<DreamFolksTranscationData> data) { this.data = data; }

}
