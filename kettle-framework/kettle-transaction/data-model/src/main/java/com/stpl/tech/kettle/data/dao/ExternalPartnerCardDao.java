package com.stpl.tech.kettle.data.dao;

import java.util.List;

import com.stpl.tech.kettle.data.model.ExternalPartnerCardDetail;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface ExternalPartnerCardDao extends AbstractDao {
	public List<ExternalPartnerCardDetail> getPartnerCardDetail(String cardNumber, String partnerCode, String status);

	public List<ExternalPartnerCardDetail> getUniquePartnerBillNumber(String billNo);
}
