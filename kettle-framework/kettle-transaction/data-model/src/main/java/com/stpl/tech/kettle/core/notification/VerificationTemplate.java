package com.stpl.tech.kettle.core.notification;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractTemplate;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 02-12-2017.
 */
public class VerificationTemplate extends AbstractTemplate{
    private String basePath;
    Map<String, Object> data = new HashMap<String, Object>();
    private String verificationLink;
    private String token;
    private String shortUrl;
    private String templatePath;

    public VerificationTemplate(String basePath, String verificationLink, String token, String templatePath) {
        this.basePath = basePath;
        this.verificationLink = verificationLink;
        this.token = token;
        this.templatePath = templatePath;
    }

    // "template/VerificationTemplate.html"

    @Override
    public String getTemplatePath() {
        return templatePath;
    }

    public void setTemplatePath(String templatePath) {
        this.templatePath = templatePath;
    }

    @Override
    public String getFilepath() {
        return basePath +"/verifications/VerificationReceipt-" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        data.put("verificationLink",verificationLink);
        data.put("token",token);
        data.put("shortUrl", getShortUrl());
        return data;
    }

    private String getShortUrl(){
        String url = verificationLink + "?token=" + token;
        try {
            ShortUrlData urlData = SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(url);
            return urlData.getUrl();
        } catch (IOException e) {
            e.printStackTrace();
            return url;
        }
    }
}
