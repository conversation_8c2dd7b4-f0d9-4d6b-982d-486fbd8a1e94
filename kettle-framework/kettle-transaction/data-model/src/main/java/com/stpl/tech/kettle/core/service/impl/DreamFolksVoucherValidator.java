package com.stpl.tech.kettle.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.core.data.vo.DreamFolksTranscationData;
import com.stpl.tech.kettle.core.data.vo.DreamFolksVoucherResponse;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.core.service.VoucherValidator;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.List;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DreamFolksVoucherValidator implements VoucherValidator {
    private static final Logger LOG = LoggerFactory.getLogger(DreamFolksVoucherValidator.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    EnvironmentProperties props;
    
    @Override
    public boolean validate(OfferOrder offerOrder) {
        try {
            String endpoint = props.dreamFolksGetVendorTransactionEndpoint();
            String apiKey = props.dreamFolksApiKey();
            String checksumKey = props.dreamFolksCheckSumKey();
            Map<String, String> payload = new HashMap<>();
            payload.put("outlet_id", offerOrder.getDreamFolksVoucherDetails().getOutletId());
            payload.put("voucher_code", offerOrder.getDreamFolksVoucherDetails().getVoucherCode());
            String checksum = generateDreamFolksChecksum(payload, checksumKey);
            Map<String, String> headers = new HashMap<>();
            headers.put("x-api-key", apiKey);
            headers.put("x-checksum-value", checksum);
            headers.put("Content-Type", "application/json");
            Map<String, String> sortedPayload = new TreeMap<>(payload);
            String rawJson = objectMapper.writeValueAsString(sortedPayload);
            DreamFolksVoucherResponse response = com.stpl.tech.master.core.WebServiceHelper.postJsonWithHeaders(endpoint, rawJson, headers, DreamFolksVoucherResponse.class);
            LOG.info("DreamFolks API Response: {}", new Gson().toJson(response));
            
            if (Objects.nonNull(response) &&
                    Objects.nonNull(response.getResponseCode()) &&
                    response.getResponseCode() == 2000 &&
                    Objects.nonNull(response.getData()) &&
                    !response.getData().isEmpty()
            ) {
                DreamFolksTranscationData firstData = response.getData().get(0);
                if (Objects.nonNull(firstData.getTotalCount()) &&
                        Objects.nonNull(firstData.getTransactionId()) &&
                        Objects.nonNull(firstData.getTransactionTime())
                ) {
                    offerOrder.getDreamFolksVoucherDetails().setRedeemedQty(firstData.getTotalCount());
                    offerOrder.getDreamFolksVoucherDetails().setTransactionId(firstData.getTransactionId());
                    offerOrder.getDreamFolksVoucherDetails().setTransactionTime(AppUtils.getDate(firstData.getTransactionTime(), "yyyy-MM-dd HH:mm:ss"));
                    return true;
                }else{
                    LOG.warn("DreamFolks voucher validation failed: Missing required fields in response data");
                    return false;
                }
            }
            return false;
        } catch (Exception e) {
            LOG.error("DreamFolks voucher validation failed", e);
        }
        return false;
    }

    /**
     * Generates the DreamFolks checksum as per the provided logic.
     * 1. Ksort (TreeMap) the payload
     * 2. Serialize to raw JSON
     * 3. Concatenate <secret>|<rawJson>
     * 4. SHA512 hash
     */
    private String generateDreamFolksChecksum(Map<String, String> payload, String secret) throws Exception {
        Map<String, String> sortedPayload = new TreeMap<>(payload);
        String rawJson = objectMapper.writeValueAsString(sortedPayload);
        String rawString = secret + "|" + rawJson;
        return org.apache.commons.codec.digest.DigestUtils.sha512Hex(rawString);
    }

} 