package com.stpl.tech.kettle.core.listener;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewEvent;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.MessageProducer;

public class CustomerTransactionViewListener implements MessageListener {

	/**
	 * static sl4j Logger
	 */
	private static final Logger LOG = LoggerFactory.getLogger(CustomerTransactionViewListener.class);

	private final OrderManagementService service;

	public CustomerTransactionViewListener(OrderManagementService service) {
		this.service = service;
	}

	@Override
	public void onMessage(Message message) {
		try {
			LOG.info("On CustomerTransactionViewEvent Message " + message.getJMSMessageID());
			if (message instanceof SQSObjectMessage) {
				LOG.info("Message instance of : SQSObjectMessage : " + message.getJMSMessageID());
				SQSObjectMessage object = (SQSObjectMessage) message;
				String response = JSONSerializer.toJSON(object.getObject());
				LOG.info("SQSObjectMessage : {}", response);
				CustomerTransactionViewEvent event = JSONSerializer.toJSON(response, CustomerTransactionViewEvent.class);
				service.updateCustomerTransaction(event.getCustomerId(), event.getBrandId());
				message.acknowledge();
				LOG.info("Order Acknowledge : SQSObjectMessage : " + message.getJMSMessageID());
			} else if (message instanceof SQSTextMessage) {
				LOG.info("Message instance of : SQSTextMessage : " + message.getJMSMessageID());
				SQSTextMessage object = (SQSTextMessage) message;
				String response = (String) object.getText();
				LOG.info("SQSObjectMessage : {}", response);
				CustomerTransactionViewEvent event = JSONSerializer.toJSON(response, CustomerTransactionViewEvent.class);
				service.updateCustomerTransaction(event.getCustomerId(), event.getBrandId());
				message.acknowledge();
				LOG.info("Order Acknowledge : SQSTextMessage : " + message.getJMSMessageID());
			} else {
				LOG.info("Order Not Acknowledged" + message.getJMSMessageID());
			}

		} catch (JMSException e) {
			LOG.error("Error while saving the message", e);
		}
	}

}
