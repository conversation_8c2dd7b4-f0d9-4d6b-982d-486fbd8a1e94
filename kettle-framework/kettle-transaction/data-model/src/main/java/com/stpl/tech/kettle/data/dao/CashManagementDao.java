/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.data.model.ClosurePaymentDetails;
import com.stpl.tech.kettle.data.model.ClosurePaymentTaxDetails;
import com.stpl.tech.kettle.data.model.PullDetail;
import com.stpl.tech.kettle.data.model.SettlementDetail;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.domain.model.PullPacket;
import com.stpl.tech.kettle.domain.model.PullPacketDenomination;
import com.stpl.tech.kettle.domain.model.PullSettlementDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.UnitBasicDetail;

public interface CashManagementDao {

	public List<PullDetail> getOpenPullsForUnit(Integer unitId, Integer paymentModeId, Integer resultCount,
			List<String> statusList);

	public PullDetail submitPull(PaymentMode mode, PullPacket pullPacket);

	public PullSettlementDetail transferPull(PullSettlementDetail pullSettlementDetail);

	List<PullDetail> getUnitPullsForTransfer(Integer unitId);

	List<SettlementDetail> getOpenPullSettlements(Integer unitId, Integer settlementId);
	
	List<SettlementDetail> getOpenPullSettlementsByType(Integer settlementTypeId);

	List<SettlementDetail> getPullSettlements(Date startDate, Date endDate, int unitId,int start,int batchSize);
	
	List<SettlementDetail> getPullSettlementsByType(Date startDate, Date endDate, int settlementTypeId,int start,int batchSize);

	public List<PullPacketDenomination> getCouponDenominations(int pullId,
			Map<Integer, DenominationDetail> denominations);

	public PullSettlementDetail closePullSettlement(PullSettlementDetail pullSettlement);

	public PullDetail savePullDetails(UnitBasicDetail unit, PaymentMode mode, ClosurePaymentDetails paymentDetails);

	public ClosurePaymentDetails saveClosurePaymentDetails(ClosurePaymentDetails paymentDetails, List<ClosurePaymentTaxDetails> taxes, int unitId,
			Date businessDate);

	public UnitClosureDetails getLastClosureByUnit(int unitId, Date businessDate);

	public String getPullSettlementPath(Integer settlementId);

	SettlementDetail getSettlement(int settlementId);

	public Date getLatestSuccessfulPullDate(int unitId, int paymentMode) throws DataNotFoundException;

	Long getPullSettlementsTotalCount(Date startDate, Date endDate, int unitId);

	Long getPullSettlementsByTypeTotalCount(Date startDate, Date endDate, int settlementTypeId);

}
