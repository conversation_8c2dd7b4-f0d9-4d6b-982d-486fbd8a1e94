/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import java.util.HashMap;
import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.model.DeliveryDetail;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.domain.model.OrderFeedback;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.SDPAllocationException;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.TemplateRenderingException;

public interface DeliveryRequestService {
    public DeliveryResponse createDeliveryRequest(OrderInfo orderInfo, Unit unit);

    public DeliveryResponse cancelDeliveryRequest(String generateOrderId, int orderId, int unitId)
            throws DataNotFoundException, TemplateRenderingException;

    public Integer updateDelivery(DeliveryResponse response) throws DataNotFoundException, TemplateRenderingException;

    public Boolean checkIfAutomated(Integer deliveryPartnerId);

    public String registerMerchant(Unit unit, Integer partnerId);

    public IdCodeName convert(DeliveryResponse deliveryDetails);

    public List<DeliveryDetail> getDeliveryDetails(String generatedOrderId);

    public DeliveryResponse createDeliveryRequest(OrderInfo info, Unit unit, int deliveryPartnerId);

    public void refreshPriorityCache();

    public List<UnitToDeliveryPartnerMappings> getUnitPartnerMappings();

    public DeliveryResponse saveManualDelivery(DeliveryResponse detail) throws DataNotFoundException, TemplateRenderingException, SDPAllocationException;

    public OrderInfo addDeliveryDetails(OrderInfo info, Unit unit);

    public OrderInfo addDeliveryDetails(OrderInfo info, Unit unit, IdCodeName deliveryPartner);

    public List<OrderFeedback> getOrderListForFeedback(@RequestBody String deliveryPersonContact, String feedbackStatus);

    public String submitOrderDeliveryFeedback(int deliveryId, String code);

    public void refreshPartnerAttributes();
    
    public HashMap<Integer,IdCodeName> defaultDeliveryPartner(String mappingType);

    public HashMap<String, String> resendMsgToRider(DeliveryResponse response) throws TemplateRenderingException, DataNotFoundException;

}
