package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.util.*;
import com.stpl.tech.util.notification.EmailNotification;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 02-12-2017.
 */
public class VerificationEmailNotification extends EmailNotification{

    private VerificationTemplate template;
    private EnvType envType;
    private String customerEmail;
    private String customerName;
    private String fromEmail;
    private Integer brandId;

    public VerificationEmailNotification(VerificationTemplate template, EnvType envType,
                                         String customerEmail, String name, String fromEmail,Integer brandId) {
        this.template = template;
        this.envType = envType;
        this.customerEmail = customerEmail;
        this.customerName = AppUtils.checkBlank(name)!=null ? name : "";
        this.fromEmail = fromEmail;
        this.brandId = brandId;
    }

    @Override
    public String[] getToEmails() {
        return new String[] { customerEmail };
    }

    @Override
    public String getFromEmail() {
        return fromEmail;
    }

    @Override
    public String subject() {
        if(AppConstants.DOHFUL_BRAND_ID.equals(this.brandId)){
            return "Hi "+ customerName +", Verify your email";
        }
        return "Hi "+ customerName +", Verify your email and earn 10 loyaltea points";
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
