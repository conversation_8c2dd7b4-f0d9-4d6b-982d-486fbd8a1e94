package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.service.AGSPaymentService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.master.payment.model.AGS.AGSCreateRequest;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMStatus;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SStatus;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;

@Service
public class AGSPaymentServiceImpl implements AGSPaymentService {

    private static final Logger LOG = LoggerFactory.getLogger(AGSPaymentServiceImpl.class);

    private static final String AGS_IS_SAVED_FAILED = "01";

    private static final String AGS_IS_SAVED_SUCCESS = "00";

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private PaymentGatewayDao paymentGatewayDao;

    @Override
    public AGSCreateRequest createRequest(EnvType type, OrderPaymentRequest order) throws PaymentFailureException {
        return new AGSCreateRequest(order.getGenerateOrderId(), order.getPaidAmount());
    }

    @Override
    public AGSPaymentCMStatus fetchPayment(EnvType type, String paymentId) throws PaymentFailureException {
        return null;
    }

    @Override
    public PaymentStatus getPaymentStatus(EnvType type, String paymentId, BigDecimal transactionAmount) throws PaymentFailureException {
        AGSPaymentCMStatus response = fetchPayment(type,paymentId);
        String status = response.getStatus();
        if(status.equals("success") || status.equals("captured")){
            return PaymentStatus.SUCCESSFUL;
        }
        return PaymentStatus.FAILED;
    }

    @Override
    public AGSPaymentCMStatus fetchOrder(EnvType type, String AGSOrderId) throws PaymentFailureException {
        return null;
    }

    @Override
    public OrderPayment refundRequest(EnvType type, OrderPayment request) throws PaymentFailureException {
        return null;
    }

    @Override
    public AGSPaymentCMResponse checkAGSPaymentS2SStatus(String externalOrderId) {
        String errorReason = StringUtils.EMPTY;
        try {
            if (StringUtils.isNotBlank(externalOrderId)) {
                LOG.info("Checking if payment status already received");
                try {
                    OrderPaymentDetail orderPaymentDetail =
                            paymentGatewayDao.getActivePaymentDetail(externalOrderId);
                    if (orderPaymentDetail != null
                            && StringUtils.isNotBlank(orderPaymentDetail.getPaymentStatus())) {
                        if (orderPaymentDetail.getPaymentStatus().equalsIgnoreCase(PaymentStatus.SUCCESSFUL.name())) {
                            return getAgsPaymentCMResponse(true, 6, "Payment Success.", "Payment was successful, Now placing your order.");
                        }
                    }
                } catch (Exception ex) {
                    LOG.error("Exception occurred while updating AGS Payment status , No order payment details found: ", ex);
                    return getAgsPaymentCMResponse(false, 7, "Payment Failed.", "Payment has failed, Please try again.");
                }
            }
        } catch (Exception ex) {
            LOG.error("Exception occurred while updating AGS Payment status: ", ex);
            return getAgsPaymentCMResponse(false, 7, "Payment Failed.", "Payment has failed, Please try again.");
        }
        return getAgsPaymentCMResponse(false, 7, "Payment Failed.", "Payment has failed, Please try again.");
    }

    @Override
    public AGSPaymentS2SResponse updateAGSPaymentS2SStatus(AGSPaymentS2SStatus status) {
        String errorReason = StringUtils.EMPTY;
        try {
            if (StringUtils.isNotBlank(status.getAdditionalData())) {
                LOG.info("Checking if payment status already success");
                String externalOrderId = status.getAdditionalData();
                try {
                    OrderPaymentDetail orderPaymentDetail =
                            paymentGatewayDao.getActivePaymentDetail(externalOrderId);
                    if (orderPaymentDetail != null) {
                        LOG.info("orderPaymentDetail found: " + JSONSerializer.toJSON(orderPaymentDetail));
                        if (StringUtils.isNotBlank(orderPaymentDetail.getPaymentStatus())
                                && orderPaymentDetail.getPaymentStatus().equalsIgnoreCase(PaymentStatus.SUCCESSFUL.name())) {
                            LOG.info("Status already saved as success");
                            return getAGSPaymentS2SResponse(AGS_IS_SAVED_SUCCESS, "Status already saved as success");
                        }
                        LOG.info("Order payment is not successful, processing further...");
                    }
                } catch (Exception ex) {
                    LOG.error("Exception occurred while updating AGS Payment status , No order payment details found: ", ex);
                    return getAGSPaymentS2SResponse(AGS_IS_SAVED_FAILED, "No order payment details found.");
                }
            }
            if (isValidStatus(status, errorReason)) {
                PaymentStatus paymentStatus = PaymentStatus.FAILED;
                status.setStatus("failed");
                if (StringUtils.equalsIgnoreCase(status.getResponseCode(), AGS_IS_SAVED_SUCCESS)) {
                    paymentStatus = PaymentStatus.SUCCESSFUL;
                    status.setStatus("successful");
                }
                status.setOrderId(status.getAdditionalData());
                status.setTransactionId(status.getInvoiceNo());
                paymentGatewayDao.updateResponse(paymentStatus, status);
                LOG.info("Order payment does not exist, processing further...");
                return getAGSPaymentS2SResponse(AGS_IS_SAVED_SUCCESS, "Save Successfully");
            }
        } catch (Exception ex) {
            LOG.error("Exception occurred while updating AGS Payment status: ", ex);
            return getAGSPaymentS2SResponse(AGS_IS_SAVED_FAILED, "Some error occurred, while updating status in system");
        }
        return getAGSPaymentS2SResponse(AGS_IS_SAVED_FAILED,
                StringUtils.isNotBlank(errorReason) ? errorReason : "Failed to save status in database.");
    }

    @Override
    public AGSCreateRequest createAGSRequest(OrderPaymentRequest order) throws PaymentFailureException {
        AGSCreateRequest request = createRequest(props.getEnvironmentType(), order);
        paymentGatewayDao.createRequest(request, order);
        return request;
    }

    @Override
    public Map updateAGSPaymentCMStatus(AGSPaymentCMStatus response) {

        boolean validation = true; //because payment has been done successfully
        response.setStatus(validation ? "successful" : "failed");
        return paymentGatewayDao.updateAndRedirect(response, validation);
    }

    private AGSPaymentCMResponse getAgsPaymentCMResponse(boolean status, Integer code, String title, String message) {
        AGSPaymentCMResponse response = new AGSPaymentCMResponse();
        response.setStatus(status);
        response.setCode(code);
        response.setTitle(title);
        response.setMsg(message);
        return response;
    }

    private AGSPaymentS2SResponse getAGSPaymentS2SResponse(String isSavedCode, String message) {
        AGSPaymentS2SResponse response = new AGSPaymentS2SResponse();
        response.setIsSaved(isSavedCode);
        response.setMessage(message);
        LOG.info("AGS payment S2S Response is..." + JSONSerializer.toJSON(response));
        return response;
    }

    private boolean isValidStatus(AGSPaymentS2SStatus status, String errorReason) throws Exception {
        if (status != null) {
            if (StringUtils.isBlank(status.getTerminalId())) {
                errorReason = "Terminal_ID is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if (StringUtils.isBlank(status.getRRN())) {
                errorReason = "RRN is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if (StringUtils.isBlank(status.getInvoiceNo())) {
                errorReason = "Invoice_No is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if (StringUtils.isBlank(status.getTransactionType())) {
                errorReason = "Txn_Type is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if (StringUtils.isBlank(status.getAmount())) {
                errorReason = "Amt is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if (StringUtils.isBlank(status.getCardNo())) {
                errorReason = "Card_No is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if (StringUtils.isBlank(status.getTransactionDate())) {
                errorReason = "Txn_Date is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if (StringUtils.isBlank(status.getTransactionTime())) {
                errorReason = "Txn_Time is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if (StringUtils.isBlank(status.getResponseCode())) {
                errorReason = "Response_Code is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if (StringUtils.isBlank(status.getAdditionalData())) {
                errorReason = "additional_Data is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if (StringUtils.isBlank(status.getChecksum())) {
                errorReason = "Checksum is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            String generatedCheckSum = generateAGSCheckSum(status);
            if (StringUtils.isNotBlank(generatedCheckSum)
                    && StringUtils.equalsIgnoreCase(generatedCheckSum, status.getChecksum())) {
                LOG.info("Payment request is valid and checksum match success.");
                return true;
            } else {
                errorReason = "Payment request is invalid and checksum match failed.";
                LOG.info(errorReason);
                return false;
            }
        }
        errorReason = "Payment request is invalid or checksum match failed.";
        LOG.info(errorReason);
        return false;
    }

    private String generateAGSCheckSum(AGSPaymentS2SStatus status) throws Exception {
        String hashedString = new String();
        try {
            String checkSumMsg = getCheckSumMsgString(status);
            hashedString = DigestUtils.md5Hex(checkSumMsg + props.getAGSChecksumKey());
            hashedString.replace("-", "").toLowerCase();
            return hashedString;
        } catch (Exception ex) {
            LOG.error("Exception occurred while generating AGS checkSum: ", ex);
            return null;
        }
    }

    private String getCheckSumMsgString(AGSPaymentS2SStatus status) {
        StringBuilder checkSumMsg = new StringBuilder();
        checkSumMsg.append(status.getTerminalId());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getRRN());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getAuthCode());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getInvoiceNo());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getTransactionType());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getAmount());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getCardNo());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getTransactionDate());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getTransactionTime());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getResponseCode());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getAdditionalData());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getCardType());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getCardBrand());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getCardFlag());
        return checkSumMsg.toString();
    }
}
