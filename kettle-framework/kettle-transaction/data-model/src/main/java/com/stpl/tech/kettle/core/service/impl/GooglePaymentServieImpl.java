package com.stpl.tech.kettle.core.service.impl;

import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;

import com.stpl.tech.kettle.core.exception.OrderNotAttachedWithPaymentException;
import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.exception.UnSpecifiedGPayRefundStatus;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.OrderManagementDao;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.gpay.GPayMerchantRefund;
import com.stpl.tech.master.payment.model.gpay.GPayOriginalTransactionId;
import com.stpl.tech.master.payment.model.gpay.GPayRefundStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.stpl.tech.kettle.core.service.GooglePaymentService;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.gpay.GPayAmount;
import com.stpl.tech.master.payment.model.gpay.GPayMerchantInfo;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentPayload;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentRequest;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentResponse;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentStatus;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentStatusRequest;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentStatusResponse;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentType;
import com.stpl.tech.master.payment.model.gpay.GPayQRResponse;
import com.stpl.tech.master.payment.model.gpay.GPayTransactionDetails;
import com.stpl.tech.master.payment.model.gpay.GPayValidDuration;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;

@Service
public class GooglePaymentServieImpl implements GooglePaymentService {

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private OrderSearchService orderSearchService;

	private static final Logger LOG = LoggerFactory.getLogger(GooglePaymentServieImpl.class);
	private static final String BASE_URL = "https://nbupayments.googleapis.com/v1";
	private static final String[] SCOPES = { "https://www.googleapis.com/auth/nbupaymentsmerchants" };
	private static URL CREATE_URL;
	private static URL CHECK_STATUS_URL;
	static {
		try {
			CREATE_URL = new URL(BASE_URL + "/merchantQrPayments:create");
			CHECK_STATUS_URL = new URL(BASE_URL + "/merchantQrPayments:getStatus");
		} catch (Exception e) {
		}

	}

	private Map<EnvType, GoogleCredential> credentialMap = new HashMap<>();

	@Autowired
	private PaymentGatewayDao paymentGatewayDao;

	public String getAccessToken(GoogleCredential googleCredential) throws IOException {
		googleCredential.refreshToken();
		return googleCredential.getAccessToken();
	}

	/**
	 * Create HttpURLConnection that can be used for both retrieving and publishing.
	 *
	 * @return Base HttpURLConnection.
	 * @throws IOException
	 */
	private HttpURLConnection getConnection(URL url, String accessToken) throws IOException {
		HttpURLConnection conn = null;
		conn = (HttpURLConnection) url.openConnection();
		conn.setRequestMethod("POST");
		conn.setRequestProperty("Authorization", "Bearer " + accessToken);
		conn.setRequestProperty("Content-Type", "application/json; UTF-8");
		return conn;
	}

	private GoogleCredential getGoogleCredential(EnvType type) throws IOException {
		if (!credentialMap.containsKey(type)) {
			addGoogleCredentialToMap(type);
		}
		return credentialMap.get(type);
	}

	private void addGoogleCredentialToMap(EnvType type) throws IOException {
		String keyFile = "gpay-" + type.name().toLowerCase() + ".json";
		InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(keyFile);
		GoogleCredential googleCredential = GoogleCredential.fromStream(inputStream)
				.createScoped(Arrays.asList(SCOPES));
		credentialMap.put(type, googleCredential);
	}

	/**
	 * inputStreamToString
	 * 
	 * @param inputStream
	 * @return
	 * @throws IOException
	 */
	private String inputStreamToString(InputStream inputStream) throws IOException {
		StringBuilder stringBuilder = new StringBuilder();
		Scanner scanner = new Scanner(inputStream);
		while (scanner.hasNext()) {
			stringBuilder.append(scanner.nextLine());
		}
		scanner.close();
		return stringBuilder.toString();
	}

	public GPayPaymentRequest createRequest(OrderPaymentRequest request) {
		GPayPaymentPayload pay = new GPayPaymentPayload();
		pay.setMerchantInfo(new GPayMerchantInfo(request.getPosId()));
		pay.setQrType(GPayPaymentType.UPI_QR);
		pay.setTransactionDetails(new GPayTransactionDetails(request.getGenerateOrderId(),
				new GPayAmount("INR", request.getPaidAmount().intValue(), 0),
				String.format("Collecting %d for your order", request.getPaidAmount().intValue())));
		pay.setValidDuration(new GPayValidDuration(1000, 0));
		GPayPaymentRequest res = new GPayPaymentRequest();
		res.setStatus(PaymentStatus.INITIATED.name());
		res.setPayload(pay);
		res.setOrder(request);
		return res;

	}

	public static void main(String[] args) {
		GooglePaymentServieImpl impl = new GooglePaymentServieImpl();
		try {
			OrderPaymentRequest paymentRequest = new OrderPaymentRequest();
			paymentRequest.setCartId("1fwverg4bd4g4g");
			paymentRequest.setContactNumber("9599597740");
			paymentRequest.setCustomerId(65470);
			paymentRequest.setCustomerName("Mohit Malik");
			paymentRequest.setGenerateOrderId(AppUtils.generateRandomAlphaNumericCode(16));
			paymentRequest.setPaidAmount(new BigDecimal("1.00"));
			paymentRequest.setPaymentModeId(12);
			paymentRequest.setPaymentModeStatus("ACTIVE");
			paymentRequest.setPaymentModeName("Google Pay");
			paymentRequest.setPaymentSource(ApplicationName.KETTLE_CRM);
			paymentRequest.setPosId("BCR2DN6T7OT6RZT6");
			// System.out.println(impl.generateAccessToken(EnvType.DEV));
			System.out.println(impl.createGPayQR(EnvType.DEV, impl.createRequest(paymentRequest)));
		} catch (Exception e) {
			LOG.error("Error in gpay payment process", e);
		}
	}

	@Override
	public String generateAccessToken(EnvType env) throws IOException {
		return getAccessToken(getGoogleCredential(env));
	}

	@Override
	public GPayPaymentStatus checkPaymentStatus(EnvType env, OrderPaymentRequest order) throws IOException {
		GPayPaymentStatusRequest request = new GPayPaymentStatusRequest();
		request.setMerchantInfo(new GPayMerchantInfo(order.getPosId()));
		request.setTransactionId(order.getGenerateOrderId());
		HttpURLConnection connection = getConnection(CHECK_STATUS_URL, order.getAccessToken());

		try {
			sendRequest(connection, JSONSerializer.toJSON(request));

			int responseCode = connection.getResponseCode();
			if (responseCode == 200) {
				String response = inputStreamToString(connection.getInputStream());
				LOG.info("Payment Status Request Completed for GPay for {} \n, response is {}", request, response);
				GPayPaymentStatusResponse r = JSONSerializer.toJSON(response, GPayPaymentStatusResponse.class);
				GPayPaymentStatus qr = new GPayPaymentStatus(request.getTransactionId(),
						r.getInternalPaymentStatus().name(), r.getPaymentStatus());
				OrderPaymentDetail paytmPaymentDetails = paymentGatewayDao
						.getActivePaymentDetail(order.getGenerateOrderId());
				if (paytmPaymentDetails != null) {
					if (!paytmPaymentDetails.getPaymentStatus().equalsIgnoreCase(r.getInternalPaymentStatus().name())) {
						paymentGatewayDao.updateResponse(r.getInternalPaymentStatus(), qr);
					}
				}
				return qr;
			} else {
				String response = inputStreamToString(connection.getErrorStream());
				LOG.info("Unable to Fetch Payment Status for GPay for {} \n, response is {}", request, response);
			}

		} catch (Exception e) {
			LOG.error("Exception occurred while running Gpay Status Request", e);
		}

		return null;

	}

	@Override
	public GPayQRResponse createGPayQR(EnvType env, GPayPaymentRequest request) throws IOException {
		String accessToken = getAccessToken(getGoogleCredential(env));
		HttpURLConnection connection = getConnection(CREATE_URL, accessToken);

		try {
			connection.setDoOutput(true);
			DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream());
			outputStream.writeBytes(JSONSerializer.toJSON(request.getPayload()));
			outputStream.flush();
			outputStream.close();

			int responseCode = connection.getResponseCode();
			if (responseCode == 200) {
				String response = inputStreamToString(connection.getInputStream());
				LOG.info("Create Request Completed for GPay for {} \n, response is {}", request, response);
				GPayPaymentResponse r = JSONSerializer.toJSON(response, GPayPaymentResponse.class);
				GPayQRResponse qr = new GPayQRResponse();
				qr.setAccessToken(accessToken);
				qr.setAmount(request.getPayload().getTransactionDetails().getAmount().getUnits());
				qr.setQrCodeId(r.getQrLink());
				qr.setStatus(PaymentStatus.INITIATED.name());
				qr.setTransactionId(request.getOrder().getGenerateOrderId());
				qr.parseQRLink();
				return qr;
			} else {
				String response = inputStreamToString(connection.getErrorStream());
				LOG.info("Unable to Create Request Completed for GPay for {} \n, response is {}", request, response);
			}

		} catch (Exception e) {
			LOG.error("Exception occurred while creating Gpay Request", e);
		}

		return null;
	}

	@Override
	public GPayQRResponse getGPayQRCode(OrderPaymentRequest order) throws PaymentFailureException {
		try {
			OrderPaymentDetail existingSuceessfulPayment = paymentGatewayDao
					.getSuccessfulOrderPaymentDetail(order.getGenerateOrderId());
			if (existingSuceessfulPayment == null) {
				GPayPaymentRequest request = createRequest(order);
				paymentGatewayDao.createRequest(request, order);
				return createGPayQR(props.getEnvironmentType(), request);
			} else {
				/**
				 * In case of a already existing successful payment we return null to the UI so
				 * that we can link the successful payment to the actual order
				 */
				return null;
			}
		} catch (Exception ex) {
			LOG.error("Exception Occurred ", ex);
			throw new PaymentFailureException("Could not create payTM QR code.");
		}
	}

	@Override
	public GPayPaymentStatus checkGPayQRPaymentStatus(OrderPaymentRequest request) throws PaymentFailureException {
		try {
			LOG.info("Checking gpay payment status for OrderID " + request);
			return checkPaymentStatus(props.getEnvironmentType(), request);
		} catch (Exception ex) {
			LOG.error("Exception Occurred ", ex);
			throw new PaymentFailureException("Could not fetch status for gpay QR code.");
		}
	}

	@Override
	public OrderPayment refundRequest(EnvType envType, OrderPayment request) throws PaymentFailureException {
		try {
			GPayMerchantRefund gPayMerchantRefund = getRefundRequest(envType, request);
			String endPoint = "https://nbupayments.googleapis.com/v1/merchants/" + request.getMerchantId() + "/merchantRefunds?" +
					"merchant_refund_id=" + request.getRefundId();

			String accessToken = getAccessToken(getGoogleCredential(envType));
			URL url =  new URL(endPoint);
			HttpURLConnection connection = getConnection(url, accessToken);
			sendRequest(connection, JSONSerializer.toJSON(gPayMerchantRefund));
			String response = inputStreamToString(connection.getInputStream());
			GPayMerchantRefund refundResponse = JSONSerializer.toJSON(response, GPayMerchantRefund.class);
			if(refundResponse.getRefundState().equals(GPayRefundStatus.FAILED)){
				request.setPaymentStatus(PaymentStatus.REFUND_FAILED);
				request.setRefundStatus(PaymentStatus.FAILED);
				request.setRefundId(request.getMerchantId());
			} else if(refundResponse.getRefundState().equals(GPayRefundStatus.SUCCEEDED)) {
				request.setPaymentStatus(PaymentStatus.REFUND_PROCESSED);
				request.setRefundStatus(PaymentStatus.CREATED);
				request.setRefundId(request.getMerchantId());
			} else if(refundResponse.getRefundState().equals(GPayRefundStatus.PENDING)){
				request.setPaymentStatus(PaymentStatus.REFUND_INITIATED);
				request.setRefundStatus(PaymentStatus.INITIATED);
				request.setRefundId(request.getMerchantId());

			} else if(refundResponse.getRefundState().equals(GPayRefundStatus.REFUND_STATE_UNSPECIFIED)){
				request.setPaymentStatus(PaymentStatus.REFUND_INITIATED);
				request.setRefundStatus(PaymentStatus.INITIATED);
				request.setRefundId(request.getMerchantId());
			} else {
				throw new UnSpecifiedGPayRefundStatus();
			}
		} catch (Exception e){
			LOG.error("Error processing Refund for google:: {}", e);
			request.setPaymentStatus(PaymentStatus.REFUND_FAILED);
			request.setRefundStatus(PaymentStatus.FAILED);
			request.setRefundId(request.getMerchantId());
		}

		return request;
	}

	private void sendRequest(HttpURLConnection connection, String s) throws IOException {
		connection.setDoOutput(true);
		DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream());
		outputStream.writeBytes(s);
		outputStream.flush();
		outputStream.close();
	}

	private void setRefundId(OrderPayment request) {
		request.setRefundId(request.getPartnerTransactionId() + "_REFUND");
	}

	private GPayMerchantRefund getRefundRequest(EnvType envType, OrderPayment request) throws DataNotFoundException, OrderNotAttachedWithPaymentException {
		GPayMerchantRefund gPayMerchantRefund = new GPayMerchantRefund();
		GPayOriginalTransactionId gPayOriginalTransactionId = new GPayOriginalTransactionId(request.getPartnerTransactionId());
		gPayMerchantRefund.setOriginalTransactionId(gPayOriginalTransactionId);
		GPayAmount amount = new GPayAmount("INR", request.getTransactionAmount().intValue(), 0);
		gPayMerchantRefund.setAmount(amount);
		setRefundId(request);
		String merchantId = request.getMerchantId();
		if(merchantId == null || merchantId.trim().length() == 0){
			Integer orderId = request.getOrderId();
			if(orderId == null){
				// if attached order could not be found then getting merchantId associated with payment
				// will not be possible for legacy payments
				throw new OrderNotAttachedWithPaymentException();
			} else {
				Order order =  orderSearchService.getOrderDetail(orderId);
				UnitBasicDetail unitBasicDetail = masterCache.getUnitBasicDetail(order.getUnitId());
				merchantId = unitBasicDetail.getGoogleMerchantId();
			}
		}
		request.setMerchantId(merchantId);
		return gPayMerchantRefund;
	}


}