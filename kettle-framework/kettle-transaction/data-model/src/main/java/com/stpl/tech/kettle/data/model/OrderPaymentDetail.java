package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "ORDER_PAYMENT_DETAIL")
public class OrderPaymentDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4876301954521731219L;

	private Integer orderPaymentDetailId;
	private Integer orderSettlementId;
	private Integer orderId;
	private String externalOrderId;
	private int paymentModeId;
	private String paymentSource;
	private String paymentModeName;
	private String requestStatus;
	private String paymentStatus;
	private Date requestTime;
	private Date updateTime;
	private Date responseTime;
	private String refundId;
	private String refundRequested;
	private String refundStatus;
	private String refundReason;
	private Date refundRequestTime;
	private Date refundProcessTime;
	private String partnerOrderId;
	private String partnerTransactionId;
	private String partnerPaymentStatus;
	private String redirectUrl;
	private String cancelledBy;
	private String cancellationReason;
	private Date cancellationTime;
	private String failureReason;
	private String cartId;
	private String contactNumber;
	private String customerName;
	private Integer customerId;
	private Date failureTime;
	private BigDecimal transactionAmount;
	private String merchantId;

	private Integer brandId;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_PAYMENT_DETAIL_ID", unique = true, nullable = false)
	public Integer getOrderPaymentDetailId() {
		return orderPaymentDetailId;
	}

	public void setOrderPaymentDetailId(Integer orderPaymentDetailId) {
		this.orderPaymentDetailId = orderPaymentDetailId;
	}

	@Column(name = "ORDER_SETTLEMENT_ID", nullable = true)
	public Integer getOrderSettlementId() {
		return orderSettlementId;
	}

	public void setOrderSettlementId(Integer orderSettlementId) {
		this.orderSettlementId = orderSettlementId;
	}

	@Column(name = "EXTERNAL_ORDER_ID", nullable = true)
	public String getExternalOrderId() {
		return externalOrderId;
	}

	public void setExternalOrderId(String generatedOrderId) {
		this.externalOrderId = generatedOrderId;
	}

	@Column(name = "ORDER_ID", nullable = true)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "PAYMENT_MODE_ID", nullable = false)
	public int getPaymentModeId() {
		return paymentModeId;
	}

	public void setPaymentModeId(int paymentModeId) {
		this.paymentModeId = paymentModeId;
	}

	@Column(name = "PAYMENT_SOURCE", nullable = true)
	public String getPaymentSource() {
		return paymentSource;
	}

	public void setPaymentSource(String paymentSource) {
		this.paymentSource = paymentSource;
	}

	@Column(name = "PAYMENT_MODE_NAME", nullable = true)
	public String getPaymentModeName() {
		return paymentModeName;
	}

	public void setPaymentModeName(String paymentModeName) {
		this.paymentModeName = paymentModeName;
	}

	@Column(name = "PAYMENT_STATUS", nullable = true)
	public String getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(String paymentStaus) {
		this.paymentStatus = paymentStaus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REQUEST_TIME", nullable = true, length = 19)
	public Date getRequestTime() {
		return requestTime;
	}

	public void setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TIME", nullable = true, length = 19)
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "RESPONSE_TIME", nullable = true, length = 19)
	public Date getResponseTime() {
		return responseTime;
	}

	public void setResponseTime(Date responseTime) {
		this.responseTime = responseTime;
	}

	@Column(name = "REFUND_ID", nullable = true, length = 45)
	public String getRefundId() {
		return refundId;
	}

	public void setRefundId(String refundId) {
		this.refundId = refundId;
	}

	@Column(name = "REFUND_REQUESTED", nullable = true)
	public String getRefundRequested() {
		return refundRequested;
	}

	public void setRefundRequested(String refundRequested) {
		this.refundRequested = refundRequested;
	}

	@Column(name = "REFUND_STATUS", nullable = true)
	public String getRefundStatus() {
		return refundStatus;
	}

	public void setRefundStatus(String refundStatus) {
		this.refundStatus = refundStatus;
	}

	@Column(name = "REFUND_REASON", nullable = true)
	public String getRefundReason() {
		return refundReason;
	}

	public void setRefundReason(String refundReason) {
		this.refundReason = refundReason;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REFUND_REQUEST_TIME", nullable = true, length = 19)
	public Date getRefundRequestTime() {
		return refundRequestTime;
	}

	public void setRefundRequestTime(Date refundRequestTime) {
		this.refundRequestTime = refundRequestTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REFUND_PROCESS_TIME", nullable = true, length = 19)
	public Date getRefundProcessTime() {
		return refundProcessTime;
	}

	public void setRefundProcessTime(Date refundProcessTime) {
		this.refundProcessTime = refundProcessTime;
	}

	@Column(name = "PARTNER_TRANSACTION_ID", nullable = true)
	public String getPartnerTransactionId() {
		return partnerTransactionId;
	}

	public void setPartnerTransactionId(String partnerTransactionId) {
		this.partnerTransactionId = partnerTransactionId;
	}

	@Column(name = "PARTNER_ORDER_ID", nullable = true)
	public String getPartnerOrderId() {
		return partnerOrderId;
	}

	public void setPartnerOrderId(String partnerOrderId) {
		this.partnerOrderId = partnerOrderId;
	}

	@Column(name = "PARTNER_PAYMENT_STATUS", nullable = true)
	public String getPartnerPaymentStatus() {
		return partnerPaymentStatus;
	}

	public void setPartnerPaymentStatus(String partnerPaymentStatus) {
		this.partnerPaymentStatus = partnerPaymentStatus;
	}

	@Column(name = "REQUEST_STATUS", nullable = true)
	public String getRequestStatus() {
		return requestStatus;
	}

	public void setRequestStatus(String requestedStaus) {
		this.requestStatus = requestedStaus;
	}

	@Column(name = "REDIRECT_URL", nullable = true)
	public String getRedirectUrl() {
		return redirectUrl;
	}

	public void setRedirectUrl(String value) {
		this.redirectUrl = value;
	}

	@Column(name = "CANCELLED_BY", nullable = true)
	public String getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(String canceledBy) {
		this.cancelledBy = canceledBy;
	}

	@Column(name = "CANCELLATION_REASON", nullable = true)
	public String getCancellationReason() {
		return cancellationReason;
	}

	public void setCancellationReason(String cancelationReason) {
		this.cancellationReason = cancelationReason;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CANCELLATION_TIME", nullable = true, length = 19)
	public Date getCancellationTime() {
		return cancellationTime;
	}

	public void setCancellationTime(Date cancelationTime) {
		this.cancellationTime = cancelationTime;
	}

	@Column(name = "FAILURE_REASON", nullable = true)
	public String getFailureReason() {
		return failureReason;
	}

	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "FAILURE_TIME", nullable = true, length = 19)
	public Date getFailureTime() {
		return failureTime;
	}

	public void setFailureTime(Date failureTime) {
		this.failureTime = failureTime;
	}

	/**
	 * @return the cartId
	 */
	@Column(name = "CART_ID")
	public String getCartId() {
		return cartId;
	}

	/**
	 * @param cartId the cartId to set
	 */
	public void setCartId(String cartId) {
		this.cartId = cartId;
	}

	/**
	 * @return the contactNumber
	 */
	@Column(name = "CONTACT_NUMBER")
	public String getContactNumber() {
		return contactNumber;
	}

	/**
	 * @param contactNumber the contactNumber to set
	 */
	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	/**
	 * @return the customerName
	 */
	@Column(name = "CUSTOMER_NAME")
	public String getCustomerName() {
		return customerName;
	}

	/**
	 * @param customerName the customerName to set
	 */
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	/**
	 * @return the customerId
	 */
	@Column(name = "CUSTOMER_ID")
	public Integer getCustomerId() {
		return customerId;
	}

	/**
	 * @param customerId the customerId to set
	 */
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	/**
	 * @return the transactionAmount
	 */
	@Column(name = "TRANSACTION_AMOUNT", precision = 10)
	public BigDecimal getTransactionAmount() {
		return transactionAmount;
	}

	/**
	 * @param transactionAmount the transactionAmount to set
	 */
	public void setTransactionAmount(BigDecimal transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	@Column(name = "MERCHANT_ID")
	public String getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	@Column(name = "BRAND_ID")
	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}
}
