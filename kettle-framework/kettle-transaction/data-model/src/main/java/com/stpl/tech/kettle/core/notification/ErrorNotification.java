/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.util.notification.EmailNotification;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class ErrorNotification extends EmailNotification implements Notification {

	private static final Logger LOG = LoggerFactory.getLogger(ErrorNotification.class);

	private enum NotificationType {
		EXCEPTION, MSG_LIST
	}

	private String subject;
	private String error;
	private Exception exception;
	private EnvType env;
	private Date errorDate;
	private List<String> errorMessages;
	private NotificationType notificationType;
	private List<String> toEmailList;
	private List<String> toSlackList;
	
	public ErrorNotification(String subject, String error, Exception e, EnvType env) {
		this.subject = subject;
		this.error = error;
		this.exception = e;
		this.env = env;
		this.errorDate = AppUtils.getCurrentTimestamp();
		this.notificationType = NotificationType.EXCEPTION;
	}

	public ErrorNotification(String subject, String error, List<String> errorMessages, EnvType env) {
		this.subject = subject;
		this.error = error;
		this.errorMessages = errorMessages;
		this.env = env;
		this.errorDate = AppUtils.getCurrentTimestamp();
		this.notificationType = NotificationType.MSG_LIST;
	}

	public String subject() {
		return (TransactionUtils.isDev(getEnvironmentType()) ? "DEV : " : "") + "ERROR Notification : " + subject + " on "
				+ new SimpleDateFormat("yyyy-MM-dd").format(errorDate);
	}

	public String body() throws EmailGenerationException {

		StringBuffer body = new StringBuffer(
				"<html><p><b>Error Notification : </b>" + subject + "<br/> <b>Error Generation Timestamp: </b>"
						+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(errorDate) + "</p>");

		body.append("<p><b> Error Summary: </b>" + error + "<br/></p>");
		if (NotificationType.EXCEPTION.equals(notificationType)) {

			body.append("<b> Error: </b>" + exception.getMessage() == null ? "NO STACK TRACE"
					: exception.getMessage().replace("\n", "\n <br/>") + "<br/><b>Full Stack Trace:</b><br/>"
							+ "</html>");

		} else if (NotificationType.MSG_LIST.equals(notificationType)) {

			body = body.append("<p><b>Error Messages</b><br/></p>");
			if (errorMessages != null) {
				for (String msg : errorMessages) {
					body.append(msg + "<br/>");
				}
			}
		}

		return body.toString();
	}

	public String getFromEmail() {
		return "<EMAIL>";
	}

	public String[] getToEmails() {
		return toEmailList != null && !toEmailList.isEmpty() ? toEmailList.toArray(new String[toEmailList.size()])
				: new String[] { "<EMAIL>" };
	}

	@Override
	public void sendEmail() {
		try {
			super.sendEmail();
			if (AppUtils.isProd(env) && toSlackList != null && !toSlackList.isEmpty()) {
				for (String channel : toSlackList) {
					SlackNotificationService.getInstance().sendNotification(env, "Kettle", channel, this);
				}
			}
			SlackNotificationService.getInstance().sendNotification(env, "Kettle", SlackNotification.SYSTEM_ERRORS,
					this);
		} catch (Exception e) {
			LOG.error("Error Notification Failure", e);
		}
	}

	@Override
	public String getNotificationMessage() {
		StringBuffer body = new StringBuffer("Error Notification : " + subject + "\nError Generation Timestamp: "
				+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(errorDate));
		body.append("\nError Summary: " + error);
		if (NotificationType.EXCEPTION.equals(notificationType)) {			
			String stackTrace = org.apache.commons.lang.exception.ExceptionUtils.getStackTrace(exception);
			body.append("Error: " + exception.getMessage() + "Full Stack Trace: "
					+ stackTrace.substring(0, stackTrace.length() / 10));
			
		} else if (NotificationType.MSG_LIST.equals(notificationType)) {
			
			body = body.append("\nError Messages");
			if (errorMessages != null) {
				for (String msg : errorMessages) {
					body.append("\n" + msg);
				}
			}
		}
		return body.toString();
	}

	@Override
	public EnvType getEnvironmentType() {
		return env;
	}

	public void setToEmailList(List<String> toList) {
		this.toEmailList = toList;
	}

	public void setToSlackList(List<String> toSlackList) {
		this.toSlackList = toSlackList;
	}
	
}
