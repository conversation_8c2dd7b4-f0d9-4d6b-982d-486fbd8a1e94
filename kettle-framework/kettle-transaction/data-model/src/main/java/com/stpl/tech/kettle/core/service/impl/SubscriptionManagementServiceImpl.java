/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.notification.SubscriptionInfo;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.core.service.SubscriptionManagementService;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.SubscriptionManagementDao;
import com.stpl.tech.kettle.data.model.SubscriptionEventDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Subscription;
import com.stpl.tech.kettle.domain.model.SubscriptionEvent;
import com.stpl.tech.kettle.domain.model.SubscriptionEventStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionStatusEvents;
import com.stpl.tech.kettle.domain.model.SubscriptionViewData;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.jms.JMSException;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 */
@Service
public class SubscriptionManagementServiceImpl implements SubscriptionManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(SubscriptionManagementServiceImpl.class);

	@Autowired
	private SubscriptionManagementDao dao;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private CustomerDao customerDao;

	@Autowired
	private EnvironmentProperties environmentProperties;


	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.OrderManagementService#createOrder(com.
	 * stpl.tech.kettle.domain.model.Order)
	 */
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String createSubscription(Order order) throws DataUpdationException, DataNotFoundException {
		order.setGenerateOrderId(AppUtils.generateRandomOrderId());
		return dao.createSubscription(order);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Subscription updateSubscription(Subscription subscription) {
		return dao.updateSubscription(subscription);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<SubscriptionEventDetail> getOrdersToBeCreated(Date currentTimestamp) {
		return dao.getOrdersToBeCreated(currentTimestamp);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Order getOrder(int subscriptionId) throws DataNotFoundException {
		return dao.getOrder(subscriptionId);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateEventStatus(Integer subscriptionEventDetailId, Integer orderId,
			SubscriptionEventStatus status) {
		return dao.updateEventStatus(subscriptionEventDetailId, orderId, status);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public SubscriptionEventDetail cloneEventStatus(Integer cloneEventDetailId, SubscriptionEventStatus status) {
		return dao.cloneEventStatus(cloneEventDetailId, status);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<SubscriptionEventDetail> createAllSubscriptionsForToday() {
		return dao.createAllSubscriptionsADay(AppUtils.getCurrentDate(), true);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<SubscriptionEventDetail> createAllSubscriptionsForToday(int subscriptionId) {
		return dao.createAllSubscriptionsForADay(AppUtils.getCurrentDate(), subscriptionId, true);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void putSubscriptionsOnHoldForToday() {
		dao.putSubscriptionsOnHoldForToday();
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void takeSubscriptionsOffHoldForToday() {
		dao.takeSubscriptionsOffHoldForToday();
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void cancelSubscriptionsForToday() {
		dao.cancelSubscriptionsForToday();
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public SubscriptionInfo getSubscriptionInfo(int subscriptionId) throws DataNotFoundException {
		return dao.getSubscriptionInfo(subscriptionId);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean holdOrCancelSubscription(int subscriptionId, SubscriptionStatus status, boolean effectiveImmediate,
			Date startDate, Date endDate, int generatedBy, String reasonText) {
		boolean response = dao.createSubscriptionStatusEvent(subscriptionId, status, startDate, endDate, generatedBy,
				reasonText, effectiveImmediate);
		if (effectiveImmediate && AppUtils.getCurrentDate().equals(startDate)) {
			response = response
					&& dao.cancelAllSubscriptionsForADay(subscriptionId, startDate, endDate, reasonText, true);
		}

		return response;
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean cancelHold(int subscriptionEventDetailId) {
		return dao.cancelHold(subscriptionEventDetailId);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateHold(int subscriptionEventDetailId, Date endDate, boolean withImmediateEffect) {
		return dao.updateHold(subscriptionEventDetailId, endDate, withImmediateEffect);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getSubscriptions(String contactNumber) throws DataNotFoundException {
		return dao.getSubscriptions(contactNumber);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<SubscriptionEvent> getSubscriptionOrders(String contactNumber) throws DataNotFoundException {
		return dao.getSubscriptionOrders(contactNumber);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<SubscriptionEvent> getSubscriptionOrders(int unitId) throws DataNotFoundException {
		return dao.getSubscriptionOrders(unitId);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<SubscriptionStatusEvents> getStatusEventsForSubscription(Integer subscriptionId)
			throws DataNotFoundException {
		return dao.getStatusEventsForSubscription(subscriptionId);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateHold(int subscriptionEventDetailId, Date startDate, Date endDate, boolean withImmediateEffect,
			String reason) {
		return dao.updateHold(subscriptionEventDetailId, startDate, endDate, withImmediateEffect, reason);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateCancel(int subscriptionEventDetailId, Date startDate, String reason) {
		return dao.updateCancel(subscriptionEventDetailId, startDate, reason);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public SubscriptionEvent updateEventData(SubscriptionEvent event)
			throws DataUpdationException, DataNotFoundException {
		return dao.updateEventData(event);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void sendReminderForNotUsingSubscriptionNthDay(int nthDay, String type) {
		List<SubscriptionViewData> subscriptionViewData = dao.getAllNotUsedSubscriptionNthDay(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), nthDay),Math.abs(nthDay));
		if (Objects.nonNull(subscriptionViewData) && !subscriptionViewData.isEmpty()) {
			Brand brand = masterDataCache.getBrandMetaData().get(AppConstants.CHAAYOS_BRAND_ID);
			SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
			subscriptionViewData.forEach(
					view ->
					{
						try {
							Pair<CouponDetail, Product> couponMapping = masterDataCache.getSubscriptionSkuCodeDetail(view.getSubscriptionPlanCode());
							Customer customer = customerDao.getCustomer(view.getCustomerId());
							if (Objects.nonNull(customer)){
								view.setOfferDescription(couponMapping.getKey().getOffer().getDescription());
								view.setCustomerName(customer.getFirstName());
								view.setSubscriptionName(couponMapping.getValue().getName());
								view.setnThDay(Math.abs(nthDay));
								view.setValidityDays(AppUtils.getDaysDiff(view.getPlanStartDate(), view.getPlanEndDate()));
								sendReminderNotification(smsWebServiceClient, CustomerSMSNotificationType.valueOf(type), view, customer);
							}
						} catch (DataNotFoundException | JMSException | IOException e) {
							LOG.error("CHAAYOS_SUBSCRIPTION_REMINDER ### Exception while retreiving customer Info for {}", view.getCustomerId());
						}
					}
			);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void sendChaayosSubscriptionExpiryReminder(Integer nThDay) {
		LOG.info("CHAAYOS_SUBSCRIPTION_EXPIRY_REMINDER ### sending expiry reminder for {}th day",nThDay);
		Date currentDate = AppUtils.getCurrentTimestamp();
		Date expirationDate = AppUtils.addDays(currentDate, nThDay);
		Map<String, String> buyLinkMap = new HashMap<>();
		List<SubscriptionViewData> subscriptionPlans = dao.getAllExpiringSubscriptionOnNthDay(expirationDate);
		if (Objects.nonNull(subscriptionPlans) && !subscriptionPlans.isEmpty()) {
			Brand brand = masterDataCache.getBrandMetaData().get(AppConstants.CHAAYOS_BRAND_ID);
			SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
			subscriptionPlans.forEach(
					view ->
					{
						try {
							Pair<CouponDetail, Product> couponMapping = masterDataCache.getSubscriptionSkuCodeDetail(view.getSubscriptionPlanCode());
							Customer customer = customerDao.getCustomer(view.getCustomerId());
							if (buyLinkMap.containsKey(view.getSubscriptionPlanCode())) {
								view.setBuyLink(buyLinkMap.get(view.getSubscriptionPlanCode()));
							} else {
								URIBuilder builder = new URIBuilder(environmentProperties.getMembershipBuyLinkBasePath() + "/" + view.getSubscriptionPlanCode());
								ShortUrlData shortUrlBuyLink = smsWebServiceClient.getShortUrl(builder.build().toURL().toString());
								buyLinkMap.put(view.getSubscriptionPlanCode(), shortUrlBuyLink.getUrl());
								view.setBuyLink(shortUrlBuyLink.getUrl());
							}
							if (Objects.nonNull(customer)) {
								view.setOfferDescription(couponMapping.getKey().getOffer().getDescription());
								view.setSubscriptionName(couponMapping.getValue().getName());
								view.setnThDay(Math.abs(nThDay));
								sendReminderNotification(smsWebServiceClient, CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_EXPIRY_REMINDER, view, customer);
							}
						} catch (DataNotFoundException | JMSException | IOException | URISyntaxException e) {
							LOG.error("CHAAYOS_SUBSCRIPTION_REMINDER ### Exception while retreiving customer Info for {}", view.getCustomerId());
						}
					}
			);
		}
	}

	private void sendReminderNotification(SMSWebServiceClient smsWebServiceClient, CustomerSMSNotificationType type, SubscriptionViewData view, Customer customer) throws JMSException, IOException {
		String message = type.getMessage(view);
		notificationService.sendNotification(type.name(),
				message, customer.getContactNumber(), smsWebServiceClient, environmentProperties.getAutomatedNPSSMS(),
				getNotificationPayload(type,view,customer));
	}

	private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, SubscriptionViewData view, Customer customer) {
		try {
			NotificationPayload load = new NotificationPayload();
			if (Objects.nonNull(view.getCustomerId())) {
				load.setCustomerId(view.getCustomerId());
			}
			load.setContactNumber(customer.getContactNumber());
			load.setMessageType(type.name());
			load.setSendWhatsapp(type.isWhatsapp());
			if (Objects.nonNull(customer.getOptWhatsapp())) {
				load.setWhatsappOptIn(customer.getOptWhatsapp().equals(AppConstants.YES));
			} else {
				load.setWhatsappOptIn(false);
			}
			load.setRequestTime(AppUtils.getCurrentTimestamp());
			Map<String, String> map = new HashMap<>();
			load.setPayload(map);
			return load;
		} catch (Exception e){
			LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",view.getCustomerId());
			return null;
		}
	}
}
