package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.DroolFileType;
import com.stpl.tech.kettle.data.model.DroolsCustomerProperties;
import com.stpl.tech.kettle.data.model.DroolsDecisionTableData;
import com.stpl.tech.kettle.data.model.GamifiedOfferType;
import com.stpl.tech.kettle.data.model.RecomOfferData;
import com.stpl.tech.kettle.data.model.SuperUDomain;
import com.stpl.tech.kettle.data.model.WalletRecommendationDetail;
import com.stpl.tech.kettle.data.model.WalletSuggestionCustomerInfo;
import com.stpl.tech.kettle.data.model.WalletSuggestionData;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface DroolsDecisionService {

    public Pair<String,String> getOfferString(DroolsCustomerProperties properties, boolean isPosOffer,String offerType, Integer unitId);

    public RecomOfferData getRecomOfferData(DroolsCustomerProperties properties,Integer unitId);

//    public boolean resetOfferDecisionDrools(MultipartFile file,String droolFileType, boolean persist);

    public void downloadRecipeMedia(HttpServletResponse response, String fileName, String droolFileType, String version) throws IOException;
    WalletSuggestionData getCustomerWalletSuggestionData(String customerId, String brandId);

    WalletRecommendationDetail getCustomerWalletRecommendation(WalletSuggestionCustomerInfo customerData);

    boolean activateVersion(String fileName, String droolFileType, String version) throws IOException;

    List<DroolsDecisionTableData> fetchAllFileByType(String fileType);

    public boolean isDroolContainerInitializeForOfferDecision(String version);

    public boolean isDroolContainerInitializeForRecomOfferDecision(String version);

    boolean isDroolContainerInitializeForWalletRecommendation(String version);

    boolean isDroolContainerInitializeForSuperU(String version);

    public void initailizeDroolContainer(String droolFileType, String version);

    public boolean addNewDroolFile(MultipartFile file,String droolFileType, boolean persist);

    boolean setDefaultDroolSheet(String droolFileType, String version, String fileName);
    boolean inactivateVersion(String droolFileType, String version, String fileName);

    SuperUDomain getSuperUParam(SuperUDomain superUDomain);
}
