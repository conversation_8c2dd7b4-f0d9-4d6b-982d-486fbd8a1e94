/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.InventoryThresholdType;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppUtils;

public class InventoryStockOutEmail extends EmailNotification {

	InventoryStockOutNotification notification;
	EnvironmentProperties props;

	public InventoryStockOutEmail(InventoryStockOutNotification notification, EnvironmentProperties props) {
		this.notification = notification;
		this.props = props;
	}

	@Override
	public String[] getToEmails() {
		// TODO externalize the email ids
		String toEmail = TransactionUtils.isCODUnit(notification.getUnitCategory()) ? "<EMAIL>"
				: "<EMAIL>";
		return TransactionUtils.isDev(props.getEnvironmentType()) ? new String[] { "<EMAIL>" }
				: new String[] { toEmail };
	}

	@Override
	public String getFromEmail() {
		return AppUtils.getFormattedEmail(TransactionUtils.getPrefix(props.getEnvironmentType()) + "Cafe Admin",
				"<EMAIL>");
	}

	@Override
	public String subject() {
		String subject = null;
		UnitCategory prefix = notification.getUnitCategory();
		String isDev = "";
		if (TransactionUtils.isDev(props.getEnvironmentType())) {
			isDev = "DEV";
		}
		if (TransactionUtils.isCODUnit(notification.getUnitCategory())) {
			prefix = UnitCategory.COD;
		}
		if (notification.getThresholdType() == InventoryThresholdType.STOCK_OUT) {
			subject = isDev + " (" + prefix.name() + ")" + " Stock Out Report For " + AppUtils.getCurrentTimestamp();
		} else if (notification.getThresholdType() == InventoryThresholdType.PROBABLE_STOCK_OUT) {
			subject = isDev + " (" + prefix.name() + ")" + " Probable Stock Out Report For "
					+ AppUtils.getCurrentTimestamp();
		}
		return subject;
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return notification.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return props.getEnvironmentType();
	}

}
