/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.monitoring;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

import com.stpl.tech.master.core.service.model.ScreenType;
import com.stpl.tech.master.domain.model.UnitCategory;

public class UnitMonitorData implements Delayed {

	private int unitId;
	private String unitName;
	private UnitCategory category;
	private int terminalId;
	private ScreenType screenType;
	private long startTime;

	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}

	public UnitMonitorData() {
		super();
	}

	public UnitMonitorData(int unitId, String unitName, int terminalId, ScreenType screenType, UnitCategory category,
			long delay) {
		super();
		this.unitId = unitId;
		this.terminalId = terminalId;
		this.screenType = screenType;
		this.unitName = unitName;
		this.category = category;
		this.startTime = System.currentTimeMillis() + delay;

	}

	public long getDelay(TimeUnit unit) {
		long diff = startTime - System.currentTimeMillis();
		return unit.convert(diff, TimeUnit.MILLISECONDS);
	}

	public long getStartTime() {
		return startTime;
	}

	public int getUnitId() {
		return unitId;
	}

	public int getTerminalId() {
		return terminalId;
	}

	public ScreenType getScreenType() {
		return screenType;
	}

	public int compareTo(Delayed o) {
		if (this.startTime < ((UnitMonitorData) o).startTime) {
			return -1;
		}
		if (this.startTime > ((UnitMonitorData) o).startTime) {
			return 1;
		}
		return 0;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((screenType == null) ? 0 : screenType.hashCode());
		result = prime * result + terminalId;
		result = prime * result + unitId;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null) {
			return false;
		}
		if (getClass() != obj.getClass()) {
			return false;
		}
		UnitMonitorData other = (UnitMonitorData) obj;
		if (screenType == null) {
			if (other.screenType != null) {
				return false;
			}
		} else if (!screenType.equals(other.screenType)) {
			return false;
		}
		if (terminalId != other.terminalId) {
			return false;
		}
		if (unitId != other.unitId) {
			return false;
		}
		return true;
	}

	@Override
	public String toString() {
		return "{" + "unitId='" + unitId + '\'' + "terminalId='" + terminalId + '\'' + '\'' + "screenType='"
				+ screenType.name() + '\'' + ", startTime=" + startTime + '}';
	}

	public String getUnitName() {
		return unitName;
	}

	public UnitCategory getCategory() {
		return category;
	}

}
