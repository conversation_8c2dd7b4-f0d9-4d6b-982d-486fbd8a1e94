package com.stpl.tech.kettle.core.payment.adapter;

import com.stpl.tech.kettle.core.payment.PaymentAdapter;
import com.stpl.tech.kettle.core.service.PayTMNewPaymentService;
import com.stpl.tech.kettle.core.service.impl.PayTMNewPaymentServiceImpl;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequestResponseWrapper;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
@Component
public class PaytmNewAdapter extends PaymentAdapter<OrderPaymentRequest,
        PaytmCreateRequestResponseWrapper> {

    @Autowired
    PayTMNewPaymentService payTMNewPaymentService;

    @Override
    public PaytmCreateRequestResponseWrapper createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception{
        return payTMNewPaymentService.createPaymentRequest(orderPaymentRequest, map);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification,Integer brandId) {
        PaytmCreateResponse paytmCreateResponse = (PaytmCreateResponse)object;
        if(skipSignatureVerification){
            return payTMNewPaymentService.updatePaytmResponseWithoutVerification(paytmCreateResponse);
        } else {
            return payTMNewPaymentService.updatePaytmResponse(paytmCreateResponse, true);
        }
    }
}
