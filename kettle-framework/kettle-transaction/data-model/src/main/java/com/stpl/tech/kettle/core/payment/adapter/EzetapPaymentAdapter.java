package com.stpl.tech.kettle.core.payment.adapter;

import com.stpl.tech.kettle.core.payment.PaymentAdapter;
import com.stpl.tech.kettle.core.service.EzetapPaymentService;
import com.stpl.tech.kettle.core.service.impl.EzetapPaymentServiceImpl;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.ezetap.EzetapCreateRequest;
import com.stpl.tech.master.payment.model.ezetap.EzetapPaymentResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class EzetapPaymentAdapter extends PaymentAdapter<OrderPaymentRequest,
        EzetapCreateRequest> {

    @Autowired
    EzetapPaymentService ezetapPaymentService;

    @Override
    public EzetapCreateRequest createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {
        return ezetapPaymentService.createEzetapRequest(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification,Integer brandId) {
        EzetapPaymentResponse ezetapPaymentResponse = (EzetapPaymentResponse)object;
        return ezetapPaymentService.updateEzetapResponse(ezetapPaymentResponse);
    }
}
