/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderSettlement generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ORDER_SETTLEMENT")
public class OrderSettlement implements java.io.Serializable {

	private Integer settlementId;
	private OrderDetail orderDetail;
	private int paymentModeId;
	private BigDecimal amountPaid;
	private BigDecimal roundOffAmount;
	private BigDecimal extraVouchers;
	private String edited;
	private Integer editedBy;
	private Integer previousPaymentMode;
	private Date editTime;
	private List<OrderPaymentDenominationDetail> denominations = new ArrayList<OrderPaymentDenominationDetail>();
	private List<OrderExternalSettlementData> externalTransactions = new ArrayList<OrderExternalSettlementData>();

	public OrderSettlement() {
	}

	public OrderSettlement(OrderDetail orderDetail, int paymentModeId) {
		this.orderDetail = orderDetail;
		this.paymentModeId = paymentModeId;
	}

	public OrderSettlement(OrderDetail orderDetail, int paymentModeId, BigDecimal amountPaid,
			BigDecimal roundOffAmount) {
		this.orderDetail = orderDetail;
		this.paymentModeId = paymentModeId;
		this.amountPaid = amountPaid;
		this.roundOffAmount = roundOffAmount;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SETTLEMENT_ID", unique = true, nullable = false)
	public Integer getSettlementId() {
		return this.settlementId;
	}

	public void setSettlementId(Integer settlementId) {
		this.settlementId = settlementId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	public OrderDetail getOrderDetail() {
		return this.orderDetail;
	}

	public void setOrderDetail(OrderDetail orderDetail) {
		this.orderDetail = orderDetail;
	}

	@Column(name = "PAYMENT_MODE_ID", nullable = false)
	public int getPaymentModeId() {
		return this.paymentModeId;
	}

	public void setPaymentModeId(int paymentModeId) {
		this.paymentModeId = paymentModeId;
	}

	@Column(name = "AMOUNT_PAID", precision = 10)
	public BigDecimal getAmountPaid() {
		return this.amountPaid;
	}

	public void setAmountPaid(BigDecimal amountPaid) {
		this.amountPaid = amountPaid;
	}

	@Column(name = "ROUND_OFF_AMOUNT", precision = 10)
	public BigDecimal getRoundOffAmount() {
		return this.roundOffAmount;
	}

	public void setRoundOffAmount(BigDecimal roundOffAmount) {
		this.roundOffAmount = roundOffAmount;
	}

	@Column(name = "EXTRA_VOUCHERS", precision = 10)
	public BigDecimal getExtraVouchers() {
		return extraVouchers;
	}

	public void setExtraVouchers(BigDecimal extraVouchers) {
		this.extraVouchers = extraVouchers;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderSettlement")
	public List<OrderPaymentDenominationDetail> getDenominations() {
		return denominations;
	}

	public void setDenominations(List<OrderPaymentDenominationDetail> denominations) {
		this.denominations = denominations;
	}

	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderSettlement")
	public List<OrderExternalSettlementData> getExternalTransactions() {
		return externalTransactions;
	}

	public void setExternalTransactions(List<OrderExternalSettlementData> externalTransactions) {
		this.externalTransactions = externalTransactions;
	}

	@Column(name = "IS_EDITED", nullable = true)
	public String getEdited() {
		return edited;
	}

	public void setEdited(String edited) {
		this.edited = edited;
	}
	
	@Column(name = "EDITED_BY", nullable = true)
	public Integer getEditedBy() {
		return editedBy;
	}

	public void setEditedBy(Integer editedBy) {
		this.editedBy = editedBy;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EDIT_TIME", nullable = true, length = 19)
	public Date getEditTime() {
		return editTime;
	}

	public void setEditTime(Date editTime) {
		this.editTime = editTime;
	}
	
	@Column(name = "PREVIOUS_PAYMENT_MODE_ID", nullable = true)
	public Integer getPreviousPaymentMode() {
		return previousPaymentMode;
	}

	public void setPreviousPaymentMode(Integer previousPaymentMode) {
		this.previousPaymentMode = previousPaymentMode;
	}
	
}
