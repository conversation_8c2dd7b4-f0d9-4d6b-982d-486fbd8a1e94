/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.Gson;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.cache.DeliveryPartnerPriorityMappings;
import com.stpl.tech.kettle.core.cache.DeliveryServiceCache;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.cache.UnitDeliverySelectionCache;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.core.service.DeliveryRequestService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.DeliveryDao;
import com.stpl.tech.kettle.data.model.DeliveryDetail;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.kettle.delivery.strategy.DeliveryRouter;
import com.stpl.tech.kettle.delivery.strategy.SelectionStrategy;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderFeedback;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import com.stpl.tech.kettle.domain.model.PartnerOrderStates;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.SDPAllocationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.PaymentCategory;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.TemplateRenderingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.jms.JMSException;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DeliveryRequestServiceImpl implements DeliveryRequestService {

	private static final Logger LOG = LoggerFactory.getLogger(DeliveryRequestServiceImpl.class);

	@Autowired
	private OrderInfoCache infoCache;

	@Autowired
	private MetadataCache metaCache;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private DeliveryDao deliveryDao;

	@Autowired
	private DeliveryRouter router;

	@Autowired
	private SelectionStrategy<UnitDeliverySelectionCache, Integer> selectionStrategy;

	@Autowired
	private DeliveryPartnerPriorityMappings unitToDeliveryMappingsCache;

	@Autowired
	private SimpMessagingTemplate template;

	@Autowired
	private DeliveryServiceCache deliveryServiceCache;

	@Autowired
	private NotificationService notificationService;
	@Autowired
	private EnvironmentProperties props;
	@Autowired
    private SMSClientProviderService providerService;

	@Autowired
	private SQSNotificationService sqsNotificationService;

	private DeliveryResponse createDeliveryPartnerRequest(OrderInfo orderObject, Integer count) {
		int unitId = orderObject.getOrder().getUnitId();
		Order order = orderObject.getOrder();
		LOG.info("Unit is {} and orderId is {}", unitId, order.getOrderId());
		int partnersForUnit = unitToDeliveryMappingsCache.getRetriesForUnit(unitId);
		if (count >= partnersForUnit) {
			return null;
		} else {
			Integer partnerId = getNextPartnerFromCache(unitId);
			DeliveryResponse deliveryResponse;
			try {
				if (checkIfAutomated(partnerId) && order.getSettlements() != null
						&& checkCashOrderEligibility(order.getSettlements(), partnerId)) {

					deliveryResponse = router.createDeliveryInSystem(orderObject, getMappingForPartner(partnerId));
					if (deliveryResponse == null || (deliveryResponse
							.getDeliveryStatus() == DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus())) {
						return createDeliveryPartnerRequest(orderObject, count + 1);
					} else {
						refreshDeliveryCache(unitId);
						return deliveryResponse;
					}
				} else {
					LOG.info("Delivery Partner {} is currently not automated via API", partnerId);
					return createDeliveryPartnerRequest(orderObject, count + 1);
				}
			} catch (CloneNotSupportedException e) {
				LOG.error("Cannot create clone of object", e);
			}
			return null;
		}
	}

	private boolean checkCashOrderEligibility(List<Settlement> orderSettlements, Integer partnerId) {
		boolean flag = true;
		boolean isCashOrder = (!orderSettlements.isEmpty() && orderSettlements.get(0).getMode() == 1);
		if (isCashOrder) {
			String partnerStatus = deliveryServiceCache.getCashEligibleStatus(partnerId);
			flag = (partnerStatus == null || partnerStatus.equalsIgnoreCase("Y"));
		}
		return flag;
	}

	private void refreshDeliveryCache(int unitId) {
		selectionStrategy.refreshDeliveryCache(unitToDeliveryMappingsCache.getSelectionCacheForUnit(unitId));
	}

	private Map<String, String> getMappingForPartner(Integer partnerId) {
		return deliveryServiceCache.getApiProfiles().get(partnerId);
	}

	private Integer getNextPartnerFromCache(Integer unitId) {
		Integer selection = selectionStrategy
				.getSelectionForTicket(unitToDeliveryMappingsCache.getSelectionCacheForUnit(unitId));
		return selection;
	}

	private OrderInfo updateOrderWithDelivery(OrderInfo info, DeliveryResponse delivery, IdCodeName deliveryPartner) {
		DeliveryDetail deliveryDetails = DataConverter.convert(delivery);
		// update delivery details in cache & database
		info = updateDeliveryDetails(info, deliveryDetails, deliveryPartner);
		return info;
	}

	private OrderInfo updateDeliveryDetails(OrderInfo info, DeliveryDetail deliveryDetails,
			IdCodeName deliveryPartner) {
		if (deliveryDetails != null && deliveryDetails.getDeliveryStatus() != null && !DeliveryStatus
				.valueOf(deliveryDetails.getDeliveryStatus()).equals(DeliveryStatus.REQUEST_DECLINED)) {

			info.setDeliveryPartner(deliveryPartner);
			info.setDeliveryDetails(DataConverter.convert(info.getOrder().getUnitId(), deliveryDetails));
		} else {
			info.setDeliveryPartner(metaCache.getDeliveryPartner(1));
			info.setDeliveryDetails(null);
		}
		boolean updateInDB = updateOrderInfo(info); // in database
		LOG.info("Order updation status in database after creation :::: {}", updateInDB);

		return info;
	}

	private boolean updateOrderInfo(OrderInfo info) {
		try {
			int deliveryPartnerId = info.getDeliveryPartner().getId();
			Order order = info.getOrder();
			order.setDeliveryPartner(deliveryPartnerId);
			return deliveryDao.updateOrder(order);
		} catch (DataUpdationException e) {
			LOG.error("error in updating delivery partner productId for orderId :::: {}",
					info.getOrder().getGenerateOrderId());
		}
		return false;
	}

	private void saveDeliveryDetails(DeliveryResponse response, OrderInfo orderInfo) {
		if (response != null && response.getDeliveryTaskId() != null) {
			response.setOrderId(orderInfo.getOrder().getOrderId());
			deliveryDao.saveDeliveryDetails(orderInfo.getOrder().getUnitId(), response);
		}
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public DeliveryResponse createDeliveryRequest(OrderInfo orderInfo, Unit unit) {
		orderInfo.setUnit(unit);
		LOG.info("unit productId for orderInfo is {} ", orderInfo.getUnit().getId());
		DeliveryResponse response = createDeliveryPartnerRequest(orderInfo, 0);
		saveDeliveryDetails(response, orderInfo);
		return response;
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public DeliveryResponse createDeliveryRequest(OrderInfo info, Unit unit, int deliveryPartnerId) {
		info.setUnit(unit);
		LOG.info("unit productId for orderInfo is {} ", info.getUnit().getId());
		try {
			if (checkCashOrderEligibility(info.getOrder().getSettlements(), deliveryPartnerId)) {
				DeliveryResponse response = router.createDeliveryInSystem(info,
						getMappingForPartner(deliveryPartnerId));
				saveDeliveryDetails(response, info);
				return response;
			}
		} catch (CloneNotSupportedException e) {
			LOG.error("Unable to create delivery request since cloning is not supported");
		}
		return null;
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public DeliveryResponse cancelDeliveryRequest(String generateOrderId, int orderId, int unitId)
			throws DataNotFoundException, TemplateRenderingException {

		OrderInfo orderInfo = infoCache.getOrderById(orderId, UnitCategory.COD);
		String webSocketChannel = TransactionConstants.WEB_SOCKET_CHANNEL + unitId
				+ TransactionConstants.WEB_SOCKET_CHANNEL_ORDERS;

		List<DeliveryDetail> deliveryTasks = getDeliveryDetails(generateOrderId);
		DeliveryResponse response = new DeliveryResponse();
		if (deliveryTasks != null && deliveryTasks.size() > 0) {
			DeliveryDetail deliveryTask = deliveryTasks.get(0);
			Map<String, String> partnerIdMap = getMappingForPartner(deliveryTask.getDeliveryPartnerId());
			if (partnerIdMap != null) {
				response = router.cancelDelivery(deliveryTask.getDeliveryTaskId(), orderInfo,
						getMappingForPartner(deliveryTask.getDeliveryPartnerId()));
				if (response != null && response.getDeliveryStatus() == DeliveryStatus.CANCELLED.getDeliveryStatus()) {
					LOG.info("delivery Status of response is {} ", response.getDeliveryStatus());
					response.setOrderId(deliveryTask.getOrderId());
					orderInfo.setDeliveryDetails(null);
					orderInfo.setDeliveryPartner(metaCache.getDeliveryPartner(1));
					if (infoCache.getValidOrderStatuses().contains(orderInfo.getOrder().getStatus())) {
						infoCache.addToCache(orderInfo);
						infoCache.addToUndelivered(orderInfo);
					}
					deliveryDao.mergeCancellationDetails(response);
				}
				deliveryDao.mergeCancellationDetails(response);
			}
		}
		template.convertAndSend(webSocketChannel, orderInfo);
		return response;
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public String registerMerchant(Unit unit, Integer partnerId) {
		try {
			return router.createMerchantIdInSystem(unit, getMappingForPartner(partnerId));
		} catch (JsonProcessingException e) {
			LOG.error("Json parsing exception ::::", e);
		}
		return null;
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public List<OrderFeedback> getOrderListForFeedback(String deliveryPersonContact, String feedbackStatus) {
		return deliveryDao.getOrderListForFeedback(deliveryPersonContact, feedbackStatus);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public String submitOrderDeliveryFeedback(int deliveryId, String code) {
		return deliveryDao.submitOrderDeliveryFeedback(deliveryId, code);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public Integer updateDelivery(DeliveryResponse response) throws DataNotFoundException, TemplateRenderingException {
		LOG.info("Inside updateDelivery function of delivery request service :::: {} ", response.toString());
		Integer flag = 0;
		DeliveryDetail updatedResponse = deliveryDao.updateDelivery(response);

		if (updatedResponse != null) {
			OrderInfo orderInfo = infoCache.getOrderById(updatedResponse.getOrderId(), UnitCategory.COD);
			if (orderInfo != null) {
				if (DeliveryStatus.CANCELLED.getDeliveryStatus() != (response.getDeliveryStatus())) {
					orderInfo.setDeliveryDetails(
							DataConverter.convert(orderInfo.getOrder().getUnitId(), updatedResponse));
					orderInfo.setDeliveryPartner(metaCache.getDeliveryPartner(updatedResponse.getDeliveryPartnerId()));
				} else {
					orderInfo.setDeliveryDetails(null);
					orderInfo.setDeliveryPartner(metaCache.getDeliveryPartner(1));
				}
				if (infoCache.getValidOrderStatuses().contains(orderInfo.getOrder().getStatus())) {
					infoCache.addToCache(orderInfo);
					infoCache.addToUndelivered(orderInfo);
					Map<String, String> payload = new HashMap<>();
					String contactChannel = TransactionConstants.ORDER_QUEUE_CHANNEL
							+ TransactionConstants.WEB_SOCKET_CHANNEL_ORDERS;
					LOG.info("Contact Channel :::::: " + contactChannel);
					payload.put("generatedOrderId", updatedResponse.getGeneratedOrderId());
					payload.put("deliveryStatus", updatedResponse.getDeliveryStatus());
					payload.put("partnerId",
							metaCache.getDeliveryPartner(updatedResponse.getDeliveryPartnerId()).getName());
					template.convertAndSend(contactChannel, payload);
				}
			}
			flag = 1;
		} else {
			flag = -1;
		}
		return flag;

	}

	@Override
	public IdCodeName convert(DeliveryResponse delivery) {
		if (delivery != null && delivery.getDeliveryPartnerId() != 0) {
			return metaCache.getDeliveryPartner(delivery.getDeliveryPartnerId());
		} else {
			return metaCache.getDeliveryPartner(1);
		}
	}

	@Override
	public List<DeliveryDetail> getDeliveryDetails(String generatedOrderId) {
		return deliveryDao.getDeliveryDetail(generatedOrderId);
	}

	@Override
	public List<UnitToDeliveryPartnerMappings> getUnitPartnerMappings() {
		return deliveryServiceCache.getUnitPartnerMappings();
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public synchronized DeliveryResponse saveManualDelivery(DeliveryResponse detail)
			throws DataNotFoundException, TemplateRenderingException, SDPAllocationException {
		detail.setStatusUpdateTime(AppUtils.getCurrentTimestamp());

		if (detail.getDeliveryPartnerId() == AppConstants.DELIVERY_PARTNER_CHAAYOS_DELIVERY) {
			DeliveryResponse response = allotNoToRider(detail);
			if (response.getFailureCode() != null || response.getFailureMessage() != null) {
				LOG.info("Failure code " + response.getFailureCode() + "   failure message while assigning rider : "
						+ response.getFailureMessage());
				return response;
			}
		}

		DeliveryResponse savedDelivery = deliveryDao.saveDeliveryDetails(detail.getUnitId(), detail);

		if (savedDelivery != null) {
			// setting unitId to updated delivery response
			savedDelivery.setUnitId(detail.getUnitId());
			OrderInfo order = infoCache.getOrderById(savedDelivery.getOrderId(), UnitCategory.COD);
			IdCodeName deliveryPartner = metaCache.getDeliveryPartner(savedDelivery.getDeliveryPartnerId());
			updateOrderWithDelivery(order, detail, deliveryPartner);
			String webSocketChannel = TransactionConstants.WEB_SOCKET_CHANNEL + savedDelivery.getUnitId()
					+ TransactionConstants.WEB_SOCKET_CHANNEL_ORDERS;
			template.convertAndSend(webSocketChannel, order);
			try{
				//SQS publish for assign rider call
                LOG.info("Publishing ASSIGN RIDER order status update event");
				PartnerOrderStateUpdate partnerOrderStateUpdate = new PartnerOrderStateUpdate();
				partnerOrderStateUpdate.setOrderId(order.getOrder().getOrderId().toString());
				partnerOrderStateUpdate.setState(PartnerOrderStates.ASSIGNED);
				partnerOrderStateUpdate.setData(new Gson().toJson(new IdCodeName(0, detail.getDeliveryBoyName(), detail.getDeliveryBoyPhoneNum())));
				sqsNotificationService.publishToSQS(props.getEnvironmentType().name(), partnerOrderStateUpdate, "_PARTNER_ORDER_STATUS");
			} catch (JMSException ex){
			    LOG.error("Error publishing ASSIGN rider order status update::::::::", ex);
            }
		}

		return savedDelivery;
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public OrderInfo addDeliveryDetails(OrderInfo info, Unit unit) {
		DeliveryResponse deliveryResponse = createDeliveryRequest(info, unit);
		return updateOrderWithDelivery(info, deliveryResponse, convert(deliveryResponse));
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public OrderInfo addDeliveryDetails(OrderInfo info, Unit unit, IdCodeName deliveryPartner) {
		DeliveryResponse delivery = createDeliveryRequest(info, unit, deliveryPartner.getId());
		return updateOrderWithDelivery(info, delivery, deliveryPartner);
	}

	@Override
	public void refreshPriorityCache() {
		deliveryServiceCache.refreshPriorityCache();
	}

	@Override
	public void refreshPartnerAttributes() {
		deliveryServiceCache.refreshPartnerAttributes();
	}

	@Override
	public Boolean checkIfAutomated(Integer deliveryPartnerId) {
		if (deliveryServiceCache.getAutomatedPartnerMap().containsKey(deliveryPartnerId)) {
			return deliveryServiceCache.getAutomatedPartnerMap().get(deliveryPartnerId).equals("Y");
		}
		return false;
	}

	@Override
	public HashMap<String, String> resendMsgToRider(DeliveryResponse response) throws TemplateRenderingException, DataNotFoundException {
		HashMap<String, String> map = new HashMap<>();
		if (infoCache.getSMSCount(response.getGeneratedOrderId()) >= 2) {
			map.put("failureMessage", "You are allowed to resend message only two times.MESSAGE RESEND LIMIT EXCEEDED!");
		}else{
			sendAllotedNoMsgToSDP(response);
			infoCache.updateSMSCount(response.getGeneratedOrderId());
		}
		map.put("count", String.valueOf(infoCache.getSMSCount(response.getGeneratedOrderId()) ));
		return map;
	}

	private DeliveryResponse allotNoToRider(DeliveryResponse response)
			throws SDPAllocationException, TemplateRenderingException, DataNotFoundException {
		String availableNo = infoCache.getAvailableNoForRider(response.getDeliveryBoyPhoneNum());
		if (availableNo == null) {
			String errorMsg = "SDP " + response.getDeliveryBoyName()
					+ " has been alloted maximum no of request.Please try other SDP";
			response.setFailureCode("416");
			response.setFailureMessage(errorMsg);
			return response;
		}
		List<DeliveryDetail> deliveryDetails = deliveryDao.getDeliveryDetail(response.getGeneratedOrderId());
		if (deliveryDetails != null && deliveryDetails.size() > 0) {
			DeliveryDetail deliveryDetail = deliveryDetails.get(0);
			infoCache.removeAllotedNoToRider(deliveryDetail.getDeliveryBoyPhoneNum(), deliveryDetail.getOrderId());
			if (deliveryDetail.getAllotedNo() != null) {
				infoCache.updateMissedCallNoAllotmentCount(deliveryDetail.getAllotedNo(), "REMOVED");
				sendReAllotmentMsgToSDP(deliveryDetail);
			}
		}

		infoCache.updateMissedCallNoAllotmentCount(availableNo, "ADDED");
		infoCache.allotNoToRider(response.getOrderId(), response.getDeliveryBoyPhoneNum(), availableNo);
		response.setAllotedNo(availableNo);
		infoCache.resetSMSCount(response.getGeneratedOrderId());
		sendAllotedNoMsgToSDP(response);
		return response;
	}

	private boolean sendAllotedNoMsgToSDP(DeliveryResponse response)
			throws TemplateRenderingException, DataNotFoundException {
		LOG.info("Sending SDP Message for order :" + response.getOrderId());
		OrderInfo orderInfo = infoCache.getOrderById(response.getOrderId(), UnitCategory.COD, false);
		LOG.info("order info for order id " + orderInfo);
		if (orderInfo == null) {
			LOG.info("order info null so calling cache miss for order id " + response.getOrderId());
			orderInfo = infoCache.getOrderById(response.getOrderId(), UnitCategory.COD, true);
		}
		LOG.info("Starting to send SDP Message for order :" + response.getOrderId());
		try {
			PaymentMode paymentType = masterDataCache
					.getPaymentMode(orderInfo.getOrder().getSettlements().get(0).getMode());
			String paymentMode = paymentType.getCategory().name().equals(PaymentCategory.ONLINE.name()) ? "ONLINE"
					: paymentType.getName();

			String message = String.format("Hi %s \n # %s \n Cust -  %s \n Amt - %s\n Call on %s after order delivery.\nTeam Chaayos!",
					response.getDeliveryBoyName(), orderInfo.getOrder().getGenerateOrderId(),
					orderInfo.getCustomer().getFirstName() + "(" + orderInfo.getCustomer().getContactNumber() + ")",
					orderInfo.getOrder().getTransactionDetail().getPaidAmount().toString() + "(" + paymentMode + ")",
					response.getAllotedNo());

			LOG.info("Msg of SDP Allocation : " + message);
			/*boolean status = notificationService.sendNotification(
					CustomerSMSNotificationType.DELIVERY_ALLOTED_NO_SDP.name(), message,
					response.getDeliveryBoyPhoneNum(),
					providerService.getSMSClient(
							CustomerSMSNotificationType.DELIVERY_ALLOTED_NO_SDP.getTemplate().getSMSType(),
							ApplicationName.KETTLE_SERVICE),
					props.getAllotedNoToSDPSMS(),null);

			LOG.info("Status of msg : " + status);*/
			return true;
		} catch (Exception e) {
			LOG.error("Error while sending alloted no message to  sdp "
					+ orderInfo.getDeliveryDetails().getDeliveryBoyPhoneNum(), e);
		}
		return false;
	}

	private boolean sendReAllotmentMsgToSDP(DeliveryDetail deliveryDetail) {
		String contactNumber = deliveryDetail.getDeliveryBoyPhoneNum();
		try {
			String message = CustomerSMSNotificationType.DELIVERY_REALLOTMENT_MSG_SDP.getMessage(deliveryDetail);
			boolean status = notificationService.sendNotification(
					CustomerSMSNotificationType.DELIVERY_REALLOTMENT_MSG_SDP.name(), message, contactNumber,
					providerService.getSMSClient(
							CustomerSMSNotificationType.DELIVERY_REALLOTMENT_MSG_SDP.getTemplate().getSMSType(),
							ApplicationName.KETTLE_SERVICE),
					props.getReallotmentSDPSMS(),null);
			return status;
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending alloted no message to  sdp " + contactNumber, e);
		}
		return false;
	}

	@Override
	public HashMap<Integer, IdCodeName> defaultDeliveryPartner(String mappingType) {
		List<PartnerAttributes> partners = deliveryDao.defaultDeliveryPartner(mappingType);
		HashMap<Integer, IdCodeName> map = new HashMap<>();
		for (PartnerAttributes attributes : partners) {
			IdCodeName codeName = new IdCodeName();
			codeName.setId(attributes.getPartnerId());
			codeName.setName(attributes.getMappingType());
			codeName.setCode(attributes.getMappingValue());
			codeName.setType(attributes.getPartnerType());
			map.put(codeName.getId(), codeName);
		}
		return map;
	}
}
