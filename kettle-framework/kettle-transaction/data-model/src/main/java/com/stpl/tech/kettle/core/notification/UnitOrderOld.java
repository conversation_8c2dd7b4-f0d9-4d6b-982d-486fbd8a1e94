package com.stpl.tech.kettle.core.notification;

import java.util.ArrayList;
import java.util.List;

public class UnitOrderOld {

	private int unitId;
	private List<Integer> orders;

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public List<Integer> getOrders() {
		if (orders == null) {
			orders = new ArrayList<>();
		}
		return orders;
	}

	public void setOrders(List<Integer> orders) {
		this.orders = orders;
	}

}
