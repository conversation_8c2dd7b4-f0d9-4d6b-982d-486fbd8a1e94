/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.domain.model.PercentageDetail;

public class MembershipProductDiscount {
	private Integer productId;
	private PercentageDetail applicableDiscount;
	private String couponCode;
	private String productName;
	private String desc;
	private String dimension;
	private BigDecimal price;

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public PercentageDetail getApplicableDiscount() {
		return applicableDiscount;
	}

	public void setApplicableDiscount(PercentageDetail applicableDiscount) {
		this.applicableDiscount = applicableDiscount;
	}

	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Override
	public String toString() {
		return "MembershipProductDiscount [productId=" + productId + ", applicableDiscount=" + applicableDiscount
				+ ", couponCode=" + couponCode + ", productName=" + productName + ", desc=" + desc + ", dimension="
				+ dimension + ", price=" + price + "]";
	}

}