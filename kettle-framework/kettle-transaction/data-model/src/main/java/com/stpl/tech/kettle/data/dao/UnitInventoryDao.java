/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.stpl.tech.kettle.core.InventoryThresholdType;
import com.stpl.tech.kettle.data.model.UnitProductInventory;
import com.stpl.tech.kettle.domain.model.InventoryUpdateEvent;
import com.stpl.tech.kettle.domain.model.ProductInventory;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.PartnerDetail;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;

public interface UnitInventoryDao {

	public List<ProductInventory> getUnitInventory(int unitId) throws DataNotFoundException;

	public Map<Integer, Integer> getUnitInventoryForWeb(int unitId) throws DataNotFoundException;;

	public List<ProductInventory> getUnitInventoryForProducts(int unitId, List<Integer> productIds)
			throws DataNotFoundException;

	public Map<Integer, Integer> getUnitInventoryForProductsForWeb(int unitId, List<Integer> productIds)
			throws DataNotFoundException;

	public Map<String, List<ProductInventory>> getUnitInventoryByCategory(UnitCategory category)
			throws DataNotFoundException;

	public Map<Integer, UnitProductInventory> getUnitProductInventoryForProducts(int unitId, List<Integer> productIds);

	public boolean stockOutInventory(int unitId, int productId) throws DataUpdationException;

	public boolean updateUnitInventory(InventoryUpdateEvent updateData, boolean incrementalUpdate, int employeeId,
			Integer orderId, boolean isCancellation);

	public boolean addInventoryEvent(InventoryUpdateEvent updateData);

	public Map<String, Set<ProductInventory>> getStockedOutUnitInventoryByCategory(UnitCategory category,
			InventoryThresholdType thresholdType) throws DataNotFoundException;

	public Map<String, List<ProductInventory>> getLastHourStockedOutUnitInventory(
			Map<String, Set<ProductInventory>> stockedOutInventoryByCategory, Date lastNotificaionTime);

	public Collection<IdCodeName> getUnitToDeliveryMappings(int unitId);

	public void cloneProductInventoryMappings(int unitId, int cloneUnitId) throws DataUpdationException;

	// TODO Call this from the service in a separate transaction.
	public void addUpdateUnitDeliveryMappings(Unit unit) throws DataUpdationException;

	// TODO Call this from the service in a separate transaction.
	public void updateUnitToDeliveryPartnerMappings(int unitId, List<PartnerDetail> profiles);

	/**
	 * @param unitId
	 * @param productIds
	 * @return
	 * @throws DataNotFoundException 
	 */
	public List<ProductInventory> getUnitInventory(int unitId, List<Integer> productIds) throws DataNotFoundException;

}
