package com.stpl.tech.kettle.data.model;

import com.stpl.tech.kettle.domain.model.OrderItem;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@SuppressWarnings("serial")
@Entity
@Table(name = "ORDER_COMPLAINT")
public class OrderComplaint implements java.io.Serializable{

        private String externalOrderId;

        private Integer orderId;

        private String referenceId;

        private String complaintMessage;

        private String complaintReason;

        private Date createdAt;

        private Date expiredAt;

        private Integer customerComplaintsCount;

        private Integer repeatCustomerCount;

        private Float minCustomRefund;

        private List<ImageUrls> imageUrlsList=new ArrayList<>();

        private List<RefundOptions> refundOptionsList=new ArrayList<>();

        private List<OrderItemComplaint> orderItemComplaintList= new ArrayList<>();

        public OrderComplaint() { }

        public OrderComplaint(String externalOrderId, Integer orderId, String referenceId, String complaintMessage, String complaintReason, Date createdAt, Date expiredAt, Integer customerComplaintsCount, Integer repeatCustomerCount, Float minCustomRefund, RefundOptions refundOptions, List<ImageUrls> imageUrlsList, List<RefundOptions> refundOptionsList, List<OrderItemComplaint> orderItemComplaintList) {
            this.externalOrderId = externalOrderId;
            this.orderId = orderId;
            this.referenceId = referenceId;
            this.complaintMessage = complaintMessage;
            this.complaintReason = complaintReason;
            this.createdAt = createdAt;
            this.expiredAt = expiredAt;
            this.customerComplaintsCount = customerComplaintsCount;
            this.repeatCustomerCount = repeatCustomerCount;
            this.minCustomRefund = minCustomRefund;
            this.imageUrlsList = imageUrlsList;
            this.refundOptionsList = refundOptionsList;
            this.orderItemComplaintList=orderItemComplaintList;
        }


        @Id
        @Column(name="ORDER_ID", nullable = false)
        public Integer getOrderId() {
            return orderId;
        }

        public void setOrderId(Integer orderId) {
            this.orderId = orderId;
        }


        @Column(name = "EXTERNAL_ORDER_ID", unique = true, nullable = false)
        public String getExternalOrderId() {
            return externalOrderId;
        }

        public void setExternalOrderId(String externalOrderId) {
            this.externalOrderId = externalOrderId;
        }

        @Column(name="REFERENCE_ID", nullable = false)
        public String getReferenceId() {
            return referenceId;
        }

        public void setReferenceId(String referenceId) {
            this.referenceId = referenceId;
        }

        @Column(name="COMPLAINT_MESSAGE", nullable = false)
        public String getComplaintMessage() {
            return complaintMessage;
        }

        public void setComplaintMessage(String complaintMessage) {
            this.complaintMessage = complaintMessage;
        }

        @Column(name="COMPLAINT_REASON", nullable = false)
        public String getComplaintReason() {
            return complaintReason;
        }

        public void setComplaintReason(String complaintReason) {
            this.complaintReason = complaintReason;
        }

        @Column(name="CREATED_AT", nullable = false)
        public Date getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(Date createdAt) {
            this.createdAt = createdAt;
        }

        @Column(name="EXPIRED_AT", nullable = false)
        public Date getExpiredAt() {
            return expiredAt;
        }

        public void setExpiredAt(Date expiredAt) {
            this.expiredAt = expiredAt;
        }

        @Column(name="CUSTOMER_COMPLAINT_COUNT", nullable = false)
        public Integer getCustomerComplaintsCount() {
            return customerComplaintsCount;
        }

        public void setCustomerComplaintsCount(Integer customerComplaintsCount) {
            this.customerComplaintsCount = customerComplaintsCount;
        }

        @Column(name="REPEAT_CUSTOMER_COUNT", nullable = false)
        public Integer getRepeatCustomerCount() {
            return repeatCustomerCount;
        }

        public void setRepeatCustomerCount(Integer repeatCustomerCount) {
            this.repeatCustomerCount = repeatCustomerCount;
        }

        @Column(name="MIN_CUSTOMER_REFUND", nullable = false)
        public Float getMinCustomRefund() {
            return minCustomRefund;
        }

        public void setMinCustomRefund(Float minCustomRefund) {
            this.minCustomRefund = minCustomRefund;
        }

        @OneToMany(fetch = FetchType.LAZY, mappedBy = "orderComplaint")
        public List<RefundOptions> getRefundOptionsList() {
            return refundOptionsList;
        }

        public void setRefundOptionsList(List<RefundOptions> refundOptionsList) {
            this.refundOptionsList = refundOptionsList;
        }

        @OneToMany(fetch = FetchType.LAZY, mappedBy = "orderComplaint")
        public List<ImageUrls> getImageUrlsList() {
            return imageUrlsList;
        }

        public void setImageUrlsList(List<ImageUrls> imageUrlsList) {
            this.imageUrlsList = imageUrlsList;
        }

        @OneToMany(fetch = FetchType.LAZY, mappedBy = "orderComplaint")
        public List<OrderItemComplaint> getOrderItemComplaintList() {
            return orderItemComplaintList;
        }

        public void setOrderItemComplaintList(List<OrderItemComplaint> orderItemComplaintList) {
            this.orderItemComplaintList = orderItemComplaintList;
        }

    @Override
    public String toString() {
        return "OrderComplaint{" +
                "imageUrlsList=" + imageUrlsList +
                '}';
    }
}


