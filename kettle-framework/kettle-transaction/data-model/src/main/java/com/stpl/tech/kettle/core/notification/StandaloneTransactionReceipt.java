package com.stpl.tech.kettle.core.notification;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.kettle.data.model.StandaloneTransactionDetail;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AbstractTemplate;

public class StandaloneTransactionReceipt extends AbstractTemplate {

	public static final String COVID19_FAILURE = "COVID19-FAILURE";

	public static final String COVID19_SUCCESS = "COVID19-SUCCESS";

	private StandaloneTransactionDetail detail;

	private String fromEmail;

	private String campaign;

	private String basePath;

	private EnvType env;

	Map<String, Object> data = new HashMap<String, Object>();

	public StandaloneTransactionReceipt(StandaloneTransactionDetail detail, EnvType env, String basePath,
			String campaign, String fromEmail) {
		super();
		this.fromEmail = fromEmail;
		this.detail = detail;
		this.campaign = campaign;
		this.basePath = basePath;
		this.env = env;
	}

	public StandaloneTransactionDetail getDetail() {
		return detail;
	}

	public void setDetail(StandaloneTransactionDetail detail) {
		this.detail = detail;
	}

	@Override
	public String getTemplatePath() {
		switch (campaign) {
		case COVID19_SUCCESS:
			return "template/CovidSuccessEmail.html";
		case COVID19_FAILURE:
			return "template/CovidFailureEmail.html";
		}
		return null;
	}

	public Map<String, Object> getData() {
		data.put("customerName",
				detail.getPaymentCustomerName() == null ? "Chai Lover" : detail.getPaymentCustomerName());
		data.put("currency", detail.getPaymentCurrency());
		data.put("amount", detail.getPaymentAmount().intValue() / 100);
		data.put("noOfMeals",
				detail.getPaymentCurrency().equalsIgnoreCase("inr") ? (detail.getPaymentAmount().intValue() / 2500)
						: "");
		return data;
	}

	@Override
	public String getFilepath() {
		return basePath + "/" + "/standalonetxn/" + "/StandaloneTranactionReceipt-" + detail.getPaymentId() + ".html";
	}

	public String getBasePath() {
		return basePath;
	}

	public String getFromEmail() {
		return fromEmail;
	}

	public String getToEmail() {
		switch (campaign) {
		case COVID19_SUCCESS:
			return "<EMAIL>" + detail.getPaymentEmailId() != null
					&& detail.getPaymentEmailId().trim().length() > 0 ? "," + detail.getPaymentEmailId() : "";
		case COVID19_FAILURE:
			return "<EMAIL>";
		}
		return "<EMAIL>";
	}

	public EnvType getEnv() {
		return env;
	}

	public String getCampaign() {
		return campaign;
	}

}
