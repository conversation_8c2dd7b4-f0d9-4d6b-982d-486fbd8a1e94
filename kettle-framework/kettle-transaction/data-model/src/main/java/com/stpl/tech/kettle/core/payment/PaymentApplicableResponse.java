package com.stpl.tech.kettle.core.payment;

import com.stpl.tech.kettle.data.model.OrderPaymentDetail;

import java.util.List;

public class PaymentApplicableResponse {

    private List<OrderPaymentDetail> applicable;

    private List<OrderPaymentDetail> toBeApplicable;

    public PaymentApplicableResponse(List<OrderPaymentDetail> applicable, List<OrderPaymentDetail> toBeApplicable) {
        this.applicable = applicable;
        this.toBeApplicable = toBeApplicable;
    }

    public List<OrderPaymentDetail> getApplicable() {
        return applicable;
    }

    public List<OrderPaymentDetail> getToBeApplicable() {
        return toBeApplicable;
    }
}
