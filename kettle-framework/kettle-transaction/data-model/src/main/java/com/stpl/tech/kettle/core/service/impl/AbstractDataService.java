/**
 * 
 */
package com.stpl.tech.kettle.core.service.impl;

import java.io.IOException;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.api.services.drive.model.File;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.file.load.ExcelParser;
import com.stpl.tech.kettle.core.file.load.RowMapper;
import com.stpl.tech.kettle.core.file.management.GoogleSheetLoader;
import com.stpl.tech.master.core.exception.FileDownloadException;
import com.stpl.tech.master.core.exception.FileParsingException;
import com.stpl.tech.util.AppConstants;

/**
 * <AUTHOR>
 *
 */
public abstract class AbstractDataService {

	private static final Logger LOG = LoggerFactory.getLogger(AbstractDataService.class);

	protected <T> List<T> downloadExcelAndParse(String parentDirName, String inputFileName, String storedFileName,
			RowMapper<T, Cell> mapper) throws IOException, FileDownloadException, FileParsingException {
		GoogleSheetLoader loader = new GoogleSheetLoader();
		try {
			LOG.info("Downloading file {} ", inputFileName);
			File file = loader.getFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, inputFileName,
					TransactionConstants.MIMETYPE_GOOGLE_SHEETS, parentDirName);
			if (file == null) {
				throw new FileDownloadException("Could not locate the file with name " + inputFileName);
			}
			loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
					AppConstants.EXCEL_MIME_TYPE, storedFileName);
		} catch (IOException e) {
			LOG.error("Failed to download the file", e);
			throw e;
		}
		ExcelParser<T> parser = new ExcelParser<>(mapper);
		List<T> expenses = null;
		try {
			expenses = parser.parseExcel(storedFileName, 0, 1, 0, 1);
			if (parser.hasErrors()) {
				LOG.error("Errors in excel sheet");
				throw new FileParsingException("Parsing Errors", parser.getErrors());
			}
		} catch (IOException e) {
			LOG.error("Failed to load the stored excel file ", e);
			throw e;
		}
		return expenses;
	}
}
