package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.CustomerOfferDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerOfferDetailDao extends JpaRepository<CustomerOfferDetail,Integer> {

    public CustomerOfferDetail findByOfferCodeAndCustomerIdAndOrderId(String offerCode , Integer customerId, Integer orderId);
    public CustomerOfferDetail findByOfferCodeAndCustomerId(String offerCode , Integer customerId);
}
