/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.monitoring;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Delayed;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DelayQueueConsumer<T extends Delayed> {

	private static final Logger LOG = LoggerFactory.getLogger(DelayQueueConsumer.class);
	private String name;

	private BlockingQueue<T> queue;

	public DelayQueueConsumer(String name, BlockingQueue<T> queue) {
		super();
		this.name = name;
		this.queue = queue;
	}

	private Thread consumerThread = new Thread(new Runnable() {
		public void run() {
			while (true) {
				try {
					// Take elements out from the DelayQueue object.
					T object = queue.take();
					LOG.info(String.format("[%s] - Take object = %s%n", Thread.currentThread().getName(), object));
					if (object instanceof UnitMonitorData) {
						LOG.info("Found data ");
					}
					Thread.sleep(1000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		}
	});

	public void start() {
		this.consumerThread.setName(name);
		this.consumerThread.start();
	}

}
