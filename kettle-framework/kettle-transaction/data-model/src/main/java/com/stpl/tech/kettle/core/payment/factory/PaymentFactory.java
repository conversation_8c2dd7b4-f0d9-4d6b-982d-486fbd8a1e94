package com.stpl.tech.kettle.core.payment.factory;

import com.stpl.tech.kettle.core.payment.PaymentAdapter;
import com.stpl.tech.kettle.core.payment.PaymentPartnerType;
import com.stpl.tech.kettle.core.payment.adapter.AGSPaymentAdapter;
import com.stpl.tech.kettle.core.payment.adapter.EzetapPaymentAdapter;
import com.stpl.tech.kettle.core.payment.adapter.GPayQRPaymentAdapter;
import com.stpl.tech.kettle.core.payment.adapter.IngenicoPaymentAdapter;
import com.stpl.tech.kettle.core.payment.adapter.PaytmAdapter;
import com.stpl.tech.kettle.core.payment.adapter.PaytmNewAdapter;
import com.stpl.tech.kettle.core.payment.adapter.PaytmQRPaymentAdapter;
import com.stpl.tech.kettle.core.payment.adapter.PaytmUPIAdapter;
import com.stpl.tech.kettle.core.payment.adapter.RazorPayAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PaymentFactory {

    @Autowired
    RazorPayAdapter razorPayAdapter;
    @Autowired
    PaytmNewAdapter paytmNewAdapter;
    @Autowired
    PaytmAdapter paytmAdapter;
    @Autowired
    EzetapPaymentAdapter ezetapPaymentAdapter;
    @Autowired
    PaytmQRPaymentAdapter paytmQRPaymentAdapter;
    @Autowired
    IngenicoPaymentAdapter ingenicoPaymentAdapter;
    @Autowired
    AGSPaymentAdapter agsPaymentAdapter;
    @Autowired
    PaytmUPIAdapter paytmUPIAdapter;
    @Autowired
    GPayQRPaymentAdapter gPayQRPaymentAdapter;

    public PaymentAdapter getCreatePaymentAdapter(PaymentPartnerType paymentPartnerType){
        switch (paymentPartnerType){
            case PAYTM2 : {
                return paytmNewAdapter;
            }
            case RAZORPAY : {
                return razorPayAdapter;
            }
            case PAYTM1 : {
                return paytmAdapter;
            }
            case EZETAP: {
                return ezetapPaymentAdapter;
            }
            case PAYTM1QR: {
                return paytmQRPaymentAdapter;
            }
            case INGENICO : {
                return ingenicoPaymentAdapter;
            }
            case AGS: {
                return agsPaymentAdapter;
            }
            case PAYTM_UPI:{
                return paytmUPIAdapter;
            }
            case GPAY_QR:{
                return gPayQRPaymentAdapter;
            }
            default:{
                throw new IllegalArgumentException("Payment partner " + paymentPartnerType.name() + " not found");
            }
        }
    }
}
