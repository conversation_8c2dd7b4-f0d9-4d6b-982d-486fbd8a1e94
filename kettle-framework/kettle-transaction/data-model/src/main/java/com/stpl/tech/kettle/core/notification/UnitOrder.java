package com.stpl.tech.kettle.core.notification;

import java.util.ArrayList;
import java.util.List;

public class UnitOrder {

	private int unitId;
	private List<StatusData> orders;

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public List<StatusData> getOrders() {
		if (orders == null) {
			orders = new ArrayList<>();
		}
		return orders;
	}

	public void setOrders(List<StatusData> orders) {
		this.orders = orders;
	}

}
