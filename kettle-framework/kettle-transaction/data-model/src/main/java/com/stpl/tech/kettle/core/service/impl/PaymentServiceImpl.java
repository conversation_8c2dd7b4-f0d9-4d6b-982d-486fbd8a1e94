package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.StandaloneTransactionNotification;
import com.stpl.tech.kettle.core.notification.StandaloneTransactionReceipt;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.core.service.EzetapPaymentService;
import com.stpl.tech.kettle.core.service.GooglePaymentService;
import com.stpl.tech.kettle.core.service.IngenicoPaymentService;
import com.stpl.tech.kettle.core.service.PayTMNewPaymentService;
import com.stpl.tech.kettle.core.service.PayTMPaymentService;
import com.stpl.tech.kettle.core.service.PaymentService;
import com.stpl.tech.kettle.core.service.PaymentServiceNew;
import com.stpl.tech.kettle.core.service.RazorPayPaymentService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.data.model.OrderPaymentEvent;
import com.stpl.tech.kettle.data.model.StandaloneTransactionDetail;
import com.stpl.tech.kettle.delivery.model.PaymentPartner;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.payment.config.PaytmConfig;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.payment.model.AGS.AGSCreateRequest;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMStatus;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SStatus;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentResponse;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.PaymentStatusChangeRequest;
import com.stpl.tech.master.payment.model.PaymentVO;
import com.stpl.tech.master.payment.model.ezetap.EzetapCreateRequest;
import com.stpl.tech.master.payment.model.ezetap.EzetapPaymentResponse;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentRequest;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentStatus;
import com.stpl.tech.master.payment.model.gpay.GPayQRResponse;
import com.stpl.tech.master.payment.model.ingenico.IngenicoQrResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmFetchPaymentOptionsRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauth;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamBody;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamHead;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParams;
import com.stpl.tech.master.payment.model.patymNew.PaytmPaymentStatusType;
import com.stpl.tech.master.payment.model.patymNew.PaytmStatusResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmTransactionAmount;
import com.stpl.tech.master.payment.model.patymNew.PaytmUserInfo;
import com.stpl.tech.master.payment.model.patymNew.PaytmValidateSSOTokenResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequestResponseWrapper;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmPaymentStatus;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiQrRequest;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiQrResponse;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiS2SResponse;
import com.stpl.tech.master.payment.model.razorpay.BasicTransactionInfo;
import com.stpl.tech.master.payment.model.razorpay.RazorPayCreateRequest;
import com.stpl.tech.master.payment.model.razorpay.RazorPayEventData;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentResponse;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.notification.pubnub.PubnubService;
import com.stpl.tech.util.notification.pubnub.PushNotification;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.jms.JMSException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Deprecated
@Service
public class PaymentServiceImpl implements PaymentService {

    private static final Logger LOG = LoggerFactory.getLogger(PaymentServiceImpl.class);

    private static final String AGS_IS_SAVED_FAILED = "01";

    private static final String AGS_IS_SAVED_SUCCESS = "00";

    @Autowired
    private PaymentGatewayDao paymentGatewayDao;
    @Autowired
    private PayTMPaymentService payTMService;
    @Autowired
    private GooglePaymentService gPayService;
    @Autowired
    private RazorPayPaymentService razorPayService;
    @Autowired
    private EzetapPaymentService ezetapPaymentService;
    @Autowired
    private IngenicoPaymentService ingenicoService;
    @Autowired
    private MasterDataCache masterCache;
    @Autowired
    private EnvironmentProperties props;
    @Autowired
    private PubnubService pubnubService;
    @Autowired
    private com.stpl.tech.kettle.core.service.AGSPaymentService AGSPaymentService;
    @Autowired
	private NotificationService notificationService;
	@Autowired
    private SMSClientProviderService providerService;

	@Autowired
	private PayTMNewPaymentService payTMNewPaymentService;

    @Autowired
    private PaytmConfig paytmConfig;


    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.impl.PaymentService#createRequest(com.
     * stpl.tech.kettle.domain.model.Order)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public RazorPayCreateRequest createRazorPayRequest(OrderPaymentRequest order)
            throws PaymentFailureException, DataNotFoundException {
        RazorPayCreateRequest request = razorPayService.createRequest(props.getEnvironmentType(), order);
        paymentGatewayDao.createRequest(request, order);
        return request;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.impl.PaymentService#createRequest(com.
     * stpl.tech.kettle.domain.model.Order)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public EzetapCreateRequest createEzetapRequest(OrderPaymentRequest order)
            throws PaymentFailureException {
        EzetapCreateRequest request = ezetapPaymentService.createRequest(props.getEnvironmentType(), order);
        paymentGatewayDao.createRequest(request, order);
        return request;
    }


    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.impl.PaymentService#createRequest(com.
     * stpl.tech.kettle.domain.model.Order)
     */
//	@Override
//	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
//	public IngenicoQrRequest createIngenicoRequest(OrderPaymentRequest order)
//			throws PaymentFailureException {
//		IngenicoQrRequest request = ingenicoService.createRequest(props.getEnvironmentType(), order);
//		paymentGatewayDao.createRequest(request, order);
//		return request;
//	}


    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.impl.PaymentService#createRequest(com.
     * stpl.tech.kettle.domain.model.Order)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaytmCreateRequest createPaytmRequest(OrderPaymentRequest order)
            throws PaymentFailureException, DataNotFoundException {
        PaytmCreateRequest request = payTMService.createRequest(order);
        LOG.info("::::::::::::::::: Paytm Request generated ::::::::::::::::");
        LOG.info(JSONSerializer.toJSON(request));
        paymentGatewayDao.createRequest(request, order);
        return request;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.impl.PaymentService#createRequest(com.
     * stpl.tech.kettle.domain.model.Order)
     */
    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaymentRequest createRequest(OrderPaymentRequest order)
            throws PaymentFailureException, DataNotFoundException {
        PaymentRequest request = null;
        if (order.getPaymentModeId() == PaymentPartner.PAYTM.getSystemId(props.getEnvironmentType())) {
            request = payTMService.createRequest(order);
        } else if (order.getPaymentModeId() == PaymentPartner.RAZORPAY.getSystemId(props.getEnvironmentType())) {
            request = razorPayService.createRequest(props.getEnvironmentType(), order);
        }
// else if(order.getPaymentModeId() == PaymentPartner.INGENICO.getSystemId(props.getEnvironmentType())){
//			request = ingenicoService.createRequest(props.getEnvironmentType(), order);
//		}

        if (request == null) {
            throw new PaymentFailureException("Online Payment for "
                    + masterCache.getPaymentMode(order.getPaymentModeId()).getName() + " is not supported");
        } else {
            paymentGatewayDao.createRequest(request, order);
        }
        return request;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.impl.PaymentService#createRequest(com.
     * stpl.tech.kettle.domain.model.Order)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map updateRazorPayResponse(RazorPayPaymentResponse response,Integer brandId)
            throws PaymentFailureException {
        boolean validation = razorPayService.validateResponse(props.getEnvironmentType(), response, brandId);
        response.setStatus(validation ? "successful" : "failed");
        return updateAndRedirect(response, validation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map updateRazorPayResponseWithoutVerification(RazorPayPaymentResponse response)
            throws PaymentFailureException {
        LOG.info("Updating payment without s v");
        if(response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.SUCCESSFUL.value()) ||
                response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.CAPTURED.value())
                || response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.AUTHORIZED.value())){
            response.setStatus("successful");
            return updateAndRedirect(response, true);
        } else {
            response.setStatus("failed");
            return updateAndRedirect(response, false);
        }

    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map updateRazorPayResponseForHangingPayment(RazorPayPaymentResponse response)
            throws PaymentFailureException {
        //boolean validation = razorPayService.validateResponse(props.getEnvironmentType(), response);
        boolean validation = false;
        if(response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.SUCCESSFUL.value()) ||
                response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.CAPTURED.value())
                || response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.AUTHORIZED.value())){
            response.setStatus("successful");
            validation = true;
        } else {
            response.setStatus("failed");
            validation = false;
        }
        return update(response, validation);
    }
    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.impl.PaymentService#createRequest(com.
     * stpl.tech.kettle.domain.model.Order)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map updateEzetapResponse(EzetapPaymentResponse response)
            throws PaymentFailureException {
        boolean validation = true; //because payment has been done successfully
        //ezetapPaymentService.validateResponse(props.getEnvironmentType(), response);
        response.setStatus(validation ? "successful" : "failed");
        return updateAndRedirect(response, validation);
    }

    private Map updateAndRedirect(PaymentResponse response, boolean validation) {
        PaymentStatus status = validation ? PaymentStatus.SUCCESSFUL : PaymentStatus.FAILED;
        OrderPaymentDetail paymentDetail = paymentGatewayDao.updateResponse(status, response);
        if(paymentDetail == null){
            validation = false;
        }
        Map returnResult = new HashMap<String,String>();
        if (validation) {
            returnResult.put("orderId", response.getOrderId());
            returnResult.put("txnId", response.getTransactionId());
        } else {
            returnResult.put("error", response.getOrderId());
            returnResult.put("reason", response.getReason());
        }
        return returnResult;
    }

    private Map update(PaymentResponse response, boolean validation) {
        PaymentStatus status = validation ? PaymentStatus.SUCCESSFUL : PaymentStatus.FAILED;
        paymentGatewayDao.updateResponseUsingPartnerOrderId(status, response);
        Map returnResult = new HashMap<String,String>();
        if (validation) {
            returnResult.put("orderId", response.getOrderId());
            returnResult.put("txnId", response.getTransactionId());
        } else {
            returnResult.put("error", response.getOrderId());
            returnResult.put("reason", response.getReason());
        }
        return returnResult;
    }

    private Map updateAndRedirectToIngenico(IngenicoQrResponse ingenicoResponse, PaymentResponse response, boolean validation) {
        PaymentStatus status = ingenicoResponse.getStatus().equals("SUCCESS") ? PaymentStatus.SUCCESSFUL : PaymentStatus.FAILED;
        paymentGatewayDao.updateResponse(status, response);
        Map returnResult = new HashMap<String,String>();
        if (validation) {
            returnResult.put("orderId", response.getOrderId());
            returnResult.put("txnId", response.getTransactionId());
            PushNotification<Map<String,PaymentResponse>> notification = new IngenicoResponseNotification(props.getEnvironmentType(), ingenicoResponse, response);
            pubnubService.sendNotification(notification);

        } else {
            returnResult.put("error", response.getOrderId());
            returnResult.put("reason", response.getReason());
        }
        return returnResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map updatePaytmResponse(PaytmCreateResponse response, boolean verifyPaytmStatus) throws PaymentFailureException {
        boolean validation = payTMNewPaymentService.validateResponse(response, verifyPaytmStatus);
        return updateAndRedirect(response, validation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map updatePaytmResponse(PaytmCreateResponse response) throws PaymentFailureException {
        boolean validation = payTMService.validateResponse(response);
        return updateAndRedirect(response, validation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map updatePaytmResponseWithoutVerification(PaytmCreateResponse response ) throws PaymentFailureException {
        LOG.info("Updating payment without s v");
        if(response.getStatus().equals(PaytmPaymentStatusType.TXN_SUCCESS.name())){
            return updateAndRedirect(response, true);
        } else {
            return updateAndRedirect(response, false);
        }

    }

    @Override
    public PaytmStatusResponse getPaytmStatusResponse(String orderId) throws Exception {
        return payTMService.getPaytmStatusResponse(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void createPaymentEvent(RazorPayEventData event) {
        paymentGatewayDao.createPaymentEvent(event);
    }


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public StandaloneTransactionDetail createStandalonePaymentEvent(RazorPayEventData event) {
		if (event.getInfo() == null) {
			event.setInfo(new BasicTransactionInfo());
		}
		event.getInfo().setCurrentStatus(getStatus(event.getEvent()));
		return paymentGatewayDao.createStandalonePaymentEvent(event);
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean setNotificationDetail(String paymentId, String smsType, String emailType) {
		paymentGatewayDao.setNotificationDetail(paymentId, smsType, emailType);
		return true;
	}


	private String getStatus(String event) {
		switch (event) {
		case "payment.authorized":
			return "AUTHORIZED";
		case "payment.captured":
			return "CAPTURED";
		case "payment.failed":
			return "FAILED";
		case "order.paid":
			return "SUCCESSFUL";
		default:
			return "UNKNOWN";
		}
	}

	@Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public RazorPayPaymentResponse fetchRazorPayPayment(String paymentId) throws PaymentFailureException {
        RazorPayPaymentResponse payment = null;
        if(paymentId!=null){
            payment = razorPayService.fetchPayment(props.getEnvironmentType(), paymentId);
            OrderPaymentDetail paymentDetail = paymentGatewayDao
                    .getSuccessfulPaymentDetailFromOrderId(payment.getRazorPayOrderId());
            if (paymentDetail == null) {
                throw new PaymentFailureException("No Payment Entry found in our system for id " + paymentId);
            } else {
                payment.setOrderId(paymentDetail.getExternalOrderId());
                return payment;
            }
        }
        return payment;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public RazorPayPaymentResponse fetchResponseByRazorPayByPartnerOrderId(String partnerOrderId,Integer brandId) throws PaymentFailureException {
        RazorPayPaymentResponse response = razorPayService.fetchOrder(props.getEnvironmentType(),
                partnerOrderId,brandId);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean cancelPayment(PaymentStatusChangeRequest cancel) {
        return paymentGatewayDao.cancelPayment(cancel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean failurePayment(PaymentStatusChangeRequest failure) {
        return paymentGatewayDao.paymentFailure(failure);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean checkPaymentStatus(PaymentVO payment) {
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getActivePaymentDetail(payment.getOrderId());
        if (paymentDetail != null) {
            int paymentMode = paymentDetail.getPaymentModeId();
            if (PaymentPartner.RAZORPAY.getSystemId(props.getEnvironmentType()) == paymentMode) {
                return checkRazorpayPayment(paymentDetail);
            } else if (PaymentPartner.PAYTM.getSystemId(props.getEnvironmentType()) == paymentMode) {
                return checkPaytmPaymentStatus(paymentDetail);
            }else if(PaymentPartner.INGENICO.getSystemId(props.getEnvironmentType()) == paymentMode){
                return checkIngenicoPaymentStatus(paymentDetail);
            }
        }
        return false;
    }

    private boolean checkIngenicoPaymentStatus(OrderPaymentDetail paymentDetail) {
        try{
            PaymentStatus status = ingenicoService.getPaymentStatus(props.getEnvironmentType(), paymentDetail);
            return status.equals(PaymentStatus.SUCCESSFUL);
        }catch (PaymentFailureException pe){
            return false;
        }
    }

    private boolean checkPaytmPaymentStatus(OrderPaymentDetail paymentDetail) {
        OrderPayment payment = checkPaytmPayment(paymentDetail);
        if (payment != null && payment.getPaymentStatus() != null) {
            PaymentStatus status = payment.getPaymentStatus();
            if (status.equals(PaymentStatus.SUCCESSFUL)) {
                return true;
            }
        }
        return false;
    }

    private OrderPayment checkPaytmPayment(OrderPaymentDetail paymentDetail) {
        try {
            PaytmCreateResponse paytmResponse = payTMService.getPaymentStatus(paymentDetail.getExternalOrderId(),
                    paymentDetail.getTransactionAmount());
            if (paytmResponse != null) {
                paymentDetail.setPartnerTransactionId(paytmResponse.getTransactionId());
                paymentDetail.setUpdateTime(AppUtils.getCurrentTimestamp());
                paymentDetail = paymentGatewayDao.save(paymentDetail);
                paymentDetail.setPartnerPaymentStatus(paytmResponse.getStatus());
                String paymentStatus = "TXN_SUCCESS".equals(paytmResponse.getStatus())?
                        PaymentStatus.SUCCESSFUL.name():PaymentStatus.FAILED.name();
                paymentDetail.setPaymentStatus(paymentStatus);
                return DataConverter.convert(paymentDetail,
                        masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
            }
        } catch (PaymentFailureException e) {
            LOG.error("Error while paytm payment verification", e);
        } catch (IOException e) {
            LOG.error("Error while paytm payment verification", e);
        } catch (JSONException e) {
            LOG.error("Error while paytm payment verification", e);
        }
        return null;
    }

    private Boolean checkRazorpayPayment(OrderPaymentDetail paymentDetail) {
        try {
            RazorPayPaymentResponse payment = razorPayService.fetchOrder(props.getEnvironmentType(),
                    paymentDetail.getPartnerOrderId(),paymentDetail.getBrandId());
            return payment != null && TransactionConstants.PAYMENT_STATUS_SUCCESSFUL.equalsIgnoreCase(payment.getStatus().toUpperCase())
                    || TransactionConstants.PAYMENT_STATUS_CAPTURED.equalsIgnoreCase(payment.getStatus().toUpperCase());
        } catch (PaymentFailureException e) {
            LOG.error("Error while razorpay payment verification", e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment refundByPaymentId(Integer paymentDetailId) throws IOException, PaymentFailureException {
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getPaymentDetail(paymentDetailId);
        if(paymentDetail!=null){
            OrderPayment payment = DataConverter.convert(paymentDetail,masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
            return refundPayment(payment);
        }
        return null;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateStatusOfDisassociatedPayments(String contact) {
        LOG.info("updating status of disassociated payments");
        Date timeBeforeRefundIsEligible = AppUtils.addMinutesToDate(AppUtils.getCurrentTimestamp(), 0);
		List<OrderPaymentDetail> payments = paymentGatewayDao.getRecentDisassociatedPaymentDetail(contact, timeBeforeRefundIsEligible);
		if (payments != null && payments.size() > 0) {
			for (OrderPaymentDetail opd : payments) {
				if (PaymentPartner.RAZORPAY.getSystemId(props.getEnvironmentType()) == opd.getPaymentModeId()) {
				    LOG.info("Verifying status of RazorPay payment with id " + opd.getPartnerOrderId());
					try {
						RazorPayPaymentResponse payment = razorPayService.fetchOrder(props.getEnvironmentType(),
								opd.getPartnerOrderId(),opd.getBrandId());
						if(payment != null) {
                            updateRazorPayResponseForHangingPayment(payment);
						}
					} catch (PaymentFailureException e) {
						LOG.error("Error in updaing payment response for razor pay payment id "
								+ opd.getPartnerOrderId());
					}
				} else if(PaymentPartner.PAYTM.getSystemId(props.getEnvironmentType()) == opd.getPaymentModeId()){
                    LOG.info("Verifying status of Paytm payment with id " + opd.getPartnerOrderId());
                    try {
                        if(opd.getPaymentSource().equals(ApplicationName.DINE_IN.value())){
                            PaytmStatusResponse paytmStatusResponse = payTMNewPaymentService.getPayTmPaymentStatus(opd.getExternalOrderId());
                            if(paytmStatusResponse != null){
                                LOG.info("Payment response " , paytmStatusResponse);

                                PaytmCreateResponse sudoPaymentResponse = new PaytmCreateResponse();
                                sudoPaymentResponse.setOrderId(opd.getExternalOrderId());
                                sudoPaymentResponse.setStatus(paytmStatusResponse.getBody().getResultInfo().getResultStatus());
                                sudoPaymentResponse.setTransactionId(paytmStatusResponse.getBody().getTxnId());

                                updatePaytmResponseWithoutVerification(sudoPaymentResponse);
                            }
                        } else {
                            LOG.info("Payment source is not dine in");
                        }

                    } catch (Exception e) {
                        LOG.error("Error in updating payment response for Paytm payment id "
                                + opd.getPartnerOrderId());
                    }
                }
			}
		}
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment getDisassociatedPayment(String contact) throws PaymentFailureException {
        Date timeBeforeRefundIsEligible = AppUtils.addMinutesToDate(AppUtils.getCurrentTimestamp(), -1*props.getNumberOfMinuteForRefundBlock());
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getDisassociatedPaymentDetail(contact, timeBeforeRefundIsEligible);
        if (paymentDetail != null) {
            LOG.info("Disassociated payment detail ", paymentDetail);
            if (paymentDetail.getPartnerTransactionId() == null) {
                int paymentModeId = paymentDetail.getPaymentModeId();
                EnvType env = props.getEnvironmentType();
                if (paymentModeId == PaymentPartner.PAYTM.getSystemId(env)) {
                    LOG.info("Disassociated payment is of Paytm ");
                    if(paymentDetail.getPaymentSource().equals(ApplicationName.DINE_IN.value())){
                        try {
                            PaytmStatusResponse paytmStatusResponse = payTMNewPaymentService.getPayTmPaymentStatus(paymentDetail.getExternalOrderId());
                            LOG.info("Payment response from paytm ", paytmStatusResponse);
                            PaymentStatus status = null;
                            if(paytmStatusResponse.getBody().getResultInfo().getResultStatus().equals(PaytmPaymentStatusType.TXN_SUCCESS.name())){
                                status = PaymentStatus.SUCCESSFUL;
                            } else {
                                status = PaymentStatus.FAILED;
                            }
                            paymentDetail.setPartnerTransactionId(paytmStatusResponse.getBody().getTxnId());
                            paymentDetail.setPaymentStatus(status.toString());

                            paymentDetail = paymentGatewayDao.save(paymentDetail);
                        } catch (Exception e) {
                            LOG.error("Error getting payment status from paytm " , e);
                            throw new PaymentFailureException();
                        }
                    }else {
                        return checkPaytmPayment(paymentDetail);
                    }
                } else if (paymentModeId == PaymentPartner.RAZORPAY.getSystemId(env)) {
                    LOG.info("Disassociated payment is of Razorpay ");
                    RazorPayPaymentResponse response = razorPayService.fetchOrder(env,
                            paymentDetail.getPartnerOrderId(),paymentDetail.getBrandId());
                    if (response != null) {
                        paymentDetail.setPartnerTransactionId(response.getTransactionId());
                        paymentDetail.setPartnerOrderId(response.getRazorPayOrderId());

                        //TODO improve this logic
                        PaymentStatus status = (response.getStatus().equalsIgnoreCase(TransactionConstants.PAYMENT_STATUS_SUCCESSFUL)
                                || response.getStatus().equalsIgnoreCase(TransactionConstants.PAYMENT_STATUS_CAPTURED)) ? PaymentStatus.SUCCESSFUL
                                : PaymentStatus.FAILED;
                        paymentDetail.setPaymentStatus(status.toString());
                        paymentDetail.setExternalOrderId(response.getOrderId());
                        paymentDetail = paymentGatewayDao.save(paymentDetail);
                    }
                }
            }
            return DataConverter.convert(paymentDetail, masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
        }
        return null;
    }

    private PaytmCreateResponse getSudoPaytmResponse(PaytmStatusResponse paytmStatusResponse, String externalOrderId) {
        PaytmCreateResponse paymentResponse = new PaytmCreateResponse();
        paymentResponse.setOrderId(externalOrderId);
        paymentResponse.setStatus(paytmStatusResponse.getBody().getResultInfo().getResultStatus());
        paymentResponse.setTransactionId(paytmStatusResponse.getBody().getTxnId());
        return paymentResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OrderPayment> getPendingRefunds() {
        List<OrderPaymentDetail> paymentDetailList = paymentGatewayDao.getPendingRefunds();
        if (paymentDetailList != null) {
            return paymentDetailList.stream()
                    .map(orderPaymentDetail -> DataConverter.convert(orderPaymentDetail,
                            masterCache.getPaymentMode(orderPaymentDetail.getPaymentModeId())))
                    .collect(Collectors.toList());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment refundPayment(Integer orderId) throws IOException, PaymentFailureException {
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getSuccessfulPaymentDetailFromOrderId(orderId);
        if(paymentDetail!=null){
            OrderPayment payment = DataConverter.convert(paymentDetail, masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
            return refundPayment(payment);
        }
        LOG.error("Did not find any external payment detail for orderID::::: {}",orderId);
        throw new PaymentFailureException("Could not invoke refund for Payment Detail");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment getPaymentStatus(Integer orderId) throws PaymentFailureException {
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getPaymentDetailFromOrderId(orderId);
        if(paymentDetail!=null){
            return DataConverter.convert(paymentDetail, masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaytmCreateRequest getPayTMQRCodeIdForKIOSK(OrderPaymentRequest order) throws PaymentFailureException {
        try {
            OrderPaymentDetail existingSuceessfulPayment = paymentGatewayDao
                    .getSuccessfulOrderPaymentDetail(order.getGenerateOrderId());
            if (existingSuceessfulPayment == null) {
                PaytmCreateRequest request = payTMService.createRequest(order);
                paymentGatewayDao.createRequest(request, order);
                payTMService.createPayTMQRForKIOSK(order, request);
                return request;
            } else {
                /**
                 * In case of a already existing successful payment we return null to the UI so
                 * that we can link the successful payment to the actual order
                 */
                return null;
            }
        } catch (Exception ex) {
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not create payTM QR code.");
        }
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public GPayQRResponse getGPayQRCode(OrderPaymentRequest order) throws PaymentFailureException {
		try {
			OrderPaymentDetail existingSuceessfulPayment = paymentGatewayDao
					.getSuccessfulOrderPaymentDetail(order.getGenerateOrderId());
			if (existingSuceessfulPayment == null) {
				GPayPaymentRequest request = gPayService.createRequest(order);
				paymentGatewayDao.createRequest(request, order);
				return gPayService.createGPayQR(props.getEnvironmentType(), request);
			} else {
				/**
				 * In case of a already existing successful payment we return null to the UI so
				 * that we can link the successful payment to the actual order
				 */
				return null;
			}
		} catch (Exception ex) {
			LOG.error("Exception Occurred ", ex);
			throw new PaymentFailureException("Could not create payTM QR code.");
		}
	}

    @Override
    public PaytmPaymentStatus checkKIOSKPaytmQRPaymentStatus(String orderId) throws PaymentFailureException {
        try{
            LOG.info("Checking paytm payment status for OrderID " + orderId);
            return payTMService.checkKIOSKPaytmQRPaymentStatus(orderId);
        }catch (Exception ex){
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not create payTM QR code.");
        }
    }

    @Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public GPayPaymentStatus checkGPayQRPaymentStatus(OrderPaymentRequest request) throws PaymentFailureException {
        try{
            LOG.info("Checking gpay payment status for OrderID " + request);
            return gPayService.checkPaymentStatus(props.getEnvironmentType(), request);
        }catch (Exception ex){
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not fetch status for gpay QR code.");
        }
    }

    @Override
    public boolean refundKIOSKPaytmQRPaymentAmount(String orderId, BigDecimal amountToRefund, String refundReason) throws PaymentFailureException {
        try{
            LOG.info("Refunding paytm payment amount for orderId " + orderId);
            return payTMService.refundKIOSKPaytmQRPaymentAmount(orderId, amountToRefund, refundReason);
        }catch (Exception ex){
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not refund payTM amount.");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public IngenicoQrResponse validateIngenicoCallback(String response) throws PaymentFailureException {
        try{
            IngenicoQrResponse ingenicoQrResponse = ingenicoService.getValidatedResponse(response);
            updateAndRedirectToIngenico(ingenicoQrResponse,ingenicoQrResponse, true);
            return ingenicoQrResponse;
        }catch (PaymentFailureException e){
            LOG.error("Error while validating response from ingenico :::::::: {}", e.getMessage());
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void markTransactionCancel(OrderPaymentRequest order) throws Exception {
        try{
            paymentGatewayDao.markTransactionFail(order);
        }catch (Exception e){
            LOG.error("Error while updating transaction status :::::::: {}", e.getMessage());
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void initiateAutoRefunds() {
        int tenMinutes = 600000;
        List<OrderPaymentDetail> payments = paymentGatewayDao.getInitiatedPaymentDetails(PaymentStatus.INITIATED);
        if(payments!=null && payments.size()>0){
            for(OrderPaymentDetail payment : payments){
                //initiate refunds for payments with no order id and order settlement id for more than
                if(AppUtils.getSecondsDiff(AppUtils.getCurrentTimestamp(),payment.getRequestTime()) > tenMinutes){
                    OrderPayment paymentRequest = DataConverter.convert(payment,masterCache.getPaymentMode(payment.getPaymentModeId()));
                    try {
                        paymentRequest = refundByPaymentId(payment.getOrderPaymentDetailId());
                        logRefundAttempt(paymentRequest, PaymentStatus.SUCCESSFUL, null, null);
                    } catch (IOException | PaymentFailureException e) {
                        logRefundAttempt(paymentRequest, PaymentStatus.FAILED,"refund_failed",
                                paymentRequest.getFailureReason());
                    }
                }
            }
        }
    }


    private void logRefundAttempt(OrderPayment paymentRequest, PaymentStatus paymentStatus,
                                  String errorCode, String errorDescription) {
        OrderPaymentEvent paymentEvent = new OrderPaymentEvent();
        paymentEvent.setOrderPaymentDetailId(paymentRequest.getOrderPaymentDetailId());
        paymentEvent.setEventType(PaymentStatus.REFUND_INITIATED.name());
        paymentEvent.setPaymentStatus(paymentStatus.name());
        paymentEvent.setCreateTime(AppUtils.getCurrentTimestamp());
        paymentEvent.setPaymentId(paymentRequest.getPartnerTransactionId());
        if(paymentStatus.equals(PaymentStatus.FAILED)){
            paymentEvent.setErrorCode(errorCode);
            paymentEvent.setErrorDescription(errorDescription);
        }
        paymentGatewayDao.createPaymentEvent(paymentEvent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment refundPayment(OrderPayment request) throws IOException, PaymentFailureException {
        EnvType envType =props.getEnvironmentType();

        // TODO: Add condition for PAYTM UPI
        LOG.info("paymetn partner " + PaymentPartner.PAYTM.getSystemId(props.getEnvironmentType()));
        LOG.info("paytm mode id: " + (request.getPaymentModeId() == PaymentPartner.PAYTM.getSystemId(props.getEnvironmentType())));
        if (request.getPaymentModeId() == PaymentPartner.PAYTM.getSystemId(props.getEnvironmentType())) {
            if (request.getPaymentSource().equalsIgnoreCase("DINE_IN")) {
                request = payTMNewPaymentService.refundRequest(request);
            } else {
                try {
                    request = payTMService.refundRequest(request);
                    //request = payTMService.refundPaytmUpiQR(request);
                } catch (PaymentFailureException e) {
                    String message = "Error while refunding payment details for payment with id " + request.getPartnerTransactionId();
                    LOG.error(message, e);
                    SlackNotificationService.getInstance()
                            .sendNotification(envType, "Kettle", null,
                                    SlackNotification.REFUNDS.getChannel(envType), message);
                    new ErrorNotification("Payment Refund Failure", message, e, envType).sendEmail();
                    throw new PaymentFailureException(message, e);
                }
            }
        } else if (request.getPaymentModeId() == PaymentPartner.RAZORPAY.getSystemId(envType)) {
            request = razorPayService.refundRequest(envType, request);
        } else if( request.getPaymentModeId() == PaymentPartner.INGENICO.getSystemId(envType)){
            request = ingenicoService.refundRequest(envType, request);
        }

        if (request.getPaymentStatus().equals(PaymentStatus.REFUND_PROCESSED)) {
            LOG.info("Refund processed");
            request = paymentGatewayDao.refundPayment(request);
            String message = String.format("Refund successfully processed for: \n" +
                            "Contact: %s\n" +
                            "Payment Id: %s \n" +
                            "Order Id: %s",
                    request.getContactNumber(), request.getPartnerTransactionId(), request.getExternalOrderId());

            SlackNotificationService.getInstance()
                    .sendNotification(envType, "Kettle", null,
                            SlackNotification.REFUNDS.getChannel(envType), message);
        }else{
            LOG.info("Refund failed");
            String message = String.format("Refund failed for: \n" +
                            "Contact: %s\n" +
                            "Payment Id: %s \n" +
                            "Order Id: %s" +
                            "Failure Reason: %s",
                    request.getContactNumber(), request.getPartnerTransactionId(),
                    request.getExternalOrderId(), request.getFailureReason());

            SlackNotificationService.getInstance()
                    .sendNotification(envType, "Kettle", null,
                            SlackNotification.REFUNDS.getChannel(envType), message);

        }
        return request;
    }

    private Map updateAndPushForPaytmUpi(PaytmUpiS2SResponse paytmUpiS2SResponse, PaymentResponse response, boolean validation) {
        PaymentStatus status = paytmUpiS2SResponse.getStatus().equals("SUCCESS") ? PaymentStatus.SUCCESSFUL : PaymentStatus.FAILED;
        paymentGatewayDao.updateResponse(status, response);
        Map returnResult = new HashMap<String, String>();
        if (validation) {
            returnResult.put("orderId", response.getOrderId());
            returnResult.put("txnId", response.getTransactionId());
            PushNotification<Map<String, PaymentResponse>> notification = new PaytmUpiResponseNotification(props.getEnvironmentType(), paytmUpiS2SResponse, response);
            pubnubService.sendNotification(notification);

        } else {
            returnResult.put("error", response.getOrderId());
            returnResult.put("reason", response.getReason());
        }
        return returnResult;
    }


    @Override
    public OrderPayment refundPaytmUpiQR(OrderPayment paymentRefundRequest) throws PaymentFailureException {
        try {
            LOG.info("Refunding paytm payment amount for orderId " + paymentRefundRequest.getOrderId());
            return payTMService.refundPaytmUpiQR(paymentRefundRequest);
        } catch (Exception ex) {
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not refund payTM amount.");
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updatePaytmUpiStatus(PaytmUpiS2SResponse response) throws PaymentFailureException {
        updateAndPushForPaytmUpi(response, response, true);
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaytmUpiQrResponse getPayTmUpiQRCodeId(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException {
        try {
            OrderPaymentDetail existingSuccessfulPayment = paymentGatewayDao
                    .getSuccessfulOrderPaymentDetail(order.getGenerateOrderId());
            if (existingSuccessfulPayment == null) {
                PaytmUpiQrRequest request = payTMService.createPaytmUpiRequest(order);
                paymentGatewayDao.createRequest(request, order);
                return payTMService.createPayTMUPIQR(order, request);
            } else {
                /**
                 * In case of a already existing successful payment we return null to the UI so
                 * that we can link the successful payment to the actual order
                 */
                return null;
            }
        } catch (Exception ex) {
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not create payTM UPI QR code.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AGSCreateRequest createAGSRequest(OrderPaymentRequest order)
            throws PaymentFailureException {
        AGSCreateRequest request = AGSPaymentService.createRequest(props.getEnvironmentType(), order);
        paymentGatewayDao.createRequest(request, order);
        return request;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map updateAGSPaymentCMStatus(AGSPaymentCMStatus response)
            throws PaymentFailureException {
        boolean validation = true; //because payment has been done successfully
        response.setStatus(validation ? "successful" : "failed");
        return updateAndRedirect(response, validation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AGSPaymentS2SResponse updateAGSPaymentS2SStatus(AGSPaymentS2SStatus status)
            throws PaymentFailureException {
        String errorReason = StringUtils.EMPTY;
        try{
            if(StringUtils.isNotBlank(status.getAdditionalData())){
                LOG.info("Checking if payment status already success");
                String externalOrderId = status.getAdditionalData();
                try{
                    OrderPaymentDetail orderPaymentDetail =
                            paymentGatewayDao.getActivePaymentDetail(externalOrderId);
                    if(orderPaymentDetail!=null){
                        LOG.info("orderPaymentDetail found: " + JSONSerializer.toJSON(orderPaymentDetail));
                        if(StringUtils.isNotBlank(orderPaymentDetail.getPaymentStatus())
                                && orderPaymentDetail.getPaymentStatus().equalsIgnoreCase(PaymentStatus.SUCCESSFUL.name())){
                            LOG.info("Status already saved as success");
                            return getAGSPaymentS2SResponse(AGS_IS_SAVED_SUCCESS, "Status already saved as success");
                        }
                        LOG.info("Order payment is not successful, processing further...");
                    }
                }catch (Exception ex){
                    LOG.error("Exception occurred while updating AGS Payment status , No order payment details found: ", ex);
                    return getAGSPaymentS2SResponse(AGS_IS_SAVED_FAILED, "No order payment details found.");
                }
            }
            if(isValidStatus(status, errorReason)){
                PaymentStatus paymentStatus = PaymentStatus.FAILED;
                status.setStatus("failed");
                if(StringUtils.equalsIgnoreCase(status.getResponseCode(), AGS_IS_SAVED_SUCCESS)){
                    paymentStatus = PaymentStatus.SUCCESSFUL;
                    status.setStatus("successful");
                }
                status.setOrderId(status.getAdditionalData());
                status.setTransactionId(status.getInvoiceNo());
                paymentGatewayDao.updateResponse(paymentStatus, status);
                LOG.info("Order payment does not exist, processing further...");
                return getAGSPaymentS2SResponse(AGS_IS_SAVED_SUCCESS, "Save Successfully");
            }
        }catch (Exception ex){
            LOG.error("Exception occurred while updating AGS Payment status: ", ex);
            return getAGSPaymentS2SResponse(AGS_IS_SAVED_FAILED, "Some error occurred, while updating status in system");
        }
        return getAGSPaymentS2SResponse(AGS_IS_SAVED_FAILED,
                StringUtils.isNotBlank(errorReason) ? errorReason: "Failed to save status in database.");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AGSPaymentCMResponse checkAGSPaymentS2SStatus(String externalOrderId)
            throws PaymentFailureException {
        String errorReason = StringUtils.EMPTY;
        try{
            if(StringUtils.isNotBlank(externalOrderId)){
                LOG.info("Checking if payment status already received");
                try{
                    OrderPaymentDetail orderPaymentDetail =
                            paymentGatewayDao.getActivePaymentDetail(externalOrderId);
                    if(orderPaymentDetail!=null
                            && StringUtils.isNotBlank(orderPaymentDetail.getPaymentStatus())){
                        if(orderPaymentDetail.getPaymentStatus().equalsIgnoreCase(PaymentStatus.SUCCESSFUL.name())){
                            return getAgsPaymentCMResponse(true, 6, "Payment Success.", "Payment was successful, Now placing your order.");
                        }
                    }
                }catch (Exception ex){
                    LOG.error("Exception occurred while updating AGS Payment status , No order payment details found: ", ex);
                    return getAgsPaymentCMResponse(false, 7, "Payment Failed.", "Payment has failed, Please try again.");
                }
            }
        }catch (Exception ex){
            LOG.error("Exception occurred while updating AGS Payment status: ", ex);
            return getAgsPaymentCMResponse(false, 7, "Payment Failed.", "Payment has failed, Please try again.");
        }
        return getAgsPaymentCMResponse(false, 7, "Payment Failed.", "Payment has failed, Please try again.");
    }

    private AGSPaymentCMResponse getAgsPaymentCMResponse(boolean status, Integer code, String title, String message) {
        AGSPaymentCMResponse response = new AGSPaymentCMResponse();
        response.setStatus(status);
        response.setCode(code);
        response.setTitle(title);
        response.setMsg(message);
        return response;
    }

    private AGSPaymentS2SResponse getAGSPaymentS2SResponse(String isSavedCode, String message) {
        AGSPaymentS2SResponse response = new AGSPaymentS2SResponse();
        response.setIsSaved(isSavedCode);
        response.setMessage(message);
        LOG.info("AGS payment S2S Response is..."+ JSONSerializer.toJSON(response));
        return response;
    }

    private boolean isValidStatus(AGSPaymentS2SStatus status, String errorReason) throws Exception {
        if(status!=null){
            if(StringUtils.isBlank(status.getTerminalId())){
                errorReason = "Terminal_ID is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if(StringUtils.isBlank(status.getRRN())){
                errorReason = "RRN is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if(StringUtils.isBlank(status.getInvoiceNo())){
                errorReason = "Invoice_No is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if(StringUtils.isBlank(status.getTransactionType())){
                errorReason = "Txn_Type is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if(StringUtils.isBlank(status.getAmount())){
                errorReason = "Amt is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if(StringUtils.isBlank(status.getCardNo())){
                errorReason = "Card_No is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if(StringUtils.isBlank(status.getTransactionDate())){
                errorReason = "Txn_Date is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if(StringUtils.isBlank(status.getTransactionTime())){
                errorReason = "Txn_Time is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if(StringUtils.isBlank(status.getResponseCode())){
                errorReason = "Response_Code is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if(StringUtils.isBlank(status.getAdditionalData())){
                errorReason = "additional_Data is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            if(StringUtils.isBlank(status.getChecksum())){
                errorReason = "Checksum is missing in the status request";
                LOG.info("Invalid Request: " + errorReason);
                return false;
            }
            String generatedCheckSum = generateAGSCheckSum(status);
            if(StringUtils.isNotBlank(generatedCheckSum)
                    && StringUtils.equalsIgnoreCase(generatedCheckSum, status.getChecksum())){
                LOG.info("Payment request is valid and checksum match success.");
                return true;
            }else{
                errorReason = "Payment request is invalid and checksum match failed.";
                LOG.info(errorReason);
                return false;
            }
        }
        errorReason = "Payment request is invalid or checksum match failed.";
        LOG.info(errorReason);
        return false;
    }

    private String generateAGSCheckSum(AGSPaymentS2SStatus status) throws Exception {
        String hashedString = new String();
        try{
            String checkSumMsg = getCheckSumMsgString(status);
            hashedString  = DigestUtils.md5Hex(checkSumMsg + props.getAGSChecksumKey());
            hashedString.replace("-", "").toLowerCase();
            return hashedString;
        }catch (Exception ex){
            LOG.error("Exception occurred while generating AGS checkSum: ", ex);
            return null;
        }
    }

    private String getCheckSumMsgString(AGSPaymentS2SStatus status) {
        StringBuilder checkSumMsg = new StringBuilder();
        checkSumMsg.append(status.getTerminalId());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getRRN());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getAuthCode());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getInvoiceNo());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getTransactionType());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getAmount());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getCardNo());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getTransactionDate());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getTransactionTime());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getResponseCode());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getAdditionalData());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getCardType());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getCardBrand());
        checkSumMsg.append("|");
        checkSumMsg.append(status.getCardFlag());
        return checkSumMsg.toString();
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void sendStandaloneNotification(String paymentId, String event, BasicTransactionInfo info) {
		StandaloneTransactionDetail transaction = paymentGatewayDao.getStandAloneTransaction(paymentId);
		String smsType = null;
		String emailType = null;
		if (transaction != null) {
			paymentId = transaction.getPaymentId();
			if (event.equals("payment.failed")) {
				String failureMessage = String.format(
						"COVID19_FAILURE : Call Back Request\n" + "Name : %s\n" + "Contact Number : %s\n"
								+ "Email Id : %s\n" + "Amount : %s\n" + "Error Code : %s\n"
								+ "Error Description : %s\n",
						transaction.getPaymentCustomerName(), transaction.getPaymentContactNumber(),
						transaction.getPaymentEmailId(), transaction.getPaymentAmount().intValue()/100,
						transaction.getPaymentErrorCode(), transaction.getPaymentErrorDescription());
				SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), AppConstants.KETTLE,
						SlackNotification.STANDALONE_TRANSACTION_FAILURE, failureMessage);
				LOG.info("Checking for info.isSmsFailureNotification() "+ info.isSmsFailureNotification());

				if (info.isSmsFailureNotification()) {
					smsType = StandaloneTransactionReceipt.COVID19_FAILURE;
					LOG.info("Sending SMS  "+ smsType+" to user "+transaction.getPaymentCustomerName()+ " and contact "+transaction.getPaymentContactNumber());
					sendStandaloneFailureNotification(true, transaction.getPaymentCustomerName(),
							transaction.getPaymentContactNumber());
				}
				if (info.isEmailFailureNotification()) {
					emailType = StandaloneTransactionReceipt.COVID19_FAILURE;
					StandaloneTransactionReceipt receipt = new StandaloneTransactionReceipt(transaction,
							props.getEnvironmentType(), props.getBasePath(),
							StandaloneTransactionReceipt.COVID19_FAILURE, "<EMAIL>");
					StandaloneTransactionNotification notification = new StandaloneTransactionNotification(receipt);
					try {
						notification.sendEmail();
					} catch (EmailGenerationException e) {
						LOG.error("Error In Sending Standalone Success Transaction Email",e);
						new ErrorNotification("Error In Sending Standalone Success Transaction Email", e.getMessage(),
								e, props.getEnvironmentType()).sendEmail();
					}

				}

			} else if (event.equals("order.paid")) {
				if (info.isSmsSuccessNotification()) {
					LOG.info("Skipping SMS Notification of successul transaction");
				}
				if (info.isEmailSuccessNotification()) {
					emailType = StandaloneTransactionReceipt.COVID19_SUCCESS;
					StandaloneTransactionReceipt receipt = new StandaloneTransactionReceipt(transaction,
							props.getEnvironmentType(), props.getBasePath(),
							StandaloneTransactionReceipt.COVID19_SUCCESS, "<EMAIL>");
					StandaloneTransactionNotification notification = new StandaloneTransactionNotification(receipt);
					try {
						notification.sendEmail();
					} catch (EmailGenerationException e) {
						new ErrorNotification("Error In Sending Standalone Success Transaction Email", e.getMessage(),
								e, props.getEnvironmentType()).sendEmail();
					}
				}

			}

		}
		if(smsType != null || emailType != null) {
			paymentGatewayDao.setNotificationDetail(paymentId, smsType, emailType);
		}

	}

	private boolean sendStandaloneFailureNotification(boolean sendSms, String customerName, String contactNumber) {
		if (contactNumber.startsWith("+91") && contactNumber.substring(3).length() == 10) {
			contactNumber = contactNumber.substring(3);
		} else {
			LOG.error("Could not send COVID_19_FAILURE_NOTIFICATION to customer " + contactNumber + " and name "
					+ customerName);
			return false;
		}
		try {
			String message = CustomerSMSNotificationType.COVID_19_FAILURE_NOTIFICATION.getMessage(customerName);
			boolean status = notificationService.sendNotification(
					CustomerSMSNotificationType.COVID_19_FAILURE_NOTIFICATION.name(), message, contactNumber,
					providerService.getSMSClient(
							CustomerSMSNotificationType.COVID_19_FAILURE_NOTIFICATION.getTemplate().getSMSType(),
							ApplicationName.KETTLE_SERVICE),
					sendSms,null);
			return status;
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending message to the  COVID_19_FAILURE_NOTIFICATION campaign contact number" + contactNumber, e);
		}
		return false;
	}


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaytmCreateRequestResponseWrapper createPaytmRequestForDineIn(OrderPaymentRequest order, String ssoToken)
            throws Exception {
        PaytmCreateRequest request = payTMNewPaymentService.createRequestDineIn(order);
        LOG.info("::::::::::::::::: Paytm Request generated ::::::::::::::::");
        LOG.info(JSONSerializer.toJSON(request));
        paymentGatewayDao.createRequest(request, order);
        /*
        Adding signature in header
         */
        PaytmParams paytmParams = createPaytmParams(request, ssoToken);
        PaytmParamResponse paytmParamResponse = initiatePaytmPayment(paytmParams);
        if(paytmParamResponse == null
                || paytmParamResponse.getBody() == null
                || paytmParamResponse.getBody().getResultInfo() == null
                || paytmParamResponse.getBody().getResultInfo().getResultStatus().equals("F")){
            LOG.error("Error in initiating payment request ", paytmParamResponse);
            throw new PaymentFailureException();
        }
        PaytmCreateRequestResponseWrapper paytmCreateRequestResponseWrapper =
                new PaytmCreateRequestResponseWrapper(request, paytmParamResponse);
        return paytmCreateRequestResponseWrapper;
    }

    private PaytmParams createPaytmParams(PaytmCreateRequest request , String ssoToken ){
        PaytmParams paytmParams = new PaytmParams();
        PaytmParamHead head = new PaytmParamHead();
        paytmParams.setHead(head);
        PaytmParamBody body = new PaytmParamBody();
        paytmParams.setBody(body);
        PaytmTransactionAmount txnAmount = new PaytmTransactionAmount();
        body.setTxnAmount(txnAmount);
        txnAmount.setCurrency("INR");
        txnAmount.setValue(request.getTransactionAmount());
        PaytmUserInfo userInfo = new PaytmUserInfo();
        body.setUserInfo(userInfo);
        userInfo.setCustId(request.getCustomerId());
        body.setCallbackUrl(paytmConfig.getDineInCallBackUrl());
        body.setMid(paytmConfig.getDineInPaytmMid());
        body.setOrderId(request.getOrderId());
        body.setPaytmSsoToken(ssoToken);
        body.setRequestType("Payment");
        body.setWebsiteName(paytmConfig.getDineInPaytmWebsiteApp());

        return paytmParams;
    }

//    private PaytmParamResponse createPaytmParamResponse(OrderPaymentRequest order, PaytmCreateRequest request) {
//        PaytmParamResponse paytmParamResponse = new PaytmParamResponse();
//        PaytmParamResponseHead head = new PaytmParamResponseHead();
//        paytmParamResponse.setHead(head);
//        PaytmParamResponseBody body = new PaytmParamResponseBody();
//        paytmParamResponse.setBody(body);
//    }

    @Override
    public PaytmParamResponse initiatePaytmPayment(PaytmParams paytmParams) throws Exception {
        return payTMNewPaymentService.initiatePaytmPayment(paytmParams);
    }

    @Override
    public PaytmOauth getPaytmOauth(PaytmOauthRequest paytmOauthRequest) throws Exception {
        return payTMNewPaymentService.getPaytmOauth(paytmOauthRequest);
    }

    @Override
    public PaytmOauthResponse getPaytmOauth2V3(PaytmOauthRequest paytmOauthRequest) {
        return payTMNewPaymentService.getPaytmOauth2V3(paytmOauthRequest);
    }

    @Override
    public boolean revokePaytmToken(PaytmOauthRequest paytmOauthRequest) {
        return payTMNewPaymentService.revokePaytmToken(paytmOauthRequest);
    }

    @Override
    public PaytmValidateSSOTokenResponse validatePaytmSSOToken(String ssoToken) {
        return payTMNewPaymentService.validatePaytmSSOToken(ssoToken);
    }

    @Override
    public String getPaytmAccessToken(PaytmFetchPaymentOptionsRequest request) throws Exception {
        return payTMNewPaymentService.getPaytmAccessToken(request);
    }

    @Override
    public String fetchPaytmPaymentOptions(PaytmFetchPaymentOptionsRequest request) {
        return payTMNewPaymentService.fetchPaytmPaymentOptions(request);
    }

    @Override
    public String fetchPaytmPaymentOptionsV2(PaytmFetchPaymentOptionsRequest request) {
        return payTMNewPaymentService.fetchPaytmPaymentOptionsV2(request);
    }

    public static void main(String[] args) {
		String number = "+919988776655";
		if (number.startsWith("+91") && number.substring(3).length() == 10) {
			System.out.println(number.substring(3));
		}
	}
    //TODO: To be removed once AGS integration is done
    /*public static void main(String args[])throws Exception{
        try{
            PaymentServiceImpl paymentService = new PaymentServiceImpl();
            AGSPaymentS2SStatus status= new AGSPaymentS2SStatus();
            status.setTerminalId("RA123047");
            status.setRRN("003716236379");
            status.setAuthCode("881524");
            status.setInvoiceNo("236379");
            status.setTransactionType("SALE");
            status.setAmount("11.11");
            status.setCardNo("817200******0013");
            status.setTransactionDate("2020-02-06");
            status.setTransactionTime("16:52:32.3870000");
            status.setResponseCode("00");
            status.setAdditionalData("KO20200206165217298");
            status.setCardType("RUPAY");
            status.setCardBrand("DEBIT");
            status.setCardFlag("RDD");
            status.setChecksum("09b5a8066e23386c1af820ca46998627");

            String error = StringUtils.EMPTY;
            paymentService.isValidStatus(status, error);
        }catch (Exception ex){
            System.out.println("Some exception occurred."+ ex);
        }
    }*/
}
