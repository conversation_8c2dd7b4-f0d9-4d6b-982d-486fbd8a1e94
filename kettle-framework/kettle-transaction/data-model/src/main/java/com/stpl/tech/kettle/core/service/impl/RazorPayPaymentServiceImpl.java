package com.stpl.tech.kettle.core.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.razorpay.Payment;
import com.razorpay.RazorpayClient;
import com.razorpay.RazorpayException;
import com.razorpay.Refund;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.service.RazorPayPaymentService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.razorpay.RazorPayCreateRequest;
import com.stpl.tech.master.payment.model.razorpay.RazorPayEnv;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentResponse;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentStatus;
import com.stpl.tech.master.payment.model.razorpay.RazorPayServiceRequest;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.codec.binary.Hex;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class RazorPayPaymentServiceImpl implements RazorPayPaymentService {

	private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";
	private static final Logger LOG = LoggerFactory.getLogger(RazorPayPaymentServiceImpl.class);

	@Autowired
	private PaymentGatewayDao paymentGatewayDao;

	@Autowired
	private EnvironmentProperties props;


	public RazorPayCreateRequest createRequest(EnvType type, OrderPaymentRequest order)
			throws PaymentFailureException, DataNotFoundException {
		RazorPayEnv env = RazorPayEnv.get(type,order.getBrandId());
		try {
			RazorpayClient razorpayClient = new RazorpayClient(env.getKey(), env.getAuthKey());
			RazorPayServiceRequest request = new RazorPayServiceRequest(order.getGenerateOrderId(), order.getPaidAmount());
			ObjectMapper mapper = new ObjectMapper();
			String jsonString = mapper.writeValueAsString(request);
			System.out.println("jsonString : " + jsonString);
			JSONObject object = new JSONObject(jsonString);
			LOG.info("Create payment request {}", object);
			com.razorpay.Order paymentRequest = razorpayClient.Orders.create(object);
			RazorPayCreateRequest finalRequest = new RazorPayCreateRequest(request, paymentRequest.get("id"));
			return finalRequest;
		}  catch (Throwable e) {
			String message = "Error while creating request using razor pay for order with id "
					+ order.getGenerateOrderId();
			LOG.error(message, e);
			SlackNotificationService.getInstance().sendNotification(type, "", "kettle_errors", message);
			List<String> errors = new ArrayList<>();
			errors.add(e.getMessage());
			new ErrorNotification("Payment Request Failure", message, errors, type).sendEmail();
			throw new PaymentFailureException(message, e);
		}
	}

	public boolean validateResponse(EnvType type, RazorPayPaymentResponse response, Integer brandId) {
		if (response.getSignature() != null) {
			RazorPayEnv env = RazorPayEnv.get(type,brandId);
			try {
				Mac sha256HMAC = Mac.getInstance(HMAC_SHA256_ALGORITHM);
				SecretKeySpec secretKey = new SecretKeySpec(env.getAuthKey().getBytes("UTF-8"), HMAC_SHA256_ALGORITHM);
				sha256HMAC.init(secretKey);

				String hash = Hex.encodeHexString(sha256HMAC.doFinal(
						(response.getRazorPayOrderId() + "|" + response.getTransactionId()).getBytes("UTF-8")));
				return hash.equals(response.getSignature());
			} catch (Exception e) {
				LOG.error("Error while validating razorpay request id " + response.getOrderId(), e);
				return false;
			}
		}
		return false;

	}

	@Override
	public RazorPayPaymentResponse fetchPayment(EnvType type, String paymentId) throws PaymentFailureException {
		OrderPaymentDetail orderPaymentDetail = paymentGatewayDao.getPaymentDetail(paymentId);
		RazorPayEnv env = RazorPayEnv.get(type,orderPaymentDetail.getBrandId());
		try {
			RazorpayClient razorpayClient = new RazorpayClient(env.getKey(), env.getAuthKey());
			Payment payment = razorpayClient.Payments.fetch(paymentId);
			RazorPayPaymentResponse response = new RazorPayPaymentResponse();
			response.setRazorPayOrderId(payment.get("order_id"));
			response.setStatus(payment.get("status"));
			response.setTransactionId(paymentId);
			LOG.info("Got Razor Pay payment data from fetch call " + payment.toString());
			return response;
		} catch (RazorpayException e) {
			String message = "Error while fetchng payment details for payment with id " + paymentId;
			LOG.error(message, e);
			SlackNotificationService.getInstance().sendNotification(type, "", "kettle_errors", message);
			new ErrorNotification("Payment Fetch Failure", message, e, type).sendEmail();
			throw new PaymentFailureException(message, e);
		}
	}

	@Override
	public RazorPayPaymentResponse fetchOrder(EnvType type, String razorPayOrderId,Integer brandId) throws PaymentFailureException {

		RazorPayEnv env = RazorPayEnv.get(type,brandId);
		try {
			RazorpayClient razorpayClient = new RazorpayClient(env.getKey(), env.getAuthKey());
			LOG.info("Looking for razor pay order with id {}" ,razorPayOrderId);
			List<Payment> payments = razorpayClient.Orders.fetchPayments(razorPayOrderId);
			Payment order = null;

			if (payments.size() > 1) {
				order = payments.stream()
						.filter(payment -> payment.get("status").equals("successful")
								|| payment.get("status").equals("captured"))
						.findAny().orElse(payments.get(payments.size() - 1));
			} else if (payments.size() == 1) {
				order = payments.get(0);
			}
			if (order == null) {
				return null;
			}
			LOG.info("Got Razor Pay payment data from fetch call " + order.toString());
			RazorPayPaymentResponse response = new RazorPayPaymentResponse();
			response.setRazorPayOrderId(razorPayOrderId);
			response.setStatus(order.get("status").toString().toUpperCase());
			response.setTransactionId(order.get("id"));
			return response;
		} catch (RazorpayException e) {
			String message = "Error while fetchng payment details for payment with id " + razorPayOrderId;
			LOG.error(message, e);
			SlackNotificationService.getInstance().sendNotification(type, "", "kettle_errors", message);
			new ErrorNotification("Payment Fetch Failure", message, e, type).sendEmail();
			throw new PaymentFailureException(message, e);
		}
	}

	@Override
	public PaymentStatus getPaymentStatus(EnvType type, String paymentId, BigDecimal transactionAmount) throws PaymentFailureException {
		RazorPayPaymentResponse response = fetchPayment(type,paymentId);
		String status = response.getStatus();
		if(status.equals("success") || status.equals("captured")){
			return PaymentStatus.SUCCESSFUL;
		}
		return PaymentStatus.FAILED;
	}

	@Override
	public OrderPayment refundRequest(EnvType type, OrderPayment request) throws PaymentFailureException {
		RazorPayEnv env = RazorPayEnv.get(type, request.getBrandId());
		String paymentId = request.getPartnerTransactionId();
		try {
			RazorpayClient razorpayClient = new RazorpayClient(env.getKey(), env.getAuthKey());
			Refund refund = razorpayClient.Payments.refund(paymentId);
			String refundId = refund.get("id");
			if(refundId!=null){
				request.setPaymentStatus(PaymentStatus.REFUND_PROCESSED);
				request.setRefundStatus(PaymentStatus.CREATED);
				request.setRefundId(refundId);
			}else{
				request.setPaymentStatus(PaymentStatus.REFUND_FAILED);
				request.setRefundStatus(PaymentStatus.FAILED);
			}
			return request;
		} catch (Exception e) {
			String error = "Error while creating refund request. Response from Razorpay :::: " + JSONSerializer.toJSON(e);
			LOG.error(error);
			request.setPaymentStatus(PaymentStatus.REFUND_FAILED);
			request.setRefundStatus(PaymentStatus.FAILED);
		}
		return request;
	}

	@Override
	public OrderPaymentDetail handleDisassociatedPayment(OrderPaymentDetail paymentDetail) throws PaymentFailureException {
		LOG.info("Disassociated payment is of Razorpay ");
		RazorPayPaymentResponse response = fetchOrder(props.getEnvironmentType(),
				paymentDetail.getPartnerOrderId(),paymentDetail.getBrandId());
		if (response != null) {
			paymentDetail.setPartnerTransactionId(response.getTransactionId());
			paymentDetail.setPartnerOrderId(response.getRazorPayOrderId());

			//TODO improve this logic
			PaymentStatus status = (response.getStatus().equalsIgnoreCase(TransactionConstants.PAYMENT_STATUS_SUCCESSFUL)
					|| response.getStatus().equalsIgnoreCase(TransactionConstants.PAYMENT_STATUS_CAPTURED)) ? PaymentStatus.SUCCESSFUL
					: PaymentStatus.FAILED;
			paymentDetail.setPaymentStatus(status.toString());
			paymentDetail.setExternalOrderId(response.getOrderId());
			return paymentGatewayDao.save(paymentDetail);
		} else{
			throw new PaymentFailureException();
		}
	}

	@Override
	public OrderPaymentDetail handleDisassociatedPaymentForDineIn(OrderPaymentDetail paymentDetail) throws PaymentFailureException {
		LOG.info("Disassociated payment is of Razorpay ");
		RazorPayPaymentResponse response = fetchOrder(props.getEnvironmentType(),
				paymentDetail.getPartnerOrderId(),paymentDetail.getBrandId());
		if (response != null) {
			paymentDetail.setPartnerTransactionId(response.getTransactionId());
			paymentDetail.setPartnerOrderId(response.getRazorPayOrderId());

			//TODO improve this logic
			paymentDetail.setPaymentStatus(PaymentStatus.REFUND_INITIATED.name());
			paymentDetail.setExternalOrderId(response.getOrderId());
			return paymentGatewayDao.save(paymentDetail);
		} else{
			throw new PaymentFailureException();
		}
	}


	@Override
	public Boolean checkRazorpayPayment(OrderPaymentDetail paymentDetail) {
		try {
			RazorPayPaymentResponse payment = fetchOrder(props.getEnvironmentType(),
					paymentDetail.getPartnerOrderId(),paymentDetail.getBrandId());
			return payment != null && TransactionConstants.PAYMENT_STATUS_SUCCESSFUL.equalsIgnoreCase(payment.getStatus().toUpperCase())
					|| TransactionConstants.PAYMENT_STATUS_CAPTURED.equalsIgnoreCase(payment.getStatus().toUpperCase());
		} catch (PaymentFailureException e) {
			LOG.error("Error while razorpay payment verification", e);
		}
		return false;
	}

	@Override
	public RazorPayPaymentResponse fetchRazorPayPayment(String paymentId) throws PaymentFailureException {
		RazorPayPaymentResponse payment = null;
		if (paymentId != null) {
			payment = fetchPayment(props.getEnvironmentType(), paymentId);
			OrderPaymentDetail paymentDetail = paymentGatewayDao
					.getSuccessfulPaymentDetailFromOrderId(payment.getRazorPayOrderId());
			if (paymentDetail == null) {
				throw new PaymentFailureException("No Payment Entry found in our system for id " + paymentId);
			} else {
				payment.setOrderId(paymentDetail.getExternalOrderId());
				return payment;
			}
		}
		return payment;
	}

	@Override
	public RazorPayCreateRequest createRazorPayRequest(OrderPaymentRequest order) throws DataNotFoundException, PaymentFailureException {
		RazorPayCreateRequest request = createRequest(props.getEnvironmentType(), order);
		paymentGatewayDao.createRequest(request, order);
		return request;
	}

	@Override
	public Map updateRazorPayResponse(RazorPayPaymentResponse response,Integer brandId) {
		boolean validation = validateResponse(props.getEnvironmentType(), response,brandId);
	//	boolean validation = true;
		response.setStatus(validation ? "successful" : "failed");
		return paymentGatewayDao.updateAndRedirect(response, validation);
	}

	@Override
	public Map updateRazorPayResponseWithoutVerification(RazorPayPaymentResponse response) {
		LOG.info("Updating payment without s v");
		if (response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.SUCCESSFUL.value()) ||
				response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.CAPTURED.value())
				|| response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.AUTHORIZED.value())) {
			response.setStatus("successful");
			return paymentGatewayDao.updateAndRedirect(response, true);
		} else {
			response.setStatus("failed");
			return paymentGatewayDao.updateAndRedirect(response, false);
		}
	}

	@Override
	public Map updateRazorPayResponseForHangingPayment(RazorPayPaymentResponse response) {
		boolean validation = false;
		if (response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.SUCCESSFUL.value()) ||
				response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.CAPTURED.value())
				|| response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.AUTHORIZED.value())) {
			response.setStatus("successful");
			validation = true;
		} else {
			response.setStatus("failed");
			validation = false;
		}
		return paymentGatewayDao.update(response, validation);
	}

	@Override
	public void updateRefundedRazorPayPayment(RazorPayPaymentResponse response) {
		paymentGatewayDao.markPaymentRefunded(response);
	}
	@Override
	public void updateRefundInitiatedRazorPayPayment(RazorPayPaymentResponse response) {
		paymentGatewayDao.markPaymentRefundInitiated(response);
	}

	@Override
	public Map updateRazorPayResponseForHangingPaymentDineIn(RazorPayPaymentResponse response) {
		boolean validation = false;
		if (response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.SUCCESSFUL.value()) ||
				response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.CAPTURED.value())
				|| response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.AUTHORIZED.value())) {
			response.setStatus("successful");
			validation = true;
		} else {
			response.setStatus("failed");
			validation = false;
		}
		return paymentGatewayDao.updateForDineIn(response, validation);
	}

}