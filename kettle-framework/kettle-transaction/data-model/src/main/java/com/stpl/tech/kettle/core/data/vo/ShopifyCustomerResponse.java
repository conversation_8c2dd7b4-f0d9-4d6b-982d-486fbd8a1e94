package com.stpl.tech.kettle.core.data.vo;

import com.google.gson.annotations.SerializedName;
import com.stpl.tech.master.domain.model.ShopifyAddress;
import lombok.*;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor

public class ShopifyCustomerResponse {


    private Integer iid;

    @SerializedName("first_name")
    private String firstName;

    @SerializedName("last_name")
    private String lastName;

    private String countryCode="+91";
    private  String email;

    @SerializedName("verified_email")
    private boolean verifiedEmail;

    private int loyaltyPoints;

    private BigDecimal chaayosCash;

    private Integer unitId=11001;

    private String acquisitionSource;

    private String acquisitionToken;

    @SerializedName("orders_count")
    private Integer ordersCount;

    private boolean loyaltySubscriber;


    private List<ShopifyAddress> addresses;

    @SerializedName("accepts_marketing")
    private Boolean acceptsMarketing;
    @SerializedName("created_at")
    private String createdAt;
    @SerializedName("updated_at")
    private String updatedAt;



    private String state;

    @SerializedName("total_spent")
    private BigDecimal totalSpent;

    @SerializedName("last_order_id")
    private Long lastOrderId;



    private String  note;



    @SerializedName("multi_pass")
    private String multipassIdentifier;

    @SerializedName("tax_exempt")
    private boolean taxExempt;
    private String tags;

    @SerializedName("last_order_name")
    private String lastOrderName;


    private String currency;

    // contains +91 also
    private String phone;


    @SerializedName("accepts_marketing_updated_at")
    private String acceptsMarketingUpdatedAt;

    @SerializedName("marketing_opt_in_level")
    private Object marketingOptInLevel;
    @SerializedName("tax_exemptions")
    private List<String> taxExemptions;

    @SerializedName("email_marketing_consent")
    private Object emailMarketingConsent;

    @SerializedName("sms_marketing_consent")
    private Object smsMarketingConsent;

    @SerializedName("admin_graphql_api_id")
    private  String adminGraphqlApiId;

    @SerializedName("default_address")
    private ShopifyAddress defaultAddress;

    private Integer brandId;

    public String getPhone(){
        if(this.phone!=null && phone.charAt(0)=='+')
        {
            return phone.substring(3);
        }
        else {
            return this.phone;
        }

    }
}
