package com.stpl.tech.kettle.data.dao;

import java.util.List;

import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface TableDataDao extends AbstractDao {

	public List<UnitTableMappingDetail> findTablesForUnit(int unitId);

	public UnitTableMappingDetail searchTableForUnit(int unitId, int tableNumber);

	public UnitTableMappingDetail reserveTableForUnit(int unitId, int tableNumber);

	public void addOrderTableMapping(OrderDetail orderDetail, Integer tableRequestId, Customer customer) throws DataUpdationException;

	public void refreshTableSummary(Integer tableRequestId);

	public UnitTableMappingDetail findUnitTableMappingForTableRequestId(Integer tableRequestId);

}
