/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * ChannelPartner generated by hbm2java
 */
@Entity
@Table(name = "CASH_CARD_EVENT")
public class CashCardEvent implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5089884634778314279L;
	private Integer cashCardEventId;
	private int cashCardId;
	private int orderId;
	private int orderSettlementId;
	private BigDecimal settlementAmount;
	private String settlementStatus;
	private Date settlementTime;
//
//	private BigDecimal openingBalance;
//	private BigDecimal closingBalance;

	public CashCardEvent() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "CASH_CARD_EVENT_ID", unique = true, nullable = false)
	public Integer getCashCardEventId() {
		return this.cashCardEventId;
	}

	public void setCashCardEventId(Integer loyaltyPointsId) {
		this.cashCardEventId = loyaltyPointsId;
	}

	@Column(name = "CASH_CARD_ID", nullable = false)
	public int getCashCardId() {
		return cashCardId;
	}

	public void setCashCardId(int cashCardId) {
		this.cashCardId = cashCardId;
	}

	@Column(name = "ORDER_ID", nullable = false)
	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	@Column(name = "ORDER_SETTLEMENT_ID", nullable = false)
	public int getOrderSettlementId() {
		return orderSettlementId;
	}

	public void setOrderSettlementId(int orderSettlementId) {
		this.orderSettlementId = orderSettlementId;
	}

	@Column(name = "SETTLEMENT_AMOUNT", nullable = false, precision = 10)
	public BigDecimal getSettlementAmount() {
		return settlementAmount;
	}

	public void setSettlementAmount(BigDecimal settlementAmount) {
		this.settlementAmount = settlementAmount;
	}

	@Column(name = "SETTLEMENT_STATUS", nullable = false, length = 20)
	public String getSettlementStatus() {
		return settlementStatus;
	}

	public void setSettlementStatus(String settlementStatus) {
		this.settlementStatus = settlementStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "SETTLEMENT_TIME", nullable = false, length = 19)
	public Date getSettlementTime() {
		return settlementTime;
	}

	public void setSettlementTime(Date settlementTime) {
		this.settlementTime = settlementTime;
	}

//
//	@Column(name = "OPENING_BALANCE", nullable = true)
//	public BigDecimal getOpeningBalance() {
//		return openingBalance;
//	}
//
//	public void setOpeningBalance(BigDecimal openingBalance) {
//		this.openingBalance = openingBalance;
//	}
//
//	@Column(name = "CLOSING_BALANCE", nullable = true)
//	public BigDecimal getClosingBalance() {
//		return closingBalance;
//	}
//
//	public void setClosingBalance(BigDecimal closingBalance) {
//		this.closingBalance = closingBalance;
//	}

}