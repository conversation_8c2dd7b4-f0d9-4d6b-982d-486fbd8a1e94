/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.service.WorkStationService;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.OrderSearchDao;
import com.stpl.tech.kettle.data.dao.WorkStationDao;
import com.stpl.tech.kettle.data.model.AssemblyLogData;
import com.stpl.tech.kettle.data.model.AssemblyTATData;
import com.stpl.tech.kettle.data.model.MonkLogData;
import com.stpl.tech.kettle.data.model.WorkStationManualTaskDetail;
import com.stpl.tech.kettle.data.model.WsManualTaskErrorType;
import com.stpl.tech.kettle.data.model.WsManualTaskType;
import com.stpl.tech.kettle.domain.model.AssemblyLog;
import com.stpl.tech.kettle.domain.model.AssemblyTAT;
import com.stpl.tech.kettle.domain.model.MonkCalibrationEvent;
import com.stpl.tech.kettle.domain.model.MonkCalibrationStatus;
import com.stpl.tech.kettle.domain.model.MonkCalibrationStatusType;
import com.stpl.tech.kettle.domain.model.MonkCalibrationTime;
import com.stpl.tech.kettle.domain.model.MonkLog;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.TATSummary;
import com.stpl.tech.kettle.domain.model.TATType;
import com.stpl.tech.kettle.domain.model.WorkstationLog;
import com.stpl.tech.kettle.domain.model.WorkstationManualTask;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Singh
 * @date 05-Apr-2016 10:40:43 pm
 *
 */
@Service
@Slf4j
public class WorkStationServiceImpl implements WorkStationService {

	@Autowired
	private WorkStationDao workStationDao;

	@Autowired
	private OrderSearchDao orderSearchDao;

	@Autowired
	private MasterDataCache masterDataCache;

	private List<com.stpl.tech.kettle.data.model.WorkstationLog> convert(List<WorkstationLog> loglist) {
		List<com.stpl.tech.kettle.data.model.WorkstationLog> wlogList = new ArrayList<com.stpl.tech.kettle.data.model.WorkstationLog>();
		for (WorkstationLog log : loglist) {
			com.stpl.tech.kettle.data.model.WorkstationLog wlog = new com.stpl.tech.kettle.data.model.WorkstationLog();
			wlog.setBillCreationTime(log.getBillCreationTime());
			wlog.setCancelled(log.isCancelled());
			wlog.setCooktopStation(log.getCooktopStation());
			wlog.setDimension(log.getDimension());
			wlog.setDispatched(log.isDispatched());
			wlog.setEmployeeId(log.getEmployeeId());
			wlog.setItemId(log.getItemId());
			wlog.setItemQuantity(log.getItemQuantity());
			wlog.setOrderId(log.getOrderId());
			wlog.setOrderSource(log.getOrderSource());
			wlog.setProductId(log.getProductId());
			wlog.setStationTasksForOrder(log.getStationTasksForOrder());
			wlog.setTimeToAcknowledge(log.getTimeToAcknowledge());
			wlog.setTimeToCancel(log.getTimeToCancel());
			wlog.setTimeToProcess(log.getTimeToProcess());
			wlog.setTimeToProcessByMachine(log.getTimeToProcessByMachine());
			wlog.setTimeToStart(log.getTimeToStart());
			wlog.setType(log.getType());
			wlog.setUnitId(log.getUnitId());
			wlog.setWorkstationLogId(log.getWorkstationLogId());
			wlog.setMonkName(log.getMonkName());
			wlogList.add(wlog);
		}
		return wlogList;
	}

	private List<AssemblyLogData> convertAssembyList(List<AssemblyLog> logList) {
		List<AssemblyLogData> alogList = new ArrayList<AssemblyLogData>();
		for (AssemblyLog log : logList) {
			AssemblyLogData logdata = new AssemblyLogData();
			logdata.setAssemblyLogDataId(log.getAssemblyLogId());
			logdata.setBillingServerTime(log.getBillingServerTime());
			logdata.setChannelPartner(log.getChannelPartner());
			logdata.setCooktopStation(log.getCooktopStation());
			logdata.setDeliveryPartner(log.getDeliveryPartner());
			logdata.setOrderSource(log.getOrderSource());
			logdata.setStationEventsForOrder(log.getStationEventsForOrder());
			logdata.setTimeToAcknowledge(log.getTimeToAcknowledge());
			logdata.setTimeToDispatch(log.getTimeToDispatch());
			logdata.setTimeToProcessByWorkstations(log.getTimeToProcessByWorkstations());
			logdata.setTimeToProcessCold(log.getTimeToProcessCold());
			logdata.setTimeToProcessFood(log.getTimeToProcessFood());
			logdata.setTimeToProcessHot(log.getTimeToProcessHot());
			logdata.setTimeToReadyForDispatch(log.getTimeToReadyForDispatch());
			logdata.setUnitId(log.getUnitId());
			logdata.setTimeToCancel(log.getTimeToCancel());
			logdata.setOrderId(log.getOrderId());
			logdata.setTimeToDeliver(log.getTimeToDeliver());
			alogList.add(logdata);
		}
		return alogList;
	}

	@Override
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public int addWorkStationLog(List<WorkstationLog> workstationLog) {
        Integer noOfLogsAdded = workStationDao.addWorkStationLog(convert(workstationLog));
        if(noOfLogsAdded!=null && noOfLogsAdded > 0){
            for(WorkstationLog wsLog : workstationLog){
                MonkLog monkLog = wsLog.getMonkLog();
                if(monkLog!=null){
                    addMonkLog(monkLog);
                }
            }
        }
        return noOfLogsAdded;
	}

	@Override
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public int addAssemblyLog(List<AssemblyLog> assemblyLog) {
		return workStationDao.addAssemblyLog(convertAssembyList(assemblyLog));
	}

    @Override
    @Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int addMonkLog(MonkLog monkLog) {
        return workStationDao.addMonkLog(convertMonkLog(monkLog));
    }

	@Override
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public AssemblyTAT getAssemblyTAT(Integer unitId) {
		Date businessDate = AppUtils.getBusinessDate();
		List<AssemblyTATData> unitTAT = workStationDao.getAssemblyTAT(unitId, businessDate);
		AssemblyTAT tat = new AssemblyTAT(unitId, businessDate);
		for(AssemblyTATData tatData: unitTAT){
			TATSummary summary = DataConverter.convert(tatData);
			switch (TATType.valueOf(tatData.getType())){
				case LAST_HOUR: tat.setLastHour(summary);
								break;
				case TODAY:
				default: tat.setToday(summary);
						break;
			}
		}
		return tat;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
	public void processTATForAllUnits() {
		workStationDao.processTATForAllUnits();
	}

	private List<MonkLogData> convertMonkLog(MonkLog monkLog) {
        List<MonkLogData> monkLogDataList = new ArrayList<>();

        Integer orderId = monkLog.getOrderId();
        Integer orderItemId = monkLog.getTaskId();
        String monkName = monkLog.getMonkName();
        Date taskCreationTime = monkLog.getTaskCreationTime();
        String reassigned = monkLog.isReassigned() ? AppConstants.YES : AppConstants.NO;
        Map<Integer,Long> eventMap = monkLog.getEventMap();

        for(Integer event : eventMap.keySet()){
            MonkLogData monkLogData = new MonkLogData();
            monkLogData.setOrderId(orderId);
            monkLogData.setOrderItemId(orderItemId);
            monkLogData.setMonkName(monkName);
            monkLogData.setEvent(event.toString());
            monkLogData.setTaskCreationTime(taskCreationTime);
            monkLogData.setTimeElapsedAtEvent(eventMap.get(event).intValue());
            monkLogData.setReassigned(reassigned);
            monkLogDataList.add(monkLogData);
        }

	    return monkLogDataList;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<WorkstationManualTask> addWorkStationManualTasks(List<WorkstationManualTask> workstationTasks) {
		if (Objects.nonNull(workstationTasks) && !workstationTasks.isEmpty()) {
			for (WorkstationManualTask task : workstationTasks) {
				try {
					Order order = orderSearchDao.getOrderDetail(task.getGeneratedOrderId());
					verifyManualTask(order, task);
				} catch (Exception e) {
					log.error("Error while fetching and verifying task: {}", task, e);
					task.setErrorType(WsManualTaskErrorType.ORDER_NOT_FOUND.name());
				}
			}
			List<WorkStationManualTaskDetail> taskDetails = workStationDao.addWorkStationManualTasks(workstationTasks);
			workstationTasks = new ArrayList<>();
			for (WorkStationManualTaskDetail taskDetail : taskDetails) {
				workstationTasks.add(DataConverter.convert(taskDetail));
			}
		}
		return workstationTasks;
	}

	private void verifyManualTask(Order order, WorkstationManualTask task) {
		if (Objects.isNull(order)) {
			task.setErrorType(WsManualTaskErrorType.ORDER_NOT_FOUND.name());
		} else if (Objects.isNull(task.getUnitId()) || order.getUnitId() != task.getUnitId()) {
			task.setErrorType(WsManualTaskErrorType.WRONG_UNIT_ID.name());
		} else if (Objects.isNull(task.getOrderTime()) || !compareOrderTime(order.getBillCreationTime(), task.getOrderTime())) {
			task.setErrorType(WsManualTaskErrorType.WRONG_ORDER_TIME.name());
		} else if (Objects.isNull(task.getTaskType()) || (!task.getTaskType().equals(WsManualTaskType.REMAKE.name()) && order.getStatus().equals(OrderStatus.SETTLED))) {
			task.setErrorType(WsManualTaskErrorType.ORDER_ALREADY_SETTLED.name());
		} else {
			List<OrderItem> orderItems = order.getOrders();
			boolean productFound = false;
			for (OrderItem orderItem : orderItems) {
				if (Objects.isNull(task.getProductItemId()) || orderItem.getProductId() == task.getProductItemId()) {
					productFound = true;
					if (Objects.isNull(task.getDimension()) || !orderItem.getDimension().equals(task.getDimension())) {
						task.setErrorType(WsManualTaskErrorType.DIMENSION_NOT_MATCHED.name());
					} else if (Objects.isNull(task.getQuantity()) || orderItem.getQuantity() != task.getQuantity()) {
						task.setErrorType(WsManualTaskErrorType.QUANTITY_NOT_MATCHED.name());
					} else {
						List<WorkstationManualTask> savedManualTasks = workStationDao.getWorkStationManualTasks(order.getGenerateOrderId());
						if (Objects.nonNull(savedManualTasks) && !savedManualTasks.isEmpty()) {
							for (WorkstationManualTask savedTask : savedManualTasks) {
								if (Objects.nonNull(savedTask) && task.getUnitId().equals(savedTask.getUnitId()) &&
										task.getProductItemId().equals(savedTask.getProductItemId()) && task.getDimension().equals(savedTask.getDimension())
										&& task.getQuantity().equals(savedTask.getQuantity())) {
									if (task.getTaskType().equals(WsManualTaskType.REMAKE.name())) {
										task.setErrorType(WsManualTaskErrorType.DUPLICATE_REMAKE_TASK.name());
									} else {
										task.setErrorType(WsManualTaskErrorType.DUPLICATE_MANUAL_TASK.name());
									}
									return;
								}
							}
						}
						task.setErrorType(WsManualTaskErrorType.NO_ERROR.name());
						return;
					}
				} else if (Boolean.FALSE.equals(productFound)) {
					task.setErrorType(WsManualTaskErrorType.PRODUCT_NOT_FOUND.name());
				}
			}
		}
	}

	private boolean compareOrderTime(Date billCreationTime, Date orderTime) {
		long duration = Math.abs(orderTime.getTime() - billCreationTime.getTime());
		return TimeUnit.MILLISECONDS.toHours(duration) < AppConstants.WS_MANUAL_TASK_ORDER_TIME_DIFF;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public MonkCalibrationEvent addMonkCalibrationEvent(MonkCalibrationEvent monkCalibrationEvent) {
		return workStationDao.addMonkCalibrationEvent(monkCalibrationEvent);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public MonkCalibrationEvent getLastMonkCalibrationStatus(Integer unitId, Integer monkNo) {
		return workStationDao.getLastMonkCalibrationStatus(unitId, monkNo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<MonkCalibrationStatus> getMonkCalibrationStatus(Integer unitId) {

		int TOTAL_MONK_COUNT = 5;
		int dayOfWeek = AppUtils.getDayOfWeek(AppUtils.getCurrentTimestamp());
		Date startTime = getUnitStartTimeOrDefault(unitId, dayOfWeek);

		List<MonkCalibrationEvent> calibrationEvents = new ArrayList<>();
		for (int i = 0; i < TOTAL_MONK_COUNT; i += 1) {
			MonkCalibrationEvent calibrationEvent = workStationDao.getLastMonkCalibrationStatus(unitId, i + 1);
			if (Objects.nonNull(calibrationEvent)) {
				calibrationEvents.add(calibrationEvent);
			}
		}
		List<MonkCalibrationStatus> calibrationStatusList = new ArrayList<>();
		if (Objects.nonNull(calibrationEvents) && !calibrationEvents.isEmpty()) {
			Map<Integer, Date> monksLastCalibTimeMap = new HashMap<>();
			Map<Integer, Boolean> monksCalibStatusMap = new HashMap<>();
			Map<Integer, Boolean> monksFailedCalibMap = new HashMap<>();
			for (MonkCalibrationEvent calibrationEvent : calibrationEvents) {
				if (monksLastCalibTimeMap.containsKey(calibrationEvent.getMonkNo())) {
					if (calibrationEvent.getServerTime().compareTo(monksLastCalibTimeMap.get(calibrationEvent.getMonkNo())) > 0) {
						monksLastCalibTimeMap.put(calibrationEvent.getMonkNo(), calibrationEvent.getServerTime());
						if (MonkCalibrationStatusType.CALIBRATED.name().equals(calibrationEvent.getCalibrationStatus())) {
							monksCalibStatusMap.put(calibrationEvent.getMonkNo(), true);
							monksFailedCalibMap.put(calibrationEvent.getMonkNo(), false);
						} else if (MonkCalibrationStatusType.FAILED_CALIBRATION.name().equals(calibrationEvent.getCalibrationStatus())) {
							monksCalibStatusMap.put(calibrationEvent.getMonkNo(), false);
							monksFailedCalibMap.put(calibrationEvent.getMonkNo(), true);
						} else {
							monksCalibStatusMap.put(calibrationEvent.getMonkNo(), false);
							monksFailedCalibMap.put(calibrationEvent.getMonkNo(), false);
						}
					}
				} else {
					monksLastCalibTimeMap.put(calibrationEvent.getMonkNo(), calibrationEvent.getServerTime());
					if (MonkCalibrationStatusType.CALIBRATED.name().equals(calibrationEvent.getCalibrationStatus())) {
						monksCalibStatusMap.put(calibrationEvent.getMonkNo(), true);
						monksFailedCalibMap.put(calibrationEvent.getMonkNo(), false);
					} else if (MonkCalibrationStatusType.FAILED_CALIBRATION.name().equals(calibrationEvent.getCalibrationStatus())) {
						monksCalibStatusMap.put(calibrationEvent.getMonkNo(), false);
						monksFailedCalibMap.put(calibrationEvent.getMonkNo(), true);
					} else {
						monksCalibStatusMap.put(calibrationEvent.getMonkNo(), false);
						monksFailedCalibMap.put(calibrationEvent.getMonkNo(), false);
					}
				}
			}

			for (Map.Entry<Integer, Date> entry : monksLastCalibTimeMap.entrySet()) {
				MonkCalibrationStatus calibrationStatus = new MonkCalibrationStatus();
				calibrationStatus.setUnitId(unitId);
				calibrationStatus.setUnitOpenTime(startTime);
				calibrationStatus.setMonkNo(entry.getKey());
				//keep monk calibrated if last calibration time of same date or if time has not crossed daily monk calibration time
				//which is 30 min before cafe start time
				if (Boolean.TRUE.equals(monksCalibStatusMap.get(entry.getKey()))) {
					if (Objects.nonNull(entry.getValue()) && (Boolean.TRUE.equals(AppUtils.isSameDate(entry.getValue(), AppUtils.getCurrentDate())) ||
							isValidMonkCalibrationTime(new Date(startTime.getTime() - AppConstants.MONK_CALIBRATION_MARGIN_TIME_MS)))) {
						calibrationStatus.setCalibrated(true);
						calibrationStatus.setCalibrationFailed(false);
						calibrationStatus.setLastCalibrationTime(entry.getValue());
					} else {
						calibrationStatus.setCalibrated(false);
						calibrationStatus.setCalibrationFailed(false);
					}
				} else if (Boolean.TRUE.equals(monksFailedCalibMap.get(entry.getKey()))) {
					calibrationStatus.setCalibrated(false);
					calibrationStatus.setCalibrationFailed(true);
				} else {
					calibrationStatus.setCalibrated(false);
					calibrationStatus.setCalibrationFailed(false);
				}
				calibrationStatusList.add(calibrationStatus);
			}
		}
		return calibrationStatusList;
	}

	private boolean isValidMonkCalibrationTime(Date calibrationTime) {
		Calendar businessCalendar = Calendar.getInstance();
		Calendar presCalendar = Calendar.getInstance();
		businessCalendar.setTime(calibrationTime);
		return presCalendar.get(Calendar.HOUR_OF_DAY) < businessCalendar.get(Calendar.HOUR_OF_DAY) ||
				(presCalendar.get(Calendar.HOUR_OF_DAY) == businessCalendar.get(Calendar.HOUR_OF_DAY) &&
						presCalendar.get(Calendar.MINUTE) <= businessCalendar.get(Calendar.MINUTE));
	}

	@Override
	public MonkCalibrationTime getMonkCalibrationTime(Integer unitId) {
		int dayOfWeek = AppUtils.getDayOfWeek(AppUtils.getCurrentTimestamp());
		MonkCalibrationTime calibrationTime = new MonkCalibrationTime();
		calibrationTime.setUnitId(unitId);
		calibrationTime.setTodayCalibrationTime(new Date(getUnitStartTimeOrDefault(unitId, dayOfWeek).getTime() - AppConstants.MONK_CALIBRATION_MARGIN_TIME_MS));
		calibrationTime.setNextDayCalibrationTime(new Date(getUnitStartTimeOrDefault(unitId, (dayOfWeek % 7) + 1).getTime() - AppConstants.MONK_CALIBRATION_MARGIN_TIME_MS));
		calibrationTime.setServerTime(AppUtils.getCurrentTimestamp());
		return calibrationTime;
	}

	private Date getUnitStartTimeOrDefault(Integer unitId, Integer dayOfWeek) {
		Date startTime = null;
		for (UnitHours uhrs : masterDataCache.getUnit(unitId).getOperationalHours()) {
			if (Objects.nonNull(uhrs) && Objects.nonNull(uhrs.getDineInOpeningTime()) && uhrs.getDayOfTheWeekNumber() == dayOfWeek) {
				startTime = new Date(uhrs.getDineInOpeningTime().getTime());
			}
		}
		if (Objects.isNull(startTime)) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTimeInMillis(0);
			calendar.set(Calendar.HOUR_OF_DAY, 7);
			calendar.set(Calendar.MINUTE, 0);
			calendar.set(Calendar.SECOND, 0);
			startTime = new Date(calendar.getTimeInMillis());
		}
		return startTime;
	}
}
