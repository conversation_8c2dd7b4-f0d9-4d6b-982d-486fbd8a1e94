package com.stpl.tech.kettle.core.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.service.PartnerCardService;
import com.stpl.tech.kettle.data.dao.ExternalPartnerCardDao;
import com.stpl.tech.kettle.data.model.ExternalPartnerCardDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;
import com.stpl.tech.util.AppUtils;

public abstract class PartnerCardServiceImpl implements PartnerCardService {

	@Autowired
	private MasterDataCache masterCache;
	@Autowired
	private ExternalPartnerCardDao partnerCardDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<ExternalPartnerCardDetail> getPartnerCardDetail(String cardNumber, String partnerCode, String status){
		return partnerCardDao.getPartnerCardDetail(cardNumber, partnerCode, status);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public ExternalPartnerDetail getExternalPartner(String partnerCode) {
		return masterCache.getExternalPartnerMap().get(partnerCode);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String getUniquePartnerBillNumber() {
		String billNo = AppUtils.generateRandomOrderId(14);
		List<ExternalPartnerCardDetail> cardDetails = partnerCardDao.getUniquePartnerBillNumber(billNo);
		if (cardDetails.size() > 0) {
			getUniquePartnerBillNumber();
		}
		return billNo;
	}

}
