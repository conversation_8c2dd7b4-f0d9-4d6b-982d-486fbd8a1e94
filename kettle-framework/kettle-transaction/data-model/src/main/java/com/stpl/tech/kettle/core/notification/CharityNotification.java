package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class CharityNotification extends EmailNotification{

	private final CharityReceipt receipt;

	public CharityNotification() {
		this.receipt = null;
	}

	public CharityNotification(CharityReceipt receipt) {
		this.receipt = receipt;
	}

	public String subject() {
		return (TransactionUtils.isDev(receipt.getDetail().getEnv()) ? EnvType.DEV.name() + " " : "")
				+ " Charity Order for Unit Name : " + receipt.getDetail().getUnit().getName() + ": " + receipt.getDetail().getOrder().getGenerateOrderId();
	}

	public String body() throws EmailGenerationException {

		try {
			return receipt.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	public String getFromEmail() {
		return receipt.getFromEmail();
	}

	public String[] getToEmails() {
		return receipt.getToEmail().split(",");
	}

	@Override
	public EnvType getEnvironmentType() {
		return receipt.getDetail().getEnv();
	}

}
