/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * ChannelPartner generated by hbm2java
 */
@Entity
@Table(name = "CASH_CARD_DETAIL")
public class CashCardDetail implements java.io.Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 6897233910000236303L;
    private Integer cashCardId;
    private String cardNumber;
    private String cardSerial;
    private String cardStatus;
    private Date startDate;
    private Date endDate;
    private Date creationTime;
    private Integer buyerId;
    private Date purchaseTime;
    private Date activationTime;
    private Integer customerId;
    private Integer offerId;
    private BigDecimal cashInitialAmount;
    private BigDecimal initialOffer;
    private BigDecimal cashPendingAmount;
    private Date lastModified;
    private String cardType;
    private String serialNumber;
    private Integer purchaseOrderId;

    public CashCardDetail() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "CASH_CARD_ID", unique = true, nullable = false)
    public Integer getCashCardId() {
        return this.cashCardId;
    }

    public void setCashCardId(Integer loyaltyPointsId) {
        this.cashCardId = loyaltyPointsId;
    }

    @Column(name = "CUSTOMER_ID", nullable = true)
    public Integer getCustomerId() {
        return this.customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "START_DATE", nullable = false, length = 10)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Column(name = "CARD_NUMBER", nullable = false, length = 8, unique = true)
    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    @Column(name = "CARD_SERIAL", unique = true)
    public String getCardSerial() {
        return cardSerial;
    }

    public void setCardSerial(String cardSerial) {
        this.cardSerial = cardSerial;
    }

    @Column(name = "CARD_STATUS", nullable = false, length = 30)
    public String getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "END_DATE", length = 10)
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false, length = 19)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "CARD_INITIAL_AMOUNT", nullable = false, precision = 10)
    public BigDecimal getCashInitialAmount() {
        return cashInitialAmount;
    }

    public void setCashInitialAmount(BigDecimal cashInitialAmount) {
        this.cashInitialAmount = cashInitialAmount;
    }

    @Column(name = "CARD_PENDING_AMOUNT", nullable = false, precision = 10)
    public BigDecimal getCashPendingAmount() {
        return cashPendingAmount;
    }

    public void setCashPendingAmount(BigDecimal cashPendingAmount) {
        this.cashPendingAmount = cashPendingAmount;
    }

    @Column(name = "BUYER_ID")
    public Integer getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Integer buyerId) {
        this.buyerId = buyerId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "PURCHASE_DATE", length = 19)
    public Date getPurchaseTime() {
        return purchaseTime;
    }

    public void setPurchaseTime(Date purchaseTime) {
        this.purchaseTime = purchaseTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ACTIVATION_DATE", length = 10)
    public Date getActivationTime() {
        return activationTime;
    }

    public void setActivationTime(Date activationTime) {
        this.activationTime = activationTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_MODIFIED", length = 19)
    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

	@Column(name = "CASH_CARD_OFFER_ID", nullable = true)
	public Integer getOfferId() {
		return offerId;
	}

	public void setOfferId(Integer offerId) {
		this.offerId = offerId;
	}

    @Column(name = "CARD_INITIAL_OFFER", nullable = true, precision = 10)
	public BigDecimal getInitialOffer() {
		return initialOffer;
	}

	public void setInitialOffer(BigDecimal initialOffer) {
		this.initialOffer = initialOffer;
	}
	
	@Column(name = "CARD_TYPE", nullable = true, length = 30)
	public String getCardType() {
		return cardType;
	}
	
	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	@Column(name = "SERIAL_NUMBER", nullable = true, length = 20)
	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}  
	
	@Column(name = "PURCHASE_ORDER_ID", nullable = true)
	public Integer getPurchaseOrderId() {
		return purchaseOrderId;
	}

	public void setPurchaseOrderId(Integer purchaseOrderId) {
		this.purchaseOrderId = purchaseOrderId;
	}


}
