/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "AUDIT_FORM_INFO")
public class AuditFormInfo implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8089009987499975326L;
	private Integer auditFormInfoId;
	private String responseId;
	private String responseToken;
	private Integer unitId;
	private Integer managerId;
	private Integer managerOnDuty;
	private Date auditDate;
	private Date auditTime;
	private Integer auditorId;

	private FeedbackForm feedbackForm;

	public AuditFormInfo() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "AUDIT_FORM_INFO_ID", unique = true, nullable = false)
	public Integer getAuditFormInfoId() {
		return this.auditFormInfoId;
	}

	public void setAuditFormInfoId(Integer feedbackInfoId) {
		this.auditFormInfoId = feedbackInfoId;
	}

	@Column(name = "RESPONSE_ID", nullable = true, length = 100)
	public String getResponseId() {
		return this.responseId;
	}

	public void setResponseId(String responseId) {
		this.responseId = responseId;
	}

	@Column(name = "RESPONSE_TOKEN", nullable = true, length = 100)
	public String getResponseToken() {
		return this.responseToken;
	}

	public void setResponseToken(String responseToken) {
		this.responseToken = responseToken;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FORM_ID", nullable = false)
	public FeedbackForm getFeedbackForm() {
		return feedbackForm;
	}

	public void setFeedbackForm(FeedbackForm feedbackForm) {
		this.feedbackForm = feedbackForm;
	}
	@Column(name = "UNIT_ID", nullable = true)
	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}
	@Column(name = "MANAGER_ID", nullable = true)
	public Integer getManagerId() {
		return managerId;
	}

	public void setManagerId(Integer managerId) {
		this.managerId = managerId;
	}

	@Column(name = "DUTY_MANAGER_ID", nullable = true)
	public Integer getManagerOnDuty() {
		return managerOnDuty;
	}

	public void setManagerOnDuty(Integer managerOnDuty) {
		this.managerOnDuty = managerOnDuty;
	}
	@Temporal(TemporalType.DATE)
	@Column(name = "AUDIT_DATE", nullable = false, length = 10)
	public Date getAuditDate() {
		return auditDate;
	}

	public void setAuditDate(Date auditDate) {
		this.auditDate = auditDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "AUDIT_TIME", nullable = true, length = 19)
	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}
	@Column(name = "AUDITOR_ID", nullable = true)
	public Integer getAuditorId() {
		return auditorId;
	}

	public void setAuditorId(Integer auditorId) {
		this.auditorId = auditorId;
	}

}