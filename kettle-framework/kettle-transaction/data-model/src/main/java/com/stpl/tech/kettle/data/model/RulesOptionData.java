package com.stpl.tech.kettle.data.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "RULES_OPTION_DATA")
public class RulesOptionData implements Serializable {

	private static final long serialVersionUID = 8712896100509063830L;

	private Integer optionDataId;

	private int optionId;

	private String optionType;

	private RulesData rule;

	private Integer option1ProductId;

	private Integer option2ProductId;
	
	private List<RulesOptionResultData> optionData = new ArrayList<RulesOptionResultData>(0);

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "OPTION_DATA_ID", unique = true, nullable = false)
	public Integer getOptionDataId() {
		return optionDataId;
	}

	public void setOptionDataId(Integer optionDataId) {
		this.optionDataId = optionDataId;
	}

	@Column(name = "OPTION_ID", nullable = false)
	public int getOptionId() {
		return optionId;
	}

	public void setOptionId(int optionId) {
		this.optionId = optionId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "RULE_ID", nullable = false)
	public RulesData getRule() {
		return rule;
	}

	public void setRule(RulesData rule) {
		this.rule = rule;
	}

	@Column(name = "OPTION_1_PRODUCT_ID", nullable = true)
	public Integer getOption1ProductId() {
		return option1ProductId;
	}

	public void setOption1ProductId(Integer option1ProductId) {
		this.option1ProductId = option1ProductId;
	}

	@Column(name = "OPTION_2_PRODUCT_ID", nullable = true)
	public Integer getOption2ProductId() {
		return option2ProductId;
	}

	public void setOption2ProductId(Integer option2ProductId) {
		this.option2ProductId = option2ProductId;
	}

	@Column(name = "OPTION_TYPE", nullable = false, length = 10)
	public String getOptionType() {
		return optionType;
	}

	public void setOptionType(String optionType) {
		this.optionType = optionType;
	}
	
	@OneToMany(fetch = FetchType.EAGER, mappedBy = "optionData")
	public List<RulesOptionResultData> getOptionData() {
		return optionData;
	}

	public void setOptionData(List<RulesOptionResultData> optionData) {
		this.optionData = optionData;
	}

	
}
