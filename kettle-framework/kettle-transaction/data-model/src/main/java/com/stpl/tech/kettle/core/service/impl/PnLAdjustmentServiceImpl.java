package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField;
import com.stpl.tech.kettle.core.service.PnLAdjustmentService;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.PnLAdjustmentDao;
import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.domain.model.AdjustmentFieldData;
import com.stpl.tech.kettle.domain.model.AdjustmentFieldsResponse;
import com.stpl.tech.kettle.domain.model.PnlAdjustment;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.PnlAdjustmentStatus;
import com.stpl.tech.util.AppUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Column;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;


@Service
@Transactional(rollbackFor = Exception.class, transactionManager = "TransactionDataSourceTM")
public class PnLAdjustmentServiceImpl implements PnLAdjustmentService {


    @Autowired
    private PnLAdjustmentDao dao;

    @Autowired
    private MasterDataCache cache;

    @Override
    public AdjustmentFieldsResponse getAdjustmentFields(String type) {
        AdjustmentFieldsResponse response = new AdjustmentFieldsResponse();
        response.setType(type);
        List<AdjustmentFieldData> list = new ArrayList<AdjustmentFieldData>();
        Class<UnitExpenditureDetail> aClass = UnitExpenditureDetail.class;
        Map<String, String> map = getGettersMap(UnitExpenditureDetail.class);
        for (Field field : aClass.getDeclaredFields()) {
            Annotation annotation = field.getDeclaredAnnotation(ExpenseField.class);
            AdjustmentFieldData f = new AdjustmentFieldData();
            if (annotation instanceof ExpenseField) {
                ExpenseField expenseField = (ExpenseField) annotation;
                if (expenseField.calculationType().name().contentEquals(type)) {
                    f.setName(field.getName());
                    f.setDetail(expenseField.detail());
                    f.setType(field.getType().getName());
                    if (map.containsKey(field.getName())) {
                        f.setColumnName(map.get(field.getName()));
                    }
                    list.add(f);
                }
            }
        }
        response.setFields(list);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PnlAdjustment createPnLAdjustment(PnlAdjustmentDetail adjustmentDetail) {
        return DataConverter.convert(dao.add(adjustmentDetail));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PnlAdjustment updatePnLAdjustmentStatus(PnlAdjustmentDetail pnlAdjustmentDetail, PnlAdjustmentStatus status) {
        Date currentTimeStamp = AppUtils.getCurrentTimestamp();
        if (status.equals(PnlAdjustmentStatus.APPROVED)) {
            pnlAdjustmentDetail.setStatus(status.value());
            pnlAdjustmentDetail.setApprovalTime(currentTimeStamp);
        } else if (status.equals(PnlAdjustmentStatus.CANCELLED)) {
            pnlAdjustmentDetail.setStatus(status.value());
            pnlAdjustmentDetail.setCancellationTime(currentTimeStamp);
        } else if (status.equals(PnlAdjustmentStatus.REJECTED)) {
            pnlAdjustmentDetail.setStatus(status.value());
            pnlAdjustmentDetail.setRejectionTime(currentTimeStamp);
        }
        return DataConverter.convert(dao.update(pnlAdjustmentDetail));

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String ,ListData> getAdjustmentReasons() throws DataNotFoundException {
        return cache.getListAdjustmentCommentData();

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PnlAdjustment> getAdjustments(PnlAdjustmentStatus status, List<Integer> unitIds, int startMonth, int startYear) {
       return dao.getAdjustments(status,unitIds,startMonth,startYear);
    }

    @Override
    public List<PnlAdjustment> getAdjustmentImpact(String headerName, List<Integer> unitId, BigDecimal value, int month, int year) {
        return null;
    }


    public   Map<String, String> getGettersMap(Class<?> clazz) {
        Map<String, String> map = new HashMap<String, String>();
        Method[] methods = clazz.getMethods();
        for (Method m : methods) {

            if (m.isAnnotationPresent(Column.class) && m.getName().startsWith("get")) {
                Column annotationNameAtt = m.getAnnotation(Column.class);
                String n = m.getName().substring(3);
                n = n.substring(0, 1).toLowerCase() + n.substring(1, n.length());
                map.put(n, annotationNameAtt.name());
            }
        }
        return map;
    }


    @Override
    public Map<String,AdjustmentFieldData> getAdjustmentMaps(String type) {
        Map<String,AdjustmentFieldData> adjustmentMap = new HashMap<>();
        Class<UnitExpenditureDetail> aClass = UnitExpenditureDetail.class;
        Map<String, String> map = getGettersMap(UnitExpenditureDetail.class);
        for (Field field : aClass.getDeclaredFields()) {
            Annotation annotation = field.getDeclaredAnnotation(ExpenseField.class);
            AdjustmentFieldData f = new AdjustmentFieldData();
            if (annotation instanceof ExpenseField) {
                ExpenseField expenseField = (ExpenseField) annotation;
                if (expenseField.calculationType().name().contentEquals(type)) {
                    f.setName(field.getName());
                    f.setDetail(expenseField.detail());
                    f.setType(field.getType().getName());
                    if (map.containsKey(field.getName())) {
                        f.setColumnName(map.get(field.getName()));
                    }
                  adjustmentMap.put(map.get(field.getName()),f);
                }
            }
        }

        return adjustmentMap;
    }
}
