/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.InventoryThresholdType;
import com.stpl.tech.kettle.domain.model.ProductInventory;
import com.stpl.tech.util.notification.AbstractTemplate;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class InventoryStockOutNotification extends AbstractTemplate {

	private String basePath;
	private final Map<String, Set<ProductInventory>> stockedOutInventoryByUnitCategory;
	private final InventoryThresholdType thresholdType;
	private final UnitCategory unitCategory;
	private final Map<String, Object> data = new HashMap<String, Object>();

	public InventoryStockOutNotification(Map<String, Set<ProductInventory>> stockedOutInventoryByUnitCategory,
			InventoryThresholdType thresholdType, UnitCategory unitCategory, String basePath) {
		super();
		this.stockedOutInventoryByUnitCategory = stockedOutInventoryByUnitCategory;
		this.thresholdType = thresholdType;
		this.unitCategory = unitCategory;
		this.basePath = basePath;
	}

	@Override
	public String getTemplatePath() {
		return "template/StockedOutInventory.html";
	}

	@Override
	public String getFilepath() {
		return basePath + "/inventory/stockout/curdate.html";
	}

	@Override
	public Map<String, Object> getData() {
		data.put("stockedOutInventory", stockedOutInventoryByUnitCategory);
		data.put("thresholdType", thresholdType);
		data.put("currentTime", AppUtils.getCurrentDate().toString());
		return data;
	}

	public InventoryThresholdType getThresholdType() {
		return thresholdType;
	}

	public UnitCategory getUnitCategory() {
		return unitCategory;
	}

}
