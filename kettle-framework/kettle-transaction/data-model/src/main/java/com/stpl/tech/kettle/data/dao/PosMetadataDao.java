/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.core.ReportStatus;
import com.stpl.tech.kettle.data.model.CreditAccountDetail;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetail;
import com.stpl.tech.kettle.data.model.ItemConsumptionEstimate;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.data.model.PartnerUnitProductStockData;
import com.stpl.tech.kettle.data.model.PullDetailReasons;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.data.model.UnitExpenditureAggregateDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.data.model.UnitPullDetail;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimate;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimateRequest;

import javax.persistence.NoResultException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Interface for accessing all metadata related to POS. This is the read only
 * API for the metadata.
 *
 * <AUTHOR>
 *
 */
public interface PosMetadataDao extends AbstractDao {

	public TransactionMetadata getTransactionData() throws DataNotFoundException;

	public List<UnitDetail> getDSRConfigpartnerId() throws  DataNotFoundException;

	public List<PartnerAttributes> getDSRConfig(Integer partnerId) throws DataNotFoundException;

	public PartnerAttributes updatePartnerAttributes(PartnerAttributes partner) throws DataNotFoundException,DataUpdationException;

	public PartnerAttributes addPartnerAttributes(PartnerAttributes partner) throws DataNotFoundException,DataUpdationException;

	public int closeDay(int unitId, int employeeId, String comment, int startOrderId, int lastOrderId,
			Date currentDate) throws DataUpdationException;

	public void markDayCloseAsCancelled(int dayClosureId);

	public boolean isDayClosed(int unitId, Date date) throws DataUpdationException;

	public List<UnitExpenditureDetail> getAllPnlListForClosureId(Integer dayClosureId);

	public ListData getComplimentaryCodes(boolean getAll) throws DataNotFoundException;

	public List<IdCodeName> getAllDeliveryPartner() throws DataNotFoundException;

	public List<UnitClosureDetails> getClosureFromBusinessDate(Date businessDate);

	public List<UnitClosureDetails> getClosureForBusinessDate(Date businessDate, Set<Integer> units);

	public boolean updateDayCloseDate(int closureId, Date businessDate) throws DataUpdationException;

	public void updateSalesData(Date businessDate);

	public List<UnitToDeliveryPartnerMappings> getAllDeliveryPartnerMappings();

	public CreditAccountDetail addCreditAccount(CreditAccountDetail detail);

	public CreditAccountDetail updateCreditAccount(CreditAccountDetail detail);

	public List<CreditAccountDetail> getAllCreditAccounts(String status);

	public List<ItemConsumptionEstimate> getAllActiveItemConsumptionEstimate();

	public void updateConsumptionEstimates(Date businessDate);

	public UnitClosure getUnitsClosure(int unitId, Date date);

	public TransactionMetadata getTransactionData(UnitCategory category) throws DataNotFoundException;

	/**
	 * @param businessDate
	 */
	public void updateDailySalesData(Date businessDate);

	/**
	 * @param closureId
	 * @return
	 * @throws DataUpdationException
	 */
	public boolean updateReportStatus(int closureId, ReportStatus status);

	/**
	 * @param businessDate
	 * @return
	 */
	public List<UnitClosureDetails> getClosures(Date businessDate, ReportStatus status);

	/**
	 * @param businessDate
	 * @return
	 */
	public List<UnitClosureDetails> getPendingPnLClosureForBusinessDate(Date businessDate);

	/**
	 * @param businessDate
	 * @return
	 */
	public List<UnitClosureDetails> getClosureForBusinessDate(Date businessDate);


	public void updateSuggestiveConsumptionEstimates(Date businessDate,int unitId);

	public void updateUptsForPreviousDate(Date businessDate,int unitId);

	public void callProductEsimateUpdateProc(int unitId, Date currentDate);

    public CrmAppScreenDetail markPreviousCrmScreenInactive(CrmAppScreenDetail detail);

    public List<CrmAppScreenDetail> getCrmScreenUrl(String data) throws DataNotFoundException;

	public UnitExpenditureAggregateDetail getPnLMap(Integer unitId) throws DataNotFoundException, NoResultException;

    boolean resetMeterReading(Integer unitId);

    List<CrmAppScreenDetail> getCrmScreenUrlForUnit(Integer unitId);

	List<CrmAppScreenDetail> getCrmScreenUrlForUnitV1(Integer unitId);

	List<CrmAppScreenDetail> getCrmAppScreenDetail(String region);

    boolean cloneCrmAppScreenDetail(IdCodeName unitId);

	public Map<Integer, List<Date>> getUnitClosingTimeMap(Date previousDate);

	void createPreviousDateStockOutEntry(Date previousDate);

	UnitWiseDayOfWeekWiseItemEstimate getConsumptionEstimates(UnitWiseDayOfWeekWiseItemEstimateRequest request);

	public List<PullDetailReasons> getPullDetailReasons();

	public List<UnitPullDetail> getPendingUnitPullDetails(int unitId);


	UnitClosureDetails getKettleDayClose(Integer unitId, Date previousDate);

    UnitClosureDetails getLastClosureDetail(int unitId);

	Map<String,PartnerUnitProductStockData> getPreviousDayStockData(int unit, Date businessDate);
}
