package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.master.payment.model.ezetap.EzetapCreateRequest;
import com.stpl.tech.master.payment.model.ezetap.EzetapPaymentResponse;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.util.EnvType;

import java.math.BigDecimal;
import java.util.Map;

public interface EzetapPaymentService {

	public EzetapCreateRequest createRequest(EnvType type, OrderPaymentRequest order) throws PaymentFailureException;
	
	public EzetapPaymentResponse fetchPayment(EnvType type, String paymentId) throws PaymentFailureException;

	public PaymentStatus getPaymentStatus(EnvType type, String paymentId, BigDecimal transactionAmount) throws PaymentFailureException;

	public EzetapPaymentResponse fetchOrder(EnvType type, String razorPayOrderId) throws PaymentFailureException;

	public OrderPayment refundRequest(EnvType type, OrderPayment request) throws PaymentFailureException;

    EzetapCreateRequest createEzetapRequest(OrderPaymentRequest order) throws PaymentFailureException;

    Map updateEzetapResponse(EzetapPaymentResponse response);
}
