package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "MONK_ORDER_DETAIL")
public class MonkOrderDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MONK_ORDER_ID")
    private int monkOrderId;

    @Column(name = "REMAKE_ORDER")
    String remakeOrder;

    @Column(name = "REMAKE_REASON")
    String remakeReason;

    @Column(name = "ORDER_ID")
    int orderId;

    @Column(name = "ORDER_ITEM_ID")
    int itemId;

    @Column(name = "NUMBER_OF_BOILS")
    int noOfBoils;//monkStatusLog recipe string item level

    @Column(name = "EXP_NUMBER_OF_BOILS")
    String expectedNoOfBoils;//monkStatusLog recipe string item level

    @Column(name = "STEEPING_TIME")
    String steepingTime;

    @Column(name = "STEEWING_TIME")
    String steewingTime;

    @Column(name = "EXP_MILK_QUANTITY")
    String expectedMilkQuatity;//monkStatusLog recipe string item level

    @Column(name = "EXP_WATER_QUANTITY")
    String expectedWaterQuatity;//monkStatusLog recipe string item level

    @Column(name = "ACTUAL_MILK_QUANTITY")
    String actualMilkQuatity;//monkStatusLog status code item level

    @Column(name = "ACTUAL_WATER_QUANTITY")
    String actualWaterQuatity;//monkStatusLog status code item level

    @Column(name = "MONK_NAME")
    String monkName;

    @Column(name = "CALIBRATION_COEFF")
    int calibrationCoef;

    @Column(name = "RECIPE_VERSION")
    String recipeVersion;//monkStatusLog recipe version item level

    @Column(name = "RECIPE_STRING")
    String recipeString;//monkStatusLog recipe string item level

    @Column(name = "FINAL_QUANTITY")
    double finalQuantity;

    @Column(name = "EXP_FINAL_QUANTITY")
    double expectedFinalQuantity;

    @Column(name = "PRODUCT_QUANTITY")
    int productQuantity;

    @Column(name = "PRODUCT_ID")
    int productId;

    @Column(name = "PRODUCT_NAME")
    String productName;

    @Column(name = "PRODUCT_DIMENSION")
    String productDimension;

    @Column(name = "UNIT_NAME")
    String unitName;

    @Column(name = "UNIT_ID")
    int unitId;

    @Column(name = "BILLING_SERVER_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    Date billingServerTime;

    @Column(name = "TASK_START_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    Date taskStartTime;

    @Column(name = "TASK_COMPLETION_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    Date taskCompletionTime;

    @Column(name = "TASK_COMPLETED_IN")
    double taskCompletedIn;

    @Column(name = "ASSEMBLY_ORDER_CREATION_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    Date assemblyOrderCreationTime;

    @Column(name = "IDEAL_PREPARATION_TIME")
    String preperationTime;

    @Column(name = "STEEPING_TIME_IN_SEC")
    Long steepingTimeInSeconds = 0L;

    @Column(name = "STEEWING_TIME_IN_SEC")
    Long steewingTimeInSeconds = 0L;

    @Column(name = "TASK_COMPLETED_IN_SEC")
    Long taskCompletedInSeconds = 0L;

    @Column(name="FORCEFULLY_REMOVED")
    String forcefullyRemoved;

}
