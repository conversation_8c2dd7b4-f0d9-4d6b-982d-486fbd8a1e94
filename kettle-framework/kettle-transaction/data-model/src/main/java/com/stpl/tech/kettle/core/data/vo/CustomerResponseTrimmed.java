package com.stpl.tech.kettle.core.data.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stpl.tech.kettle.domain.model.SubscriptionInfoDetail;
import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;


@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class CustomerResponseTrimmed {

    private int id;
    private String name;
    private String contact;
    private String email;

    private  Date addTime;

    private Integer unitId;
    private boolean contactVerified;

    private TrueCallerSettings signInMode;
    private boolean emailVerified;
    private boolean eligibleForSignupOffer;
    private boolean newCustomer;
    private String otp;
    private boolean otpVerified;
    private Date lastVisitTime;
    private boolean optOutOfFaceIt = false;
    private String faceId;
    private String gender;
    private Date dateOfBirth;
    private Date anniversary;
    protected int loyalityPoints;
    protected Boolean optWhatsapp;

    private BigDecimal walletBalance;
    private Integer orderCount;

    protected SubscriptionInfoDetail subscriptionInfoDetail;

    protected Boolean hasSubscription;
    protected boolean profileCompleted;
}
