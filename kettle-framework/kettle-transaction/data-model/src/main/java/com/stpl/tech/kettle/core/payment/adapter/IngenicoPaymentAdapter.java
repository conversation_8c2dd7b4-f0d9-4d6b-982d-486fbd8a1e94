package com.stpl.tech.kettle.core.payment.adapter;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.payment.PaymentAdapter;
import com.stpl.tech.kettle.core.service.IngenicoPaymentService;
import com.stpl.tech.kettle.core.service.impl.IngenicoPaymentServiceImpl;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.ingenico.IngenicoQrResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class IngenicoPaymentAdapter extends PaymentAdapter<OrderPaymentRequest,
        IngenicoQrResponse> {

    @Autowired
    private IngenicoPaymentService ingenicoPaymentService;

    @Override
    public IngenicoQrResponse createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {
        return ingenicoPaymentService.createRequest(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification,Integer brandId) throws PaymentFailureException {
        String response = (String) object;
        return  ingenicoPaymentService.validateIngenicoCallback(response);
    }
}
