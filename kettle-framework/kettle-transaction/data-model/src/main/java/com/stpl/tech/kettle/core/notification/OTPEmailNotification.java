package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by rahul on 12-09-2022.
 */
public class OTPEmailNotification extends EmailNotification{

    private final OTPEmailTemplate template;
    private final EnvType envType;
    private final String customerEmail;
    private final String customerName;

    public OTPEmailNotification(OTPEmailTemplate template, EnvType envType,
                                String customerEmail, String name) {
        this.template = template;
        this.envType = envType;
        this.customerEmail = customerEmail;
        this.customerName = AppUtils.checkBlank(name)!=null ? name : "";
    }

    @Override
    public String[] getToEmails() {
        return new String[] { customerEmail };
    }

    @Override
    public String getFromEmail() {
        return TransactionUtils.isDev(getEnvironmentType())?"<EMAIL>":"<EMAIL>";
    }

    @Override
    public String subject() {
        return "Hi "+ customerName +", Your Chaayos One Time Password";
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
