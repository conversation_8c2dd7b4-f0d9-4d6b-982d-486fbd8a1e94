package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.NonWastageItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NonWastageItemRepository extends JpaRepository<NonWastageItem, Integer> {
    
    List<NonWastageItem> findByOrderId(Integer orderId);
    
    List<NonWastageItem> findByUnitId(Integer unitId);
    
    void deleteByOrderId(Integer orderId);
} 