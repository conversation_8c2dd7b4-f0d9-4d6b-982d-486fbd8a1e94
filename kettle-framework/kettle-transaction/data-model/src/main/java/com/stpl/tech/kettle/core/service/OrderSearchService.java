/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.core.data.vo.OrderStatusData;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.notification.NewOrderNotification;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.model.EmployeeMealData;
import com.stpl.tech.kettle.data.model.OrderEmailNotification;
import com.stpl.tech.kettle.domain.model.CashCard;
import com.stpl.tech.kettle.domain.model.DayWiseOrderConsumptionRequest;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDetailEventRequest;
import com.stpl.tech.kettle.domain.model.OrderDetailTrim;
import com.stpl.tech.kettle.domain.model.OrderNPS;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.kettle.domain.model.UnitMetadata;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.TemplateRenderingException;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface OrderSearchService {

	/**
	 * Return Order details for an order productId
	 *
	 * @param orderId
	 * @return
	 * @throws DataNotFoundException
	 */
	public Order getOrderDetail(int orderId) throws DataNotFoundException;

	/**
	 * Return Order details for an generated Order Id
	 *
	 * @param generatedOrderId
	 * @return
	 * @throws DataNotFoundException
	 */
	public Order getOrderDetail(String generatedOrderId) throws DataNotFoundException;

	/**
	 * @return
	 * @param cashCards
	 * @throws CardValidationException
	 */
	public List<CashCard> validateGiftCards(List<CashCard> cashCards) throws CardValidationException;

	/**
	 * @return
	 * @param order
	 * @throws CardValidationException
	 */
	public boolean validateGiftCardInOrder(Order order) throws CardValidationException;

	/**
	 * Get last order details for the business dte and the unit
	 *
	 * @param unitId
	 * @param businessDate
	 * @return
	 * @throws DataNotFoundException
	 */
	public int getLastOrderDetail(int unitId) throws DataNotFoundException;
	
	public Date getLastBusinessDate(int unitId);

	/**
	 * Get all orders for a duration
	 *
	 * @param unitId
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws DataNotFoundException
	 */
	public List<Order> getOrderDetails(int unitId, Date startTime, Date endTime) throws DataNotFoundException;

	/**
	 * Get all settlements for a duration
	 *
	 * @param unitId
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public List<Settlement> getSettlementDetails(int unitId, Date startTime, Date endTime);

	public List<OrderEmailNotification> getEmailEvents();

	public OrderInfo getOrderReceipt(int orderId, boolean include, String customerName)
			throws DataNotFoundException, TemplateRenderingException;

	public OrderInfo getOrderReceipt(String orderId) throws DataNotFoundException, TemplateRenderingException;

	public List<Order> getOrderDetailsForDay(int unitId) throws DataNotFoundException;

	public int getLastDayCloseOrderId(int unitId);

	public List<Order> getOrderDetails(int unitId, int startOrderId, int endOrderId, OrderFetchStrategy strategy)
			throws DataNotFoundException;

	public List<OrderDetailTrim> getOrderDetailsTrim(int unitId, int startOrderId, int endOrderId, OrderFetchStrategy strategy)
			throws DataNotFoundException;

	public List<Order> getOrderDetailsForDay(int unitId, int startOrderId, int endOrderId) throws DataNotFoundException;

	public List<Order> getOrderDetailsForDay(Date businessDate) throws DataNotFoundException;

	public List<OrderStatusData> getOrderStatusForDay(int unitId, List<OrderStatus> orderStatus,
													  List<UnitCategory> category, List<String> currentOrderStatus, String generatedOrderId) throws DataNotFoundException;

	public UnitMetadata getUnitMetadata(int unitId);

	public NewOrderNotification returnNewOrders(int unitId, String timeStamp, List<OrderInfo> orderCache);

	public List<EmployeeMealData> getEmployeeMealData(int employeeId);

	public int getCostEvent(int orderId);

	public List<Order> getEmployeeMealOrders(int userId);

	public boolean publishFinalOrderStatus(List<Integer> orderIdList);
	
	public Order getPartnerOrderDetail(String externalOrderId, int channerPartnerId) throws DataNotFoundException;

	public OrderStatus getPartnerOrderStatus(String externalOrderId, int channerPartnerId) throws DataNotFoundException;

	public List<OrderInfo> getOrderToPublish(int id, Date businessDate) throws DataNotFoundException, TemplateRenderingException;

	public List<Order> getOrderDetailsForDay(Date businessDate, List<Integer> unitIds);

	public List<Order> getOrderDetailsForDay(Date businessDate, int unitId, String source);

	public UnitClosure getLastDayClose(int unitId);

	public UnitClosure getUnitClosure(int closureId);

	public List<OrderInfo> getOrderDetailsAfterOrderId(int unitId, int startOrderId, OrderFetchStrategy strategy,
			List<Integer> skipOrders);

	public List<Integer> getSettleOrders(Integer startOrderId, int batchSize);

	List<Order> getCustomerOrders(int customerId, Date fromDate, int maxSize, List<Integer> filteredOrderIds);

	List<OrderNPS> getCustomerFeedbacks(int customerId, Date fromDate, int maxSize, List<Integer> filteredIds);

	public OrderNPS getFeedbackDetail(int surveyResponseId) throws DataNotFoundException;

	OrderNPS getCustomerFeedbacks(int orderId);

	public Integer getCustomerId(Integer orderId);

	public Integer getChannelPartnerId(Integer orderId);

	List<Order> getOrderDetails(int unitId, Date startTime, Date endTime, OrderFetchStrategy strategy)
			throws DataNotFoundException;

	public String getPartnerOrderDetail(String partnerSourceId, String issueType);
	public Map<String,Object> fetchPartnerOrderDetail(String partnerSourceId,String issueType);

	public boolean publishPartnerOrderDetail(OrderDetailEventRequest req);

	List<Order> getAllOrdersOfTheDays(DayWiseOrderConsumptionRequest dayWiseOrderConsumptionRequest);

	List<com.stpl.tech.kettle.data.model.OrderItem> getComboOrderItems(Integer comboOrderItemId);

	List<String> getOrdersForUnitFromStatus(Integer unitId, List<String> statusList);
}
