package com.stpl.tech.kettle.core.rules;

import com.stpl.tech.kettle.offer.model.DaySlot;
import com.stpl.tech.kettle.offer.model.OrderData;
import com.stpl.tech.kettle.offer.model.RuleData;

public class RuleEngine {

	public RuleData findRule(OrderData data) {
		DaySlot slot = data.getSlot();
		/*if (data.isNewCustomer()) {
			return getCase5RuleData(slot);
		} else {*/
			int totalFood = data.getFoodCount();
			int totalBeverage = data.getHotBeverageCount() + data.getColdBeverageCount();
			if (totalFood == 0 && totalBeverage > 0) {
				return getCase1RuleData(slot);
			} else if (totalFood > 0 && totalBeverage > 0 && totalBeverage == totalFood) {
				return getCase2RuleData(slot);
			} else if (totalBeverage > totalFood) {
				return getCase3RuleData(slot);
			} else if (totalBeverage < totalFood) {
				return getCase4RuleData(slot);
			} else {
				return RuleData.CATCH_ALL;
			}
		//}
	}

	private RuleData getCase1RuleData(DaySlot slot) {
		switch (slot) {
		case MORNING:
			return RuleData.M1;
		case EVENING:
			return RuleData.E1;
		case LUNCH:
			return RuleData.L1;
		case DINNER:
			return RuleData.D1;
		default:
			return RuleData.CATCH_ALL;
		}

	}

	private RuleData getCase2RuleData(DaySlot slot) {
		switch (slot) {
		case MORNING:
			return RuleData.M2;
		case EVENING:
			return RuleData.E2;
		case LUNCH:
			return RuleData.L2;
		case DINNER:
			return RuleData.D2;
		default:
			return RuleData.CATCH_ALL;
		}

	}

	private RuleData getCase3RuleData(DaySlot slot) {
		switch (slot) {
		case MORNING:
			return RuleData.M3;
		case EVENING:
			return RuleData.E3;
		case LUNCH:
			return RuleData.L3;
		case DINNER:
			return RuleData.D3;
		default:
			return RuleData.CATCH_ALL;
		}

	}

	private RuleData getCase4RuleData(DaySlot slot) {
		switch (slot) {
		case MORNING:
			return RuleData.M4;
		case EVENING:
			return RuleData.E4;
		case LUNCH:
			return RuleData.L4;
		case DINNER:
			return RuleData.D4;
		default:
			return RuleData.CATCH_ALL;
		}
	}

/*	private RuleData getCase5RuleData(DaySlot slot) {
		switch (slot) {
		case MORNING:
			return RuleData.M5;
		case EVENING:
			return RuleData.E5;
		case LUNCH:
			return RuleData.L5;
		case DINNER:
			return RuleData.D5;
		default:
			return RuleData.CATCH_ALL;
		}
	}*/
}
