package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.master.payment.model.AGS.AGSCreateRequest;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMStatus;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SStatus;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.util.EnvType;

import java.math.BigDecimal;
import java.util.Map;

public interface AGSPaymentService {

    AGSCreateRequest createRequest(EnvType type, OrderPaymentRequest order) throws PaymentFailureException;

    AGSPaymentCMStatus fetchPayment(EnvType type, String paymentId) throws PaymentFailureException;

    PaymentStatus getPaymentStatus(EnvType type, String paymentId, BigDecimal transactionAmount) throws PaymentFailureException;

    AGSPaymentCMStatus fetchOrder(EnvType type, String AGSOrderId) throws PaymentFailureException;

    OrderPayment refundRequest(EnvType type, OrderPayment request) throws PaymentFailureException;

    AGSPaymentCMResponse checkAGSPaymentS2SStatus(String externalOrderId);

    AGSPaymentS2SResponse updateAGSPaymentS2SStatus(AGSPaymentS2SStatus status);

    AGSCreateRequest createAGSRequest(OrderPaymentRequest order) throws PaymentFailureException;

    Map updateAGSPaymentCMStatus(AGSPaymentCMStatus response);
}
