/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.data.vo;

import java.math.BigDecimal;
import java.util.Date;

public class PartnerDataConsiderRequest {

    private Integer orderId;
    private Integer unitId;
    private Integer partnerId;
    private Date updateTime;
    private Date billServerTime;
    private Integer brandId;
    private BigDecimal amount;


    public PartnerDataConsiderRequest() {

    }

    public PartnerDataConsiderRequest(Integer orderId, Integer unitId, Integer partnerId, Date updateTime, Date billServerTime, Integer brandId, BigDecimal amount) {
        this.orderId = orderId;
        this.unitId = unitId;
        this.partnerId = partnerId;
        this.updateTime = updateTime;
        this.billServerTime = billServerTime;
        this.brandId = brandId;
        this.amount = amount;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getBillServerTime() {
        return billServerTime;
    }

    public void setBillServerTime(Date billServerTime) {
        this.billServerTime = billServerTime;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }


    @Override
    public String toString() {
        return "PartnerDataConsiderRequest{" +
                "orderId=" + orderId +
                ", unitId=" + unitId +
                ", partnerId=" + partnerId +
                ", updateTime=" + updateTime +
                ", billServerTime=" + billServerTime +
                ", brandId=" + brandId +
                ", amount=" + amount +
                '}';
    }
}
