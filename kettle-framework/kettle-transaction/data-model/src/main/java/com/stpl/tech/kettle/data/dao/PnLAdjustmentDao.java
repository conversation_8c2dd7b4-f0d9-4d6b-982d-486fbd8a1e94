package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.domain.model.PnlAdjustment;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.PnlAdjustmentStatus;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface PnLAdjustmentDao extends AbstractDao {


    List<PnlAdjustment> getAdjustments(PnlAdjustmentStatus status, List<Integer> unitIds,
                                       int startMonth, int startYear);


}
