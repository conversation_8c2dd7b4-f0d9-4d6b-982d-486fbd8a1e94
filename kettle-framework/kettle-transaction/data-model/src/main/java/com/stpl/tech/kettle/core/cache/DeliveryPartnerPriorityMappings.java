/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.cache;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;

@Repository
public class DeliveryPartnerPriorityMappings {
	private static final Logger LOG = LoggerFactory.getLogger(DeliveryPartnerPriorityMappings.class);
	private Map<Integer, UnitDeliverySelectionCache> prioritizedDeliveryPartnerCache = new HashMap<Integer, UnitDeliverySelectionCache>();

	private Map<Integer, Integer> retriesPerUnit = new HashMap<Integer, Integer>();

	public void setMappings(List<UnitToDeliveryPartnerMappings> unitToDeliveryMappings) {
		LOG.info("size of Unit To Delivery Mappings {}", unitToDeliveryMappings.size());

		unitToDeliveryMappings.forEach(new Consumer<UnitToDeliveryPartnerMappings>() {
			@Override
			public void accept(UnitToDeliveryPartnerMappings t) {
				Integer unitId = t.getUnitId();
				LOG.info("Inside setMappings for unit {} :::: {} ", unitId, unitToDeliveryMappings.size());
				setPrioritizedCacheForUnit(unitId, unitToDeliveryMappings);
			}
		});
	}

	private void setPrioritizedCacheForUnit(Integer unitId,
			List<UnitToDeliveryPartnerMappings> unitToDeliveryMappings) {

		List<UnitToDeliveryPartnerMappings> priorityMappings = unitToDeliveryMappings.stream()
				.filter(new Predicate<UnitToDeliveryPartnerMappings>() {
					@Override
					public boolean test(UnitToDeliveryPartnerMappings singleMapping) {
						return unitId.equals(singleMapping.getUnitId());
					}
				}).collect(Collectors.toList());
		LOG.info("size of filtered list of mappings :::: {}", priorityMappings.size());
		UnitDeliverySelectionCache selectionCache = new UnitDeliverySelectionCache();
		selectionCache.setUnitToDeliveryPartners(priorityMappings);
		LOG.info("setPrioritizedCacheForUnit size for unit productId {} is {}", unitId,
				selectionCache.getUnitToDeliveryPartners().size());

		getRetriesPerUnit().put(unitId, priorityMappings.size());
		prioritizedDeliveryPartnerCache.put(unitId, selectionCache);
		LOG.info("selectionCache size after insertion :::: {}", prioritizedDeliveryPartnerCache.get(unitId).toString());
	}

	public UnitDeliverySelectionCache getSelectionCacheForUnit(Integer unitId) {
		LOG.info("getSelectionCacheForUnit size unit {} :::: {}", unitId,
				prioritizedDeliveryPartnerCache.get(unitId).getUnitToDeliveryPartners().size());
		return prioritizedDeliveryPartnerCache.get(unitId);
	}

	public Map<Integer, Integer> getRetriesPerUnit() {
		return retriesPerUnit;
	}

	public Integer getRetriesForUnit(Integer unitId) {
		Integer retries = retriesPerUnit.get(unitId);
		LOG.info("retries per unit for unitId ::: {}", retries);
		return retries != null ? retries : 0;
	}

	public void setRetriesPerUnit(Map<Integer, Integer> retriesPerUnit) {
		this.retriesPerUnit = retriesPerUnit;
	}

	@Override
	public String toString() {
		return "DeliveryPartnerPriorityMappings{" +
				"retriesPerUnit=" + retriesPerUnit.size() +
				'}';
	}
}
