/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.core.ReportStatus;
import com.stpl.tech.kettle.core.data.vo.RevenueCertificateData;
import com.stpl.tech.kettle.data.model.ReportExecutionDetail;
import com.stpl.tech.kettle.domain.model.ExpenseUpdateEvent;
import com.stpl.tech.kettle.domain.model.ReportDef;
import com.stpl.tech.kettle.domain.model.RevenueCertificateMapping;
import com.stpl.tech.kettle.domain.model.UnitExpense;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.UnitAttributeMapping;
import com.stpl.tech.master.data.model.UnitDetail;

public interface ReportingDao {

	public ReportDef getReportDefinition(int reportId) throws DataNotFoundException;

	public ReportExecutionDetail createReportExecutionDetail(int unitId, int reportDefId) throws DataUpdationException;

	public void updateStatus(int executionDetailId, ReportStatus status) throws DataUpdationException;

	public ReportExecutionDetail getReportExecutionDetail(int id);

	public List<UnitExpense> addUnitExpenses(ExpenseUpdateEvent event, List<UnitExpense> unitExpenses);

	public List<UnitExpense> getUnitExpenseForWeek(int year, int week);

	public List<UnitExpense> getUnitExpenseForMonth(int year, int month);

	public ExpenseUpdateEvent addExpenseUpdateEvent(ExpenseUpdateEvent event);

	public ExpenseUpdateEvent getExpenseUpdateEvent(int id);

	public List<UnitExpense> getUnitExpensesForEvent(ExpenseUpdateEvent event);

	public int getLastOrderIdForBusinessDate(Date businessDate);

	public void updateExpenseResultData(int expenseUpdateEventId);

	public List<Integer> getUnitWithSales(int month, int year);

	public List<RevenueCertificateData> getRevenueCertificateData(int month, int year,Integer unitId,List<Integer> exclusionList);
	
}
