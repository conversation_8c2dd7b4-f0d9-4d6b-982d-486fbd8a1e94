package com.stpl.tech.kettle.core.config;

import com.corundumstudio.socketio.SocketIOServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 02-06-2017.
 */

@Configuration
public class SocketIOConfigurer {

    @Autowired
    private Environment env;

    @Bean(name="webSocketServer")
    public SocketIOServer webSocketServer() {
        com.corundumstudio.socketio.Configuration config = new com.corundumstudio.socketio.Configuration();
        String host = env.getProperty("env.host.ip","127.0.0.1");
        Integer port = Integer.valueOf(env.getProperty("socket.io.port","9092"));
        config.setHostname(host);
        config.setPort(port);
        config.setAllowCustomRequests(true);
        final SocketIOServer server = new SocketIOServer(config);
        return server;
    }
}
