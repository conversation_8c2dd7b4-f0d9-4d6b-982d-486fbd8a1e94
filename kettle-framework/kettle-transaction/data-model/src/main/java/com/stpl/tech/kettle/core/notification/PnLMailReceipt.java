/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.util.notification.AbstractTemplate;

public class PnLMailReceipt extends AbstractTemplate {

	private List<UnitExpenditureDetail> pnlDetails;
	private String basePath;
	private Date pnlDate;

	private Map<String, Object> data = new HashMap<String, Object>();

	public PnLMailReceipt(List<UnitExpenditureDetail> pnlDetails, Date pnlDate, String basePath) {
		this.pnlDetails = pnlDetails;
		this.basePath = basePath;
		this.pnlDate = pnlDate;
	}

	public String getTemplatePath() {
		return "template/PNLSummary.html";
	}

	public Map<String, Object> getData() {
		data.put("pnlDetails", pnlDetails);
		data.put("pnlDate", pnlDate);
		return data;
	}

	@Override
	public String getFilepath() {
		return basePath + "/PNLReport/" + new SimpleDateFormat("MMddyyyy").format(pnlDate) + ".html";
	}

	public Date getPnlDate() {
		return pnlDate;
	}

	public void setPnlDate(Date pnlDate) {
		this.pnlDate = pnlDate;
	}

}
