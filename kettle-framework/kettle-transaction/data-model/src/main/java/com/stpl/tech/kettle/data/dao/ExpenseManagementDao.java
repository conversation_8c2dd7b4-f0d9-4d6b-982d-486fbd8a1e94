package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.core.data.budget.vo.AdjustmentAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ElectricityStaticData.ElectricityBillType;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.ExpenseDetailData;
import com.stpl.tech.kettle.data.model.MeterDetailsData;
import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetExceededData;
import com.stpl.tech.kettle.domain.model.MeterDetail;
import com.stpl.tech.master.data.dao.AbstractDao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ExpenseManagementDao extends AbstractDao {

	public List<ExpenseDetailData> getExpenseDetail(Integer unitId, String expenseCategory, String expenseType,
			Date startDate, Date endDate, String status);

	public List<Object[]> getLastMeterReading(int unitId);

	public List<MeterDetailsData> getMeterDetailList(int unitId, Date currentDate, String entryType,
			String calculationIndex);

	public void updateCalculationIndex(int unitId, Date currentDate, String calculationIndex);

	public List<ExpenseDetailData> getPnlAccountableExpenses(int unitId, int month, int year);

	public List<ExpenseDetailData> getUnAccountedPnlExpenses(int unitId, int month, int year);

	public List<PnlAdjustmentDetail> getAdjustmentAggregate(int unitId, int month, int year, Date businessDate);

	public List<PnlAdjustmentDetail> getMTDAdjustmentAggregate(int unitId, int month, int year);
	/**
	 * @param unitId
	 * @param month
	 * @param year
	 * @param category
	 * @return
	 */
	public BigDecimal getAllExpenses(int unitId, int month, int year, String category);

    List<VoucherData> getUnAccountedPnlVoucherExpenses(Integer unitId);

    /**
	 * @param unitId
	 * @param pnlDetailId
	 * @param month
	 * @param year
	 */
	public void setPnLDetailId(int unitId, int pnlDetailId, int month, int year);

	/**
	 * @param unitId
	 * @param month
	 * @param year
	 * @return
	 */
	public Map<ElectricityBillType, Map<Integer, Integer>> getMeterReading(int unitId, int month, int year);

	public List<UnitBudgetExceededData> getBudgetExceededDetails(int unitId, String notificationType, Date tillDate);

	public List<MeterDetailsData> getLastMeterDetailList(MeterDetail detail);

	public void unMarkPnlAccountableExpenses(int pnlDetailId);

	public List<VoucherData> getAccountedPnlVoucherExpenses(Integer unitId, int month, int year);

    List<MeterDetailsData> getAllMeterDetailList(int unitId);

    boolean resetAllMeterReadingForUnit(int unitId, int userId);
}
