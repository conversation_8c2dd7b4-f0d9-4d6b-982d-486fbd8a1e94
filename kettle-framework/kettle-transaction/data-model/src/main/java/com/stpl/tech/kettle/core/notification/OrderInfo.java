
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.ReceiptType;
import com.stpl.tech.kettle.data.model.OrderDelayReason;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.NotificationType;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDelayReasonData;
import com.stpl.tech.kettle.domain.model.OrderInvoice;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.PartnerOrderRiderStatesDetailData;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.PrintType;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class OrderInfo implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 8338090534694576702L;
    private EnvType env;
    private Order order;
    private Customer customer;
    private IdCodeName deliveryPartner;
    private IdCodeName channelPartner;
    private List<String> receipts;
    private List<String> additionalReceipts;
    private List<String> newCards;
    private Unit unit;
    private DeliveryResponse deliveryDetails;

    private boolean billIncludedInReceipts;
    private PrintType printType;
    private String feedbackUrl;
    private String qrCode;
    private Map<ReceiptType,String> androidReceipts;
    private Brand brand;
    private NextOffer nextOffer;
    private NextOffer nextDeliveryOffer;
    private OrderInvoice orderInvoice;
    private OrderNotification orderNotification;

    private PartnerOrderRiderStatesDetailData partnerOrderRiderStates = new PartnerOrderRiderStatesDetailData();

    private OrderDelayReasonData orderDelayReason = new OrderDelayReasonData();


    private Date lastOrderStatusEventTime;

    private Map<NotificationType,Boolean> communicationType;

    private String partnerCustomerId;

    public OrderInfo() {

    }

    public OrderInfo(EnvType env, Order order, Customer customer, Unit unit, IdCodeName deliveryPartner,
                     IdCodeName channelPartner) {
        super();
        this.order = order;
        this.customer = customer;
        this.env = env;
        this.deliveryPartner = deliveryPartner;
        this.channelPartner = channelPartner;
        this.unit = unit;
    }

    public OrderInfo(EnvType env, Order order, Customer customer, IdCodeName deliveryPartner, IdCodeName channelPartner,
			List<String> receipts, Unit unit, DeliveryResponse deliveryDetails, PrintType printType) {
		super();
		this.env = env;
		this.order = order;
		this.customer = customer;
		this.deliveryPartner = deliveryPartner;
		this.channelPartner = channelPartner;
		this.receipts = receipts;
		this.unit = unit;
		this.deliveryDetails = deliveryDetails;
		this.printType = printType;
	}

    public OrderInfo(EnvType env, Order order, Customer customer, IdCodeName deliveryPartner, IdCodeName channelPartner,
                     Map<ReceiptType,String> receipts, Unit unit, DeliveryResponse deliveryDetails, PrintType printType) {
        super();
        this.env = env;
        this.order = order;
        this.customer = customer;
        this.deliveryPartner = deliveryPartner;
        this.channelPartner = channelPartner;
        this.androidReceipts = receipts;
        this.unit = unit;
        this.deliveryDetails = deliveryDetails;
        this.printType = printType;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public EnvType getEnv() {
        return env;
    }

    public void setEnv(EnvType env) {
        this.env = env;
    }

    public IdCodeName getDeliveryPartner() {
        return deliveryPartner;
    }

    public void setDeliveryPartner(IdCodeName deliveryPartner) {
        this.deliveryPartner = deliveryPartner;
    }

    public IdCodeName getChannelPartner() {
        return channelPartner;
    }

    public void setChannelPartner(IdCodeName channelPartner) {
        this.channelPartner = channelPartner;
    }

    public List<String> getReceipts() {
        return receipts;
    }

    public void setReceipts(List<String> receipts) {
        this.receipts = receipts;
    }

    public List<String> getAdditionalReceipts() {
    	if(additionalReceipts == null ) {
    		additionalReceipts = new ArrayList<>();
    	}
		return additionalReceipts;
	}

	public void setAdditionalReceipts(List<String> additionalReceipts) {
		this.additionalReceipts = additionalReceipts;
	}

	@Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OrderInfo orderInfo = (OrderInfo) o;
        return Objects.equals(getOrder().getOrderId(), orderInfo.getOrder().getOrderId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getOrder().getOrderId());
    }

    public Unit getUnit() {
        return unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public DeliveryResponse getDeliveryDetails() {
        return deliveryDetails;
    }

    public void setDeliveryDetails(DeliveryResponse deliveryDetails) {
        this.deliveryDetails = deliveryDetails;
    }

	public boolean isBillIncludedInReceipts() {
		return billIncludedInReceipts;
	}

	public void setBillIncludedInReceipts(boolean billIncludedInRecipts) {
		this.billIncludedInReceipts = billIncludedInRecipts;
	}

	public List<String> getNewCards() {
		return newCards;
	}

	public void setNewCards(List<String> newCards) {
		this.newCards = newCards;
	}

	public PrintType getPrintType() {
		return printType;
	}

	public void setPrintType(PrintType printType) {
		this.printType = printType;
	}

    public Map<ReceiptType, String> getAndroidReceipts() {
    	if(androidReceipts == null) {
    		androidReceipts = new HashMap<>();
    	}
        return androidReceipts;
    }

    public void setAndroidReceipts(Map<ReceiptType, String> androidReceipts) {
        this.androidReceipts = androidReceipts;
    }

	public String getFeedbackUrl() {
		return feedbackUrl;
	}

	public void setFeedbackUrl(String feedbackUrl) {
		this.feedbackUrl = feedbackUrl;
	}

	public String getQrCode() {
		return qrCode;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

    public Brand getBrand() {
        return brand;
    }

    public void setBrand(Brand brand) {
        this.brand = brand;
    }

	public NextOffer getNextOffer() {
		return nextOffer;
	}

	public void setNextOffer(NextOffer nextOffer) {
		this.nextOffer = nextOffer;
	}

    public NextOffer getNextDeliveryOffer() {
        return nextDeliveryOffer;
    }

    public void setNextDeliveryOffer(NextOffer nextDeliveryOffer) {
        this.nextDeliveryOffer = nextDeliveryOffer;
    }

    public OrderInvoice getOrderInvoice() {
        return orderInvoice;
    }

    public void setOrderInvoice(OrderInvoice orderInvoice) {
        this.orderInvoice = orderInvoice;
    }

    public void setOrderNotification(OrderNotification orderNotification) {
        this.orderNotification = orderNotification;
    }

    public OrderNotification getOrderNotification() {
        return orderNotification;
    }

    public PartnerOrderRiderStatesDetailData getPartnerOrderRiderStates() {
        return partnerOrderRiderStates;
    }

    public void setPartnerOrderRiderStates(PartnerOrderRiderStatesDetailData partnerOrderRiderStates) {
        this.partnerOrderRiderStates = partnerOrderRiderStates;
    }

    public Date getLastOrderStatusEventTime() {
        return lastOrderStatusEventTime;
    }

    public void setLastOrderStatusEventTime(Date lastOrderStatusEventTime) {
        this.lastOrderStatusEventTime = lastOrderStatusEventTime;
    }

    public OrderDelayReasonData getOrderDelayReason() {
        return orderDelayReason;
    }

    public void setOrderDelayReason(OrderDelayReasonData orderDelayReason) {
        this.orderDelayReason = orderDelayReason;
    }

    public Map<NotificationType, Boolean> getCommunicationType() {
        return communicationType;
    }

    public void setCommunicationType(Map<NotificationType, Boolean> communicationType) {
        this.communicationType = communicationType;
    }

    public String getPartnerCustomerId() {
        return partnerCustomerId;
    }

    public void setPartnerCustomerId(String partnerCustomerId) {
        this.partnerCustomerId = partnerCustomerId;
    }
}
