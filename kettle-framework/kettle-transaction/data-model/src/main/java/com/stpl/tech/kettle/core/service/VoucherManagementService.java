package com.stpl.tech.kettle.core.service;

import java.util.List;

import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.domain.model.Order;

public interface VoucherManagementService {

	public List<String> verifyVoucher(Order order, boolean consume)
			throws CardValidationException;
	
	public void updateVoucher(String voucherCode, String cardNumber, String partnerCode);
	
	public boolean isGyftrCard(String cardType);
	
}
