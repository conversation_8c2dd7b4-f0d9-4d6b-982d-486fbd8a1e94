package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 02-12-2017.
 */
public class OTPEmailTemplate extends AbstractTemplate{
    private final String basePath;
    Map<String, Object> data = new HashMap<>();
    private final String token;
    private String templatePath;

    public OTPEmailTemplate(String basePath, String token, String templatePath) {
        this.basePath = basePath;
        this.token = token;
        this.templatePath = templatePath;
    }

    // "template/OTPEmailTemplate.html"

    @Override
    public String getTemplatePath() {
        return templatePath;
    }

    public void setTemplatePath(String templatePath) {
        this.templatePath = templatePath;
    }

    @Override
    public String getFilepath() {
        return basePath +"/otp/OTPEmail-" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        data.put("token",token);
        return data;
    }
}
