/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.CalculationType;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.CalculationStatus;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.DirectCost;
import com.stpl.tech.kettle.core.data.budget.vo.ElectricityAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.InventoryAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.RentAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.RevenueData;
import com.stpl.tech.kettle.core.data.budget.vo.SalesCommissionData;
import com.stpl.tech.kettle.core.data.budget.vo.WastageAggregate;
import com.stpl.tech.kettle.core.data.vo.PnlRecord;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.UnitBudgetoryDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureAggregateDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.domain.model.UnitExpenditure;
import com.stpl.tech.master.core.exception.DataUpdationException;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface UnitBudgetService {

	public Map<Integer, RevenueData> getRevenueData(Date businessDate, List<Integer> unitIds);

	public Map<Integer, SalesCommissionData> getSalesCommissionData(Date businessDate, List<Integer> unitIds);

	public Map<Integer, InventoryAggregate> getInventoryAggregate(Date businessDate, List<Integer> unitIds);

	public Map<Integer, WastageAggregate> getWastageAggregate(Date businessDate, List<Integer> unitIds);

	public Map<Integer, ConsumablesAggregate> getConsumablesAggregate(Date businessDate, List<Integer> unitIds);

	public Map<Integer, ExpenseAggregate> getExpenseAggregate(Date businessDate, List<Integer> unitIds);

	public Map<Integer, ElectricityAggregate> getElectricityAggregate(Date businessDate, List<Integer> unitIds);

	public Map<Integer, DirectCost> getDirectCost(Date businessDate, List<Integer> unitIds);

	public Map<Integer, RentAggregate> getRentAggregate(Date businessDate, List<Integer> unitIds);

	public BigDecimal getCreditCardPercentageForCurrentMonth(int unitId, Date businessDate);

	public int savePnL(BudgetDetail budgetDetail);
	
	public int savePnL(UnitExpenditureDetail expenseDetail);

	public int getPnlDetailId(int unitId, int dayClosureId);

	public UnitBudgetoryDetail getUnitBudgetDetail(int unitId, int month, int year);
	
	public List<UnitExpenditureDetail> getUnitExpenditureDetail(Date businessDate, CalculationStatus status, CalculationType calculation);

	public List<UnitExpenditureDetail> getUnitExpenditureDetail(int unitId, Date businessDate, CalculationType calculation);

	public List<UnitExpenditureDetail> getAllUnitExpenditureDetail(Date businessDate, CalculationType type);

	public UnitExpenditureDetail getLatestUnitExpenditureDetail(int unitId, int month, int year, CalculationStatus status, CalculationType calculation);

	public void saveKettleCalculatedExpenses(int detailId, BudgetDetail detail);

    void updatePnLInVoucherData(int detailId, Date businessDate, List<VoucherData> voucherDataList) throws DataUpdationException;

    public List<UnitExpenditure> getMTDPnlForUnit(Integer unitId, Date businessDate);

    public List<PnlRecord> getPnlRepresentationForUnit(Integer unitId, Date businessDate) throws DataUpdationException;

	public List<UnitExpenditureDetail> getAllUnitExpenditureDetail(int unitId, Date businessDate);

	public List<UnitExpenditureDetail> getAllMTDPnlForUnitForMonth(Integer unitId, Date date);

	public void createFinalizedEntry(Integer i, Date businessDate);

	public void createClosedPnlEntry(Integer i, Date businessDate);

	public List<Integer> getUnitsWithFinalizedEntries(Date businessDate);

	public List<Integer> getUnitsWithClosedEntries(Date businessDate);

	public List<UnitExpenditureDetail> getFinalizedPnlForUnitForMonth(Integer unitId, Integer month, Integer year);

	public List<PnlRecord> getFinalizedPnlAggregateForUnitForMonth(Integer unitId, Integer month, Integer year) throws DataUpdationException;

	UnitExpenditureAggregateDetail checkFinalizedPnlAggregateForUnitForMonth(Integer unitId, Integer month, Integer year);

	public void updateGiftCardOfferData(int detailId, int unitId, Date businessDate);

	List<PnlRecord> getPnlMtdAggregateViewData(Integer unitId, Date businessDate) throws DataUpdationException;

	DirectCost setDirectCostData(UnitBudgetoryDetail b, Integer day, Integer month, Integer year);

	public void markAsCompleted(Integer detailId);

	void markAsCancelled(int pnlDetailId);

	public void markAsBudgetNotAvailable(Integer detailId);

	public void markAsFailed(Integer detailId);

    List<UnitExpenditureAggregateDetail> getAllUnitExpenditureAggregateDetail(Integer unitId, Date businessDate);

	void markAggregateAsCancelled(int detailId);

	public void cancelAllPnLs(Date minimumDate, Set<Integer> keySet);

	public boolean savePnL(Collection<BudgetDetail> values);
}
