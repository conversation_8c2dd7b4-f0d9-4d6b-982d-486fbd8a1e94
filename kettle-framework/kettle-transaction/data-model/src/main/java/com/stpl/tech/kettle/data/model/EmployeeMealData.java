/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderItem generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "EMPLOYEE_MEAL_DATA")
public class EmployeeMealData implements java.io.Serializable {

	private Integer employeeMealDataId;
	private OrderDetail orderDetail;
	private int productId;
	private int unitId;
	private int employeeId;
	private int productTypeId;
	private int quantity;
	private Date businessDate;
	private String dimension;

	public EmployeeMealData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EMPLOYEE_MEAL_DATA_ID", unique = true, nullable = false)
	public Integer getEmployeeMealDataId() {
		return this.employeeMealDataId;
	}

	public void setEmployeeMealDataId(Integer employeeMealDataId) {
		this.employeeMealDataId = employeeMealDataId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	public OrderDetail getOrderDetail() {
		return this.orderDetail;
	}

	public void setOrderDetail(OrderDetail orderDetail) {
		this.orderDetail = orderDetail;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return this.productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "QUANTITY", nullable = false)
	public int getQuantity() {
		return this.quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	@Column(name = "DIMENSION_CODE", length = 10)
	public String getDimension() {
		return this.dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}
	
	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}
	
	@Column(name = "EMPLOYEE_ID", nullable = false)
	public int getEmployeeId() {
		return employeeId;
	}

	
	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}
	
	@Column(name = "PRODUCT_TYPE_ID", nullable = false)
	public int getProductTypeId() {
		return productTypeId;
	}

	public void setProductTypeId(int productTypeId) {
		this.productTypeId = productTypeId;
	}
	
	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE", nullable = false, length = 10)
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	
}
