/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification.receipt;


import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;

public class QRRawPrintReceipt extends OrderRawPrintReceipt {

	private String header;
	private String qr;

	public QRRawPrintReceipt(Unit unit, MasterDataCache metadataCache, OrderInfo detail, String basePath, String header, String qrData, EnvironmentProperties env) throws DataNotFoundException {
		super(unit, detail, basePath, null,env);
		this.header = header;
		this.qr = qrData;
	}

	@Override
	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getOrder().getOrderId() + "/OrderQRPrint-" + "-"
				+ getOrder().getOrderId() + ".html";
	}

	@Override
	public StringBuilder processData() {
		reset();
		// setDoubleFont();
		// setTallFont();
		left(" ");
		left(" ");
		left(" ");
		center(bold(doubleFont(header)));
		left(" ");
		left(" ");
		left(" ");
		qrAppend(qr(qr));

		left(" ");
		left(" ");
		left(" ");
		separator();
		cut();

		return getSb();
	}

}
