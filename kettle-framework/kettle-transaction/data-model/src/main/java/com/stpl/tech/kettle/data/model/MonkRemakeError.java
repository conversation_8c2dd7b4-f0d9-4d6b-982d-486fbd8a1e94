package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "MONK_REMAKE_ERROR")
public class MonkRemakeError {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true)
    private Integer id;

    @Column(name="ORDER_ID")
    private Integer orderId;

    @Column(name ="TASK_ID")
    private Integer taskId;

    @Column(name="UNIT_ID")
    private Integer unitId;

    @Column(name="MONK_NAME")
    private String monkName;

    @Column(name="S_CODE")
    private Integer sCode;

    @Column(name="CODE_REASON")
    private String codeReason;



}
