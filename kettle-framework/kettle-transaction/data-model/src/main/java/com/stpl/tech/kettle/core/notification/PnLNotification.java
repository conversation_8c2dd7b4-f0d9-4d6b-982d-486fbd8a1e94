/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;
import com.stpl.tech.util.AppUtils;

public class PnLNotification extends EmailNotification {

	private PnLMailReceipt receipt;
	private EnvironmentProperties props;

	public PnLNotification(PnLMailReceipt receipt, EnvironmentProperties props) {
		this.receipt = receipt;
		this.props = props;
	}

	@Override
	public String[] getToEmails() {
		return TransactionUtils.isDev(props.getEnvironmentType()) ? new String[] { "<EMAIL>" }
				: new String[] { "<EMAIL>", "<EMAIL>" };
	}

	@Override
	public String getFromEmail() {
		return AppUtils.getFormattedEmail(TransactionUtils.getPrefix(props.getEnvironmentType()) + "Kettle Automation",
				"<EMAIL>");
	}

	@Override
	public String subject() {
		return "PnL Summary Notification for " + AppUtils.getFormattedDate(receipt.getPnlDate());
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return receipt.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return props.getEnvironmentType();
	}

}
