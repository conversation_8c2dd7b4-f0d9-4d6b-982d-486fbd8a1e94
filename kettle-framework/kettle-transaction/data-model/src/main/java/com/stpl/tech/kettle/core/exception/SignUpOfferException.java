package com.stpl.tech.kettle.core.exception;

public class SignUpOfferException extends Exception{

    public SignUpOfferException() {
        super();
    }

    public SignUpOfferException(String message) {
        super(message);
    }

    public SignUpOfferException(String message, Throwable cause) {
        super(message, cause);
    }

    public SignUpOfferException(Throwable cause) {
        super(cause);
    }

    protected SignUpOfferException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
