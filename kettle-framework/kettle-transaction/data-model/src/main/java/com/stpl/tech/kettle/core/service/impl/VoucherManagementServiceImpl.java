package com.stpl.tech.kettle.core.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.service.PartnerCardService;
import com.stpl.tech.kettle.core.service.VoucherManagementService;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.external.cache.EnvironmentPropertiesCache;

@Service
public class VoucherManagementServiceImpl implements VoucherManagementService {

	@Autowired
	@Qualifier("GyftrService")
	private PartnerCardService gyftrService;

	@Autowired
	private EnvironmentPropertiesCache propertiesCache;

	@Override
	@Transactional(noRollbackFor = CardValidationException.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<String> verifyVoucher(Order order, boolean consume) throws CardValidationException {
		List<String> voucherList = null;
		if (TransactionUtils.isGyftrCard(propertiesCache.isGyftrActive(), order.getOrders().get(0).getCardType())) {
			voucherList = gyftrService.verifyVoucher(order, false);
			if (voucherList == null) {
				throw new CardValidationException("Gyftr vouchers are invalid.Please fill correct Voucher Code");
			}
		}
		return voucherList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateVoucher(String voucherCode, String cardNumber, String partnerCode) {
		if (TransactionUtils.isGyftrCard(propertiesCache.isGyftrActive(), partnerCode)) {
			gyftrService.updateVoucher(voucherCode, cardNumber, partnerCode);
		}
	}

	@Override
	public boolean isGyftrCard(String cardType) {
		return TransactionUtils.isGyftrCard(propertiesCache.isGyftrActive(), cardType);
	}

}
