/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderSettlement generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ORDER_STATUS_EVENT"

)
public class OrderStatusEvent implements java.io.Serializable {

	private Integer orderStatusId;
	private int orderId;
	private String fromStatus;
	private String toStatus;
	private String reasonText;
	private int generatedBy;
	private int approvedBy;
	private Date startTime;
	private Date updateTime;
	private String transitionStatus;
	private String errorTrace;

	public OrderStatusEvent() {
	}

	public OrderStatusEvent(int orderId, String fromStatus, String toStatus, String reasonText, int generatedBy,
			int approvedBy, Date startTime, Date updateTime, String transitionStatus, String errorTrace) {
		super();
		this.orderId = orderId;
		this.fromStatus = fromStatus;
		this.toStatus = toStatus;
		this.reasonText = reasonText;
		this.generatedBy = generatedBy;
		this.approvedBy = approvedBy;
		this.startTime = startTime;
		this.updateTime = updateTime;
		this.transitionStatus = transitionStatus;
		this.errorTrace = errorTrace;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "ORDER_STATUS_ID", unique = true, nullable = false)
	public Integer getOrderStatusId() {
		return this.orderStatusId;
	}

	public void setOrderStatusId(Integer orderStatusId) {
		this.orderStatusId = orderStatusId;
	}

	@Column(name = "ORDER_ID", nullable = false)
	public int getOrderId() {
		return this.orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	@Column(name = "GENERATED_BY", nullable = false)
	public int getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(int employeeId) {
		this.generatedBy = employeeId;
	}

	@Column(name = "APPROVED_BY", nullable = false)
	public int getApprovedBy() {
		return approvedBy;
	}

	public void setApprovedBy(int approvedBy) {
		this.approvedBy = approvedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TIME", nullable = false, length = 19)
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "START_TIME", nullable = false, length = 19)
	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	@Column(name = "FROM_STATUS", nullable = false, length = 30)
	public String getFromStatus() {
		return fromStatus;
	}

	public void setFromStatus(String fromStatus) {
		this.fromStatus = fromStatus;
	}

	@Column(name = "TO_STATUS", nullable = false, length = 30)
	public String getToStatus() {
		return toStatus;
	}

	public void setToStatus(String toStatus) {
		this.toStatus = toStatus;
	}

	@Column(name = "REASON_TEXT", nullable = false, length = 100)
	public String getReasonText() {
		return reasonText;
	}

	public void setReasonText(String reasonText) {
		this.reasonText = reasonText;
	}

	@Column(name = "TRANSITION_STATUS", nullable = false, length = 30)
	public String getTransitionStatus() {
		return transitionStatus;
	}

	public void setTransitionStatus(String transitionStatus) {
		this.transitionStatus = transitionStatus;
	}

	@Column(name = "ERROR_TRACE", nullable = true, length = 5000)
	public String getErrorTrace() {
		return errorTrace;
	}

	@Override
	public String toString() {
		return "OrderStatusEvent [orderStatusId=" + orderStatusId + ", orderId=" + orderId + ", fromStatus="
				+ fromStatus + ", toStatus=" + toStatus + ", reasonText=" + reasonText + ", generatedBy=" + generatedBy
				+ ", approvedBy=" + approvedBy + ", updateTime=" + updateTime + ", transitionStatus=" + transitionStatus
				+ ", errorTrace=" + errorTrace + "]";
	}

	public void setErrorTrace(String errorTrace) {
		this.errorTrace = errorTrace;
	}

}
