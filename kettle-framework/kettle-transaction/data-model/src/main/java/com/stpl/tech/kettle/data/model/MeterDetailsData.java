/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "METER_READING_DETAILS_DATA")
public class MeterDetailsData implements java.io.Serializable {

	private static final long serialVersionUID = 1L;

	private int id;
	private int unitId;
	private String billType;
	private Integer meterNo;
	private Integer currentUnit;
	private String entryType;
	private String calculationIndex;
	private Integer createdBy;
	private Date createdOn;
	private String status;
	private Integer cancelledBy;
	private Date cancelledOn;
	private Date businessDate;
	private Integer updatedBy;
	private Date updatedOn;
	private Integer resetBy;
	private Date resetOn;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "DATA_ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "BILL_TYPE", nullable = false)
	public String getBillType() {
		return billType;
	}

	public void setBillType(String billType) {
		this.billType = billType;
	}

	@Column(name = "METER_NO", nullable = false)
	public Integer getMeterNo() {
		return meterNo;
	}

	public void setMeterNo(Integer meterNo) {
		this.meterNo = meterNo;
	}

	@Column(name = "CURRENT_UNIT", nullable = false)
	public Integer getCurrentUnit() {
		return currentUnit;
	}

	public void setCurrentUnit(Integer currentUnit) {
		this.currentUnit = currentUnit;
	}

	@Column(name = "ENTRY_TYPE", nullable = false)
	public String getEntryType() {
		return entryType;
	}

	public void setEntryType(String entryType) {
		this.entryType = entryType;
	}

	@Column(name = "CALCULATION_INDEX", nullable = false)
	public String getCalculationIndex() {
		return calculationIndex;
	}

	public void setCalculationIndex(String calculationIndex) {
		this.calculationIndex = calculationIndex;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_ON", nullable = true, length = 19)
	public Date getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(Date createdOn) {
		this.createdOn = createdOn;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE", nullable = true, length = 10)
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "CANCELLED_BY", nullable = true)
	public Integer getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(Integer cancelledBy) {
		this.cancelledBy = cancelledBy;
	}

	@Column(name = "CANCELLED_ON", nullable = true)
	public Date getCancelledOn() {
		return cancelledOn;
	}

	public void setCancelledOn(Date cancelledOn) {
		this.cancelledOn = cancelledOn;
	}

	@Column(name = "UPDATED_BY", nullable = true)
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Column(name = "UPDATED_ON", nullable = true)
	public Date getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(Date updatedOn) {
		this.updatedOn = updatedOn;
	}

	@Column(name = "RESET_BY", nullable = true)
	public Integer getResetBy() {
		return resetBy;
	}

	public void setResetBy(Integer resetBy) {
		this.resetBy = resetBy;
	}

	@Column(name = "RESET_ON", nullable = true)
	public Date getResetOn() {
		return resetOn;
	}

	public void setResetOn(Date resetOn) {
		this.resetOn = resetOn;
	}
}
