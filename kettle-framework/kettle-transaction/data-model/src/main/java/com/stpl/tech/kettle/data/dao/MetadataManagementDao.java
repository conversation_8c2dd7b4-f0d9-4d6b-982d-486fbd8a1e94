/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import java.util.List;

import com.stpl.tech.kettle.data.model.ManualBillBookData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.domain.model.ManualBillBook;
import com.stpl.tech.master.domain.model.PartnerDetail;
import com.stpl.tech.master.domain.model.Unit;

public interface MetadataManagementDao extends AbstractDao {

	public List<PartnerDetail> getAllDeliveyPartnersForUnit(int unitId);

	public boolean updateDeliveryPartnerForUnit(Unit unit);
	
	public List<ManualBillBookData> getManualBillBookDetail(int unitId,boolean getAll);
	
	public List<ManualBillBookData> validateManualBillBookDetail(ManualBillBook manualBillBook, int stateId);
	
	public List<ManualBillBookData> validateManualBillBookNo(int billBookNo,int unitId);

	/**
	 * @param billBookId
	 * @return
	 */
	public List<OrderDetail> getAllOrdersForBillBook(int billBookId);
	
	public ManualBillBookData  getBillBook(int transferOrderId);
	
}
