/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.cache;

import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.delivery.strategy.UnitDeliveryMappingComparator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.PriorityQueue;

public class UnitDeliverySelectionCache {
	private static final Logger LOG = LoggerFactory.getLogger(UnitDeliverySelectionCache.class);
	private PriorityQueue<UnitToDeliveryPartnerMappings> currentQueue = new PriorityQueue<>(
			new UnitDeliveryMappingComparator());
	private List<UnitToDeliveryPartnerMappings> unitToDeliveryPartners = new ArrayList<UnitToDeliveryPartnerMappings>();

	public void refreshCurrentCache() {
		this.currentQueue.addAll(this.unitToDeliveryPartners);
		LOG.info("Inside addtoCurrentCache and its size :::: {}", currentQueue.size());
	}

	public Integer getNextPartnerFromCache() {
		UnitToDeliveryPartnerMappings nextPartner = this.currentQueue.peek();
		if (nextPartner == null) {
			refreshCurrentCache();
		}
		nextPartner = this.currentQueue.poll();
		LOG.info("returning partner ID ::: {}", nextPartner);
		return nextPartner.getDeliveryPartner().getPartnerId();
	}


	public List<UnitToDeliveryPartnerMappings> getUnitToDeliveryPartners() {
		return unitToDeliveryPartners;
	}

	public void setUnitToDeliveryPartners(List<UnitToDeliveryPartnerMappings> unitToDeliveryPartners) {
		this.unitToDeliveryPartners = unitToDeliveryPartners;
	}

	/*public PriorityQueue<UnitToDeliveryPartnerMappings> getCurrentQueue() {
		return currentQueue;
	}

	public void setCurrentQueue(PriorityQueue<UnitToDeliveryPartnerMappings> currentQueue) {
		this.currentQueue = currentQueue;
	}
*/
	@Override
	public String toString() {
		return "UnitDeliverySelectionCache [currentQueue=" + currentQueue.size() + ", unitToDeliveryPartners="
				+ unitToDeliveryPartners.size() + "]";
	}

}
