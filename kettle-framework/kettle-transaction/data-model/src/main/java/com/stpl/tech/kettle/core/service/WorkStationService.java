/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.domain.model.AssemblyLog;
import com.stpl.tech.kettle.domain.model.AssemblyTAT;
import com.stpl.tech.kettle.domain.model.MonkCalibrationEvent;
import com.stpl.tech.kettle.domain.model.MonkCalibrationStatus;
import com.stpl.tech.kettle.domain.model.MonkCalibrationTime;
import com.stpl.tech.kettle.domain.model.MonkLog;
import com.stpl.tech.kettle.domain.model.WorkstationLog;
import com.stpl.tech.kettle.domain.model.WorkstationManualTask;

import java.util.List;

/**
 * <AUTHOR> Singh
 * @date 05-Apr-2016 10:43:22 pm
 *
 */
public interface WorkStationService {

	int addWorkStationLog(List<WorkstationLog> workstationLog);

	public int addAssemblyLog(List<AssemblyLog> assemblyLog);

    public int addMonkLog(MonkLog monkLog);

    public AssemblyTAT getAssemblyTAT(Integer unitId);

    public void processTATForAllUnits();

    List<WorkstationManualTask> addWorkStationManualTasks(List<WorkstationManualTask> workstationTasks);

    MonkCalibrationEvent addMonkCalibrationEvent(MonkCalibrationEvent monkCalibrationEvent);

    MonkCalibrationEvent getLastMonkCalibrationStatus(Integer unitId, Integer monkNo);

    List<MonkCalibrationStatus> getMonkCalibrationStatus(Integer unitId);

    MonkCalibrationTime getMonkCalibrationTime(Integer unitId);
}
