/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.master.domain.model.Unit;

import java.util.ArrayList;
import java.util.Map;

public class OrderEmailReceipt extends OrderReceipt {

	private String fromEmail;

	private String toEmail;

	private boolean isEmailVerified;

	private String verifyEmailLink;

	private String token;
	
	public OrderEmailReceipt(String urlBasePath, Unit unit, OrderInfo detail, String fromEmail, String toEmail, String basePath,
							 boolean isEmailVerified, String verifyEmailLink, String token, String billPromotion, boolean cancellation, CustomerEmailData customerEmailData) {
		super(urlBasePath, unit, detail, basePath, billPromotion, cancellation, customerEmailData);
		this.fromEmail = fromEmail;
		this.toEmail = toEmail;
		this.isEmailVerified = isEmailVerified;
		this.verifyEmailLink = verifyEmailLink;
		this.token = token;
	}

	@Override
	public String getTemplatePath() {
		return "template/NewOrderReceipt.html";
	}

	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getDetail().getOrder().getOrderId()
				+ (isCancellation() ? "/CancelledOrderReceipt-" : "/OrderReceipt-") + getDetail().getOrder().getOrderId()
				+ ".html";
	}

	public String getFromEmail() {
		return fromEmail;
	}

	public String getToEmail() {
		return toEmail;
	}

	public Map<String, Object> getData() {
		Map<String, Object> data = super.getData();
		data.put("isEmailVerified", isEmailVerified);
		data.put("verifyEmailLink", verifyEmailLink);
		data.put("token", token);

		return data;
	}

}