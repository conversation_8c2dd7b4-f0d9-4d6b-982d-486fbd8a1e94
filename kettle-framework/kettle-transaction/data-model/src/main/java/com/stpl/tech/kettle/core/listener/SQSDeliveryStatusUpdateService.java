package com.stpl.tech.kettle.core.listener;


import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.Session;

@Service
@Log4j2
public class SQSDeliveryStatusUpdateService {

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private OrderManagementService orderManagementService;


    @PostConstruct
    public void init() throws JMSException {
        Regions region = AppUtils.getRegion(props.getEnvironmentType());
        SQSSession session = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
        DeliveryStatusUpdateListener deliveryStatusUpdateListener = new DeliveryStatusUpdateListener(orderManagementService);
        log.info("SUBSCRIBING QUEUE ::::::::::::::: " + props.getEnvironmentType() + " _PARTNER_ORDER_DELIVERY_STATUS.fifo in region " +
                region.getName() );
        MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvironmentType().name(),
                "_PARTNER_ORDER_DELIVERY_STATUS.fifo");
        consumer.setMessageListener(deliveryStatusUpdateListener);
        SQSNotification.getInstance().getSqsConnection(region).start();
    }
}
