package com.stpl.tech.kettle.core.converter;

import com.google.gson.Gson;
import com.stpl.tech.kettle.clevertap.util.FavChaiConstants;
import com.stpl.tech.kettle.clm.model.FavChai.CustomerFavChaiCustomizationDetail;
import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.data.model.FavChaiCustomizationDetail;
import com.stpl.tech.kettle.domain.model.ChaiCustomization;
import com.stpl.tech.kettle.domain.model.ProductSource;
import com.stpl.tech.kettle.domain.model.SavedChai;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.OptionData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
@Slf4j
public class SaveChaiConverter {

    public static SavedChai convertToSavedChai(CustomerFavChaiCustomizationDetail customerFavChai, Product product, RecipeDetail recipe, HashMap<CustomerFavChaiMapping, List<FavChaiCustomizationDetail>> chaiCustomizationMap, List<CustomerFavChaiCustomizationDetail> successFullyConvertedChaiList) {
        SavedChai savedChai = null;
        List<ChaiCustomization> chaiCustomizationList = new ArrayList<>();
        Long time = System.currentTimeMillis();
       // Stopwatch stopwatch = Stopwatch.createUnstarted();
        if(Objects.nonNull(customerFavChai) && (Objects.nonNull(customerFavChai.getRecipeId()))){
            //stopwatch.start();
            savedChai= new SavedChai();
            savedChai.setCustomerId(customerFavChai.getKey().getCustomerId());
            savedChai.setDimension(customerFavChai.getDimension());
            savedChai.setQuantity(customerFavChai.getQuantity());
            savedChai.setProductId(product.getId());
            savedChai.setProductName(product.getName());
            savedChai.setRecipeId(recipe.getRecipeId());
            savedChai.setRecipeProfile(recipe.getProfile());
            savedChai.setDimension(recipe.getDimension().getName());
            savedChai.setProductShortCode(product.getShortCode());
            savedChai.setConsumeType("SELF");
            savedChai.setStatus(AppConstants.ACTIVE);
            savedChai.setCreatedAt(AppUtils.getCurrentDate());
            savedChai.setCreationTime(AppUtils.getCurrentTimestamp());
            String sourceName = "";
            if(customerFavChai.getChannelPartnerId() ==1){
                if(Objects.nonNull(customerFavChai.getSourceVersion())){
                    sourceName=FavChaiConstants.NEW_POS;
                }
            }else{
                sourceName=FavChaiConstants.POS;
            }
            savedChai.setSourceName(sourceName);
            savedChai.setSourceId(customerFavChai.getChannelPartnerId());
            savedChai.setTagType(FavChaiConstants.FAV_CHAI_TAG);
            chaiCustomizationList =convertToCustomizationList(customerFavChai.getCustomizationIds(), customerFavChai.getCustomizations(), product , recipe, customerFavChai);
            savedChai.getChaiCustomizationList().addAll(chaiCustomizationList);
            createCustomerFavChaiCstomizationMap(chaiCustomizationMap,chaiCustomizationList, savedChai, customerFavChai,successFullyConvertedChaiList);
        }
        log.info("&&&&&&&&&&&&&& Step 5 : Completed Generation of saved chai for customerId :::{} in :::{}", customerFavChai.getKey().getCustomerId(), System.currentTimeMillis() - time);
        return savedChai;
    }

    private static void createCustomerFavChaiCstomizationMap(HashMap<CustomerFavChaiMapping, List<FavChaiCustomizationDetail>> chaiCustomizationMap, List<ChaiCustomization> chaiCustomizationList, SavedChai savedChai, CustomerFavChaiCustomizationDetail customerFavChai, List<CustomerFavChaiCustomizationDetail> successFullyConvertedChaiList) {
        CustomerFavChaiMapping customerFavChaiMapping = new ModelMapper().map(savedChai, CustomerFavChaiMapping.class);
        customerFavChaiMapping.setCustomerId(savedChai.getCustomerId());
        customerFavChaiMapping.setCreationTime(AppUtils.getCurrentTimestamp());
        customerFavChaiMapping.setCreatedAt(AppUtils.parseDate(AppUtils.getSQLFormattedDate(AppUtils.getCurrentDate())));
        customerFavChaiMapping.setIsUpdated(AppConstants.NO);
        List<FavChaiCustomizationDetail> customizationDetails = chaiCustomizationList.stream()
                .map(source -> new ModelMapper().map(source, FavChaiCustomizationDetail.class))
                .collect(Collectors.toList());
        if(Objects.nonNull(chaiCustomizationMap) && !customizationDetails.isEmpty()){
            successFullyConvertedChaiList.add(customerFavChai);
            chaiCustomizationMap.putIfAbsent(customerFavChaiMapping,customizationDetails);
        }
    }

    private static List<ChaiCustomization> convertToCustomizationList(String ids, String customizations , Product product , RecipeDetail recipeDetail, CustomerFavChaiCustomizationDetail customerFavChai) {
        Long time = System.currentTimeMillis();
        //Stopwatch stopwatch = Stopwatch.createStarted();
        //stopwatch.start();
        Set<Integer> customizationIds = Arrays.stream(ids.split(";"))
                .map(Integer::parseInt) // Convert each string to an integer
                .collect(Collectors.toCollection(HashSet::new));

        Set<String> customizationsList = Arrays.stream(customizations.split(";"))// Convert each string to an integer
                .collect(Collectors.toCollection(HashSet::new));

        List<ChaiCustomization> chaiCustomizationList = new ArrayList<>();
        try{
            if (Objects.nonNull(recipeDetail)) {
                convertVariantByCustomizationName(recipeDetail, chaiCustomizationList, customizationsList);
                convertFreeAddonByCustomizationIds(recipeDetail, chaiCustomizationList, customizationsList, customizationIds);
                convertPaidAddonByCustomizationIds(recipeDetail, chaiCustomizationList, customizationsList, customizationIds);
            }
        }catch (Exception e){
            log.error("Exception while converting customization detail for customerId :{}::::::::::::", customerFavChai.getKey().getCustomerId(),e);
        }
        log.info("&&&&&&&& Step 4:::::::Completed Generation of custionization List for customerId :{} :{} :: in {}", customerFavChai.getKey().getCustomerId(), new Gson().toJson(chaiCustomizationList), System.currentTimeMillis() - time );
        return chaiCustomizationList;
    }

    private static void convertVariantByCustomizationName(RecipeDetail recipeDetail, List<ChaiCustomization> chaiCustomizationList, Set<String> customizationsList) {
        Long time = System.currentTimeMillis();
        Set<String> variants = new HashSet<>();
        // Stopwatch stopwatch = Stopwatch.createUnstarted();
        //stopwatch.start();
        if(recipeDetail.getIngredient() !=null && recipeDetail.getIngredient().getVariants() !=null){
            for (IngredientVariant v : recipeDetail.getIngredient().getVariants()) {
                for (IngredientVariantDetail vd : v.getDetails()) {
                    variants.add(vd.getAlias());
                    if(customizationsList.contains(vd.getAlias()))
                        chaiCustomizationList.add(ChaiCustomization.builder().productId(vd.getProductId()).name(vd.getAlias())
                                .dimension(null).type(ProductClassification.VARIANT.value())
                                .productSourceSystem(ProductSource.SCM.name())
                                .shortCode(getShortCode(vd.getAlias()))
                                .quantity(vd.getQuantity()).defaultSetting(AppConstants.getValue(vd.isDefaultSetting()))
                                .uom(Objects.isNull(vd.getUom()) ? null : vd.getUom().name()).build());
                }
            }
        }
        if (!variants.isEmpty() && !variants.containsAll(customizationsList)) {
            chaiCustomizationList.addAll(new ArrayList<>());
        }
        log.info("&&&&&&&& Step 1:::::::Completed Conversion of variants  in ::: {}",System.currentTimeMillis() - time );
    }

    private static void convertFreeAddonByCustomizationIds(RecipeDetail recipeDetail, List<ChaiCustomization> chaiCustomizationList, Set<String> customizationsList, Set<Integer> customizationsListIds) {
        Long time = System.currentTimeMillis();
        Set<String> addOns = new HashSet<>();

//        Stopwatch stopwatch = Stopwatch.createUnstarted();
//        stopwatch.start();
        if (recipeDetail.getAddons() != null && recipeDetail.getAddons().size() > 0) {
            for (IngredientProductDetail v : recipeDetail.getAddons()) {
                addOns.add(v.getProduct().getName());
                if (customizationsListIds.contains(v.getProduct().getProductId()))
                    chaiCustomizationList.add(ChaiCustomization.builder().productId(v.getProduct().getProductId()).name(v.getProduct().getName())
                            .dimension(v.getDimension() == null ? null : v.getDimension().getName()).type(ProductClassification.VARIANT.value())
                            .productSourceSystem(ProductSource.MENU.name())
                            .shortCode(getShortCode(v.getProduct().getName()))
                            .type(v.getProduct().getClassification().name())
                            .quantity(v.getQuantity())
                            .defaultSetting(AppConstants.getValue(v.isDefaultSetting()))
                            .uom(v.getUom() == null ? null : v.getUom().name()).build());
            }
        }
        if (!addOns.isEmpty() && !addOns.containsAll(customizationsList)) {
            chaiCustomizationList.addAll(new ArrayList<>());
        }

        log.info("&&&&&&&& Step 2:::::::Completed Conversion of free Addons  in ::: {}",System.currentTimeMillis() - time );

    }

    private static void convertPaidAddonByCustomizationIds(RecipeDetail recipeDetail, List<ChaiCustomization> chaiCustomizationList, Set<String> customizationsList, Set<Integer> customizationsListIds) {
        Long time = System.currentTimeMillis();
        //        Stopwatch stopwatch = Stopwatch.createUnstarted();
        //        stopwatch.start();
        Set<String> paidAddons = new HashSet<>();

        if(recipeDetail.getOptions()!=null && recipeDetail.getOptions().size()>0){
            for (OptionData v : recipeDetail.getOptions()) {
                paidAddons.add(v.getName());
                if(customizationsListIds.contains(v.getId()))
                    chaiCustomizationList.add(ChaiCustomization.builder().productId(v.getProductId()).name(v.getName())
                            .dimension("None").type(ProductClassification.VARIANT.value())
                            .productSourceSystem(ProductSource.ADDON.name())
                            .shortCode(v.getShortCode()==null ? null : v.getShortCode())
                            .type(ProductClassification.PAID_ADDON.name())
                            .quantity(new BigDecimal(1))
                            .defaultSetting("N")
                            .uom("PC").build());
            }
        }
        if (!paidAddons.isEmpty() && !paidAddons.containsAll(customizationsList)) {
            chaiCustomizationList.addAll(new ArrayList<>());
        }
        log.info("&&&&&&&& Step 3:::::::Completed Conversion of Paid Addons  in ::: {}",System.currentTimeMillis() - time );

    }

    private static String getShortCode(String name){
        String shortCode ="";
        if(Objects.nonNull(name) && name.length()>0){
            String [] arr = name.split(" ");
            if(arr.length>1){
                for(String ele : arr){
                    shortCode = shortCode + ele.toUpperCase().charAt(0);
                }
                return shortCode;
            }else {
                return shortCode + arr[arr.length-1].toUpperCase().charAt(0);
            }
        }
        return null;
    }
}
