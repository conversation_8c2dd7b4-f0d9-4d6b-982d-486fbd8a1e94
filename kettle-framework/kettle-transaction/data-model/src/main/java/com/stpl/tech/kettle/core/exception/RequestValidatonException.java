package com.stpl.tech.kettle.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-03-2018.
 */
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Request Validation Exception")
public class RequestValidatonException extends Exception{

    private static final long serialVersionUID = 1L;

    private HttpStatus code;


    public RequestValidatonException() {
    }

    public RequestValidatonException(String message) {
        super(message);
    }

    public RequestValidatonException(HttpStatus code) {
        super(code.getReasonPhrase());
        this.code = code;
    }

    public RequestValidatonException(HttpStatus code, String message) {
        super(message);
        this.code = code;
    }

    public RequestValidatonException(Throwable cause) {
        super(cause);
    }

    public RequestValidatonException(String message, Throwable cause) {
        super(message, cause);
    }


    public RequestValidatonException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
