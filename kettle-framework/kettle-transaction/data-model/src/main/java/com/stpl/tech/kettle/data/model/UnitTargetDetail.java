/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are private by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "DAY_WISE_TARGETS_DATA")
public class UnitTargetDetail {

	private int dayWiseTargetDetailId;
	private int unitId;
	private Date businessDate;
	private BigDecimal netSales;
	private BigDecimal netDeliverySales;
	private BigDecimal netDineInSales;
	private BigDecimal netApc;
	private BigDecimal netDeliveryApc;
	private BigDecimal netDineInApc;
	private Integer netTickets;
	private Integer netDeliveryTickets;
	private Integer netDineInTickets;
	private String status;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "DAY_WISE_TARGETS_DATA_ID", unique = true, nullable = false)
	public int getDayWiseTargetDetailId() {
		return dayWiseTargetDetailId;
	}

	public void setDayWiseTargetDetailId(int unitExpenseDetailId) {
		this.dayWiseTargetDetailId = unitExpenseDetailId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE", nullable = false, length = 10)
	public Date getBusinessDate() {
		return this.businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name = "TARGET_NET_TICKETS")
	public Integer getNetTickets() {
		return netTickets;
	}

	public void setNetTickets(Integer netTickets) {
		this.netTickets = netTickets;
	}

	@Column(name = "TARGET_NET_SALES", precision = 10)
	public BigDecimal getNetSales() {
		return netSales;
	}

	public void setNetSales(BigDecimal netSales) {
		this.netSales = netSales;
	}

	@Column(name = "TARGET_NET_DELIVERY_SALES", precision = 10)
	public BigDecimal getNetDeliverySales() {
		return netDeliverySales;
	}

	public void setNetDeliverySales(BigDecimal netDeliverySales) {
		this.netDeliverySales = netDeliverySales;
	}

	@Column(name = "TARGET_NET_DINE_IN_SALES", precision = 10)
	public BigDecimal getNetDineInSales() {
		return netDineInSales;
	}

	public void setNetDineInSales(BigDecimal netDineInSales) {
		this.netDineInSales = netDineInSales;
	}

	@Column(name = "TARGET_NET_APC", precision = 10)
	public BigDecimal getNetApc() {
		return netApc;
	}

	public void setNetApc(BigDecimal netApc) {
		this.netApc = netApc;
	}

	@Column(name = "TARGET_NET_DELIVERY_APC", precision = 10)
	public BigDecimal getNetDeliveryApc() {
		return netDeliveryApc;
	}

	public void setNetDeliveryApc(BigDecimal netDeliveryApc) {
		this.netDeliveryApc = netDeliveryApc;
	}

	@Column(name = "TARGET_NET_DINE_IN_APC", precision = 10)
	public BigDecimal getNetDineInApc() {
		return netDineInApc;
	}

	public void setNetDineInApc(BigDecimal netDineInApc) {
		this.netDineInApc = netDineInApc;
	}

	@Column(name = "TARGET_NET_DELIVERY_TICKETS")
	public Integer getNetDeliveryTickets() {
		return netDeliveryTickets;
	}

	public void setNetDeliveryTickets(Integer netDeliveryTickets) {
		this.netDeliveryTickets = netDeliveryTickets;
	}

	@Column(name = "TARGET_DINE_IN_TICKETS")
	public Integer getNetDineInTickets() {
		return netDineInTickets;
	}

	public void setNetDineInTickets(Integer netDineInTickets) {
		this.netDineInTickets = netDineInTickets;
	}
	
	@Column(name = "TARGET_STATUS")
	public String getStatus() {
		return status;
	}
	
	public void setStatus(String status) {
		this.status = status;
	}
	
	

}