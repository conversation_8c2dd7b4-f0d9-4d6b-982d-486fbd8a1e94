/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.util;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.stpl.tech.util.AppUtils;
import org.springframework.jdbc.core.ResultSetExtractor;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;

public class ResultSetConverter implements ResultSetExtractor<JsonArray> {

	private JsonArray header;

	public JsonArray getHeader() {
		return this.header;
	}

	public JsonArray getHeader(ResultSetMetaData rsmd) throws SQLException {
		JsonArray header = new JsonArray();
		for (int i = 1; i <= rsmd.getColumnCount(); i++) {
			JsonObject obj = new JsonObject();
			obj.addProperty("field", rsmd.getColumnName(i));
			obj.addProperty("type", rsmd.getColumnTypeName(i));
			header.add(obj);
		}
		return header;
	}

	@Override
	public JsonArray extractData(ResultSet rs) throws SQLException {
		JsonArray json = new JsonArray();
		ResultSetMetaData rsmd = rs.getMetaData();
		this.header = getHeader(rsmd);
		while (rs.next()) {
			int numColumns = rsmd.getColumnCount();
			JsonObject obj = new JsonObject();
			for (int i = 1; i < numColumns + 1; i++) {
				String columnName = rsmd.getColumnName(i);
				switch (rsmd.getColumnType(i)) {
				case java.sql.Types.BIGINT:
					obj.addProperty(columnName, rs.getInt(columnName));
					break;
				case java.sql.Types.BOOLEAN:
					obj.addProperty(columnName, rs.getBoolean(columnName));
					break;
				case java.sql.Types.DOUBLE:
					obj.addProperty(columnName, rs.getDouble(columnName));
					break;
				case java.sql.Types.FLOAT:
					obj.addProperty(columnName, rs.getFloat(columnName));
					break;
				case java.sql.Types.DECIMAL:
					obj.addProperty(columnName, rs.getBigDecimal(columnName));
					break;
				case java.sql.Types.INTEGER:
					obj.addProperty(columnName, rs.getInt(columnName));
					break;
				case java.sql.Types.LONGNVARCHAR:
					obj.addProperty(columnName, rs.getNString(columnName));
					break;
				case java.sql.Types.LONGVARCHAR:
					obj.addProperty(columnName, rs.getString(columnName));
					break;
				case java.sql.Types.NVARCHAR:
					obj.addProperty(columnName, rs.getNString(columnName));
					break;
				case java.sql.Types.VARCHAR:
					obj.addProperty(columnName, rs.getString(columnName));
					break;
				case java.sql.Types.TINYINT:
					obj.addProperty(columnName, rs.getInt(columnName));
					break;
				case java.sql.Types.SMALLINT:
					obj.addProperty(columnName, rs.getInt(columnName));
					break;
				case java.sql.Types.DATE:
					obj.addProperty(columnName, AppUtils.getTimeWithoutMillisISTString(rs.getDate(columnName)));
					break;
				case java.sql.Types.TIMESTAMP:
					obj.addProperty(columnName, AppUtils.getTimeWithoutMillisISTString(rs.getTimestamp(columnName)));
					break;
				default:
					break;
				}
			}
			json.add(obj);
		}

		return json;
	}

}