package com.stpl.tech.kettle.core.data.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.stpl.tech.util.AppUtils;

public class RevenueCertificateData {

	protected Integer unitId;
	protected BigDecimal grossSales;
	protected Date date;
	protected BigDecimal serviceCharge;
	protected BigDecimal salesAfterServiceCharge;
	protected BigDecimal discount;
	protected BigDecimal taxes;
	protected BigDecimal netSale;
	protected BigDecimal chaayosDineInSales;
	protected BigDecimal chaayosDeliverySales;
	protected BigDecimal gntDineInSales;
	protected BigDecimal gntDeliverySales;
	protected BigDecimal desiCanteenDineInSales;
	protected BigDecimal desiCanteenDeliverySales;


	public RevenueCertificateData() {
		// TODO Auto-generated constructor stub
	}

	public RevenueCertificateData(Integer unitId, Date date, BigDecimal discount, BigDecimal taxes,
			BigDecimal netSale, BigDecimal chaayosDineInSales, BigDecimal chaayosDeliverySales, BigDecimal gntDineInSales, BigDecimal gntDeliverySales, BigDecimal desiCanteenDineInSales, BigDecimal desiCanteenDeliverySales) {
		super();
		this.unitId = unitId;
		this.grossSales = AppUtils.add(AppUtils.add(netSale, taxes), discount).setScale(0, BigDecimal.ROUND_HALF_UP);
		this.date = date;
		this.serviceCharge = BigDecimal.ZERO;
		this.salesAfterServiceCharge = BigDecimal.ZERO.add(grossSales).setScale(0, BigDecimal.ROUND_HALF_UP);
		this.discount = discount.setScale(0, BigDecimal.ROUND_HALF_UP);
		this.taxes = taxes.setScale(0, BigDecimal.ROUND_HALF_UP);
		this.netSale = netSale.setScale(0, BigDecimal.ROUND_HALF_UP);
		this.chaayosDineInSales = chaayosDineInSales.setScale(0, BigDecimal.ROUND_HALF_UP);
		this.chaayosDeliverySales = chaayosDeliverySales.setScale(0, BigDecimal.ROUND_HALF_UP);
		this.gntDineInSales = gntDineInSales.setScale(0, BigDecimal.ROUND_HALF_UP);
		this.gntDeliverySales = gntDeliverySales.setScale(0, BigDecimal.ROUND_HALF_UP);
		this.desiCanteenDineInSales = desiCanteenDineInSales.setScale(0, BigDecimal.ROUND_HALF_UP);
		this.desiCanteenDeliverySales = desiCanteenDeliverySales.setScale(0, BigDecimal.ROUND_HALF_UP);
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public BigDecimal getGrossSales() {
		return grossSales;
	}

	public void setGrossSales(BigDecimal grossSales) {
		this.grossSales = grossSales;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public BigDecimal getServiceCharge() {
		return serviceCharge;
	}

	public void setServiceCharge(BigDecimal serviceCharge) {
		this.serviceCharge = serviceCharge;
	}

	public BigDecimal getSalesAfterServiceCharge() {
		return salesAfterServiceCharge;
	}

	public void setSalesAfterServiceCharge(BigDecimal salesAfterServiceCharge) {
		this.salesAfterServiceCharge = salesAfterServiceCharge;
	}

	public BigDecimal getDiscount() {
		return discount;
	}

	public void setDiscount(BigDecimal discount) {
		this.discount = discount.setScale(0, BigDecimal.ROUND_HALF_UP);
	}

	public BigDecimal getTaxes() {
		return taxes;
	}

	public void setTaxes(BigDecimal taxes) {
		this.taxes = taxes;
	}

	public BigDecimal getNetSale() {
		return netSale;
	}

	public void setNetSale(BigDecimal netSale) {
		this.netSale = netSale;
	}

	public BigDecimal getChaayosDineInSales() {
		return chaayosDineInSales;
	}

	public void setChaayosDineInSales(BigDecimal chaayosDineInSales) {
		this.chaayosDineInSales = chaayosDineInSales;
	}

	public BigDecimal getChaayosDeliverySales() {
		return chaayosDeliverySales;
	}

	public void setChaayosDeliverySales(BigDecimal chaayosDeliverySales) {
		this.chaayosDeliverySales = chaayosDeliverySales;
	}

	public BigDecimal getGntDineInSales() {
		return gntDineInSales;
	}

	public void setGntDineInSales(BigDecimal gntDineInSales) {
		this.gntDineInSales = gntDineInSales;
	}

	public BigDecimal getGntDeliverySales() {
		return gntDeliverySales;
	}

	public void setGntDeliverySales(BigDecimal gntDeliverySales) {
		this.gntDeliverySales = gntDeliverySales;
	}

	public BigDecimal getDesiCanteenDineInSales() {
		return desiCanteenDineInSales;
	}

	public void setDesiCanteenDineInSales(BigDecimal desiCanteenDineInSales) {
		this.desiCanteenDineInSales = desiCanteenDineInSales;
	}

	public BigDecimal getDesiCanteenDeliverySales() {
		return desiCanteenDeliverySales;
	}

	public void setDesiCanteenDeliverySales(BigDecimal desiCanteenDeliverySales) {
		this.desiCanteenDeliverySales = desiCanteenDeliverySales;
	}
}
