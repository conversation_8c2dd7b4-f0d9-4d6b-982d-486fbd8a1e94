package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.ExternalPartnerCardDao;
import com.stpl.tech.kettle.data.model.ExternalPartnerCardDetail;
import com.stpl.tech.kettle.domain.model.*;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;
import com.stpl.tech.util.AppUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("GyftrService")
public class GyftrCardServiceImpl extends PartnerCardServiceImpl {

	private final String GYFTR_CODE = CashCardType.GYFTR.name();

	private final static Logger LOG = LoggerFactory.getLogger(GyftrCardServiceImpl.class);

	@Autowired
	private ExternalPartnerCardDao partnerCardDao;
	@Autowired
	private EnvironmentProperties properties;

	@Override
	public void updateVoucher(String voucherCode, String cardNumber, String partnerCode) {
		ExternalPartnerCardDetail cardDetail = getPartnerCardDetail(voucherCode, partnerCode,
				PartnerCardRequestStatus.SUCCESS.name()).get(0);
		cardDetail.setCardNumber(cardNumber);
		cardDetail.setRequestStatus(PartnerCardRequestStatus.REDEEMED.name());
		partnerCardDao.update(cardDetail);
	}

	private GyftrVBatchConsumeResult getGytrCardDetail(Order order, String voucherCode, String requestJobNumber,
			List<String> voucherList) throws CardValidationException {
		ExternalPartnerDetail partnerInfo = getExternalPartner(GYFTR_CODE);
		List<ExternalPartnerCardDetail> cardDetails = addRequestLog(order, requestJobNumber, voucherList);
		HttpGet requestObject = null;
		GyftrVBatchConsumeResult consumeResult = null;
		try {
			requestObject = new HttpGet(
					getBuilderUrl(partnerInfo, voucherCode, "/BatchConsume", requestJobNumber, order.getUnitId()));
		} catch (URISyntaxException e) {
			LOG.error("Error in building GYFTR Consume URI ", e);
			throw new CardValidationException("Error in building GYFTR Consume URI :  " + e.getMessage());
		}
		try {
			HttpResponse response = WebServiceHelper.getRequest(requestObject);
			if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
				ByteArrayOutputStream baos = null;
				GyftrVoucher voucher = WebServiceHelper.convertResponse(response, GyftrVoucher.class, true);
				if (voucher == null) {
					failureRequest("Gytr Service unavailable right now.Please Try Again Later.", cardDetails, "", baos);
				} else {
					LOG.info("GyftrVoucher : " + voucher);
					baos = new ByteArrayOutputStream();
					ObjectOutput out = null;
					try {
						out = new ObjectOutputStream(baos);
						out.writeObject(voucher);
						out.flush();
					} finally {
						try {
							baos.close();
						} catch (IOException e) {
							LOG.error("Error while serializing repsonse json :  ", e);
							throw new CardValidationException(
									"Error while consuming Gyftr Voucher :  " + e.getMessage());
						}
					}
					consumeResult = voucher.getvBatchConsumeResult().get(0);
					if (consumeResult.getResultType().equalsIgnoreCase(PartnerCardRequestStatus.FAILED.name())) {
						failureRequest(consumeResult.getErrorMsg(), cardDetails, consumeResult.getAuthorizationCode(),
								baos);
					} else if (consumeResult.getResultType()
							.equalsIgnoreCase(PartnerCardRequestStatus.SUCCESS.name())) {
						Map<String, GyftrVoucherAction> voucherValueMap = getVoucherMap(consumeResult);
						updateRequestLog(cardDetails, PartnerCardRequestStatus.SUCCESS.name(),
								consumeResult.getAuthorizationCode(), voucherValueMap, baos);
						return consumeResult;
					} else {
						failureRequest("Gytr Service unavailable right now for redemption.Please Try Again Later.",
								cardDetails, "", null);
					}
				}
			}
		} catch (Exception e) {
			LOG.error("Error while getting Gyftr Voucher Details :  ", e);
			throw new CardValidationException("Error while consuming Gyftr Voucher :  " + e.getMessage());
		} finally {
			requestObject.releaseConnection();
			requestObject.abort();
		}
		return consumeResult;
	}

	private GyftrVoucherQueryResult queryGyftrVoucherDetail(String voucherCode, String requestJobNumber, Order order)
			throws CardValidationException {
		ExternalPartnerDetail partnerInfo = getExternalPartner(GYFTR_CODE);
		HttpGet requestObject = null;
		GyftrVoucherQueryResult queryResult = null;
		try {
			requestObject = new HttpGet(
					getBuilderUrl(partnerInfo, voucherCode, "/QueryVoucher", requestJobNumber, order.getUnitId()));
		} catch (URISyntaxException e) {
			LOG.error("Error in building GYFTR query URI ", e);
			throw new CardValidationException("Error in building GYFTR query URI :  " + e.getMessage());
		}
		try {
			HttpResponse response = WebServiceHelper.getRequest(requestObject);
			if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
				GyftrQueryVoucher voucher = WebServiceHelper.convertResponse(response, GyftrQueryVoucher.class, false);
				if (voucher == null) {
					throw new CardValidationException(
							"Gytr Service unavailable right now for query.Please Try Again Later.");
				} else {
					LOG.info("GyftrQueryVoucher : " + voucher);
					queryResult = voucher.getvQueryVoucherResult().get(0);
					if (queryResult.getResultType().equalsIgnoreCase(PartnerCardRequestStatus.FAILED.name())) {
						throw new CardValidationException(queryResult.getErroMsg());
					} else if (queryResult.getResultType().equalsIgnoreCase(PartnerCardRequestStatus.SUCCESS.name())) {
						if (!queryResult.getStatus().equals(PartnerCardStatus.VALID.name())) {
							String msg = null;
							if (queryResult.getStatus().equalsIgnoreCase(PartnerCardStatus.INVALID.name())) {
								msg = "Gyftr Voucher Code : " + voucherCode + " is INVALID.";
							} else if (queryResult.getStatus().equalsIgnoreCase(PartnerCardStatus.EXPIRED.name())) {
								msg = "Gyftr Voucher Code : " + voucherCode + " is EXPIRED.";
							} else if (queryResult.getStatus().equalsIgnoreCase(PartnerCardStatus.CONSUMED.name())) {
								msg = "Gyftr Voucher Code : " + voucherCode + " is already CONSUMED.";
							} else {
								msg = "Gyftr Voucher Code : " + voucherCode + " is INVALID.";
							}
							throw new CardValidationException(msg);
						}
						return queryResult;
					}
				}
			}
		} catch (Exception e) {
			LOG.error("Error while querying Gyftr Voucher Details :  ", e);
			throw new CardValidationException("Error while getting Gyftr Voucher Details :  " + e.getMessage());
		} finally {
			requestObject.releaseConnection();
			requestObject.abort();
		}
		return queryResult;
	}

	private String getBuilderUrl(ExternalPartnerDetail partnerInfo, String voucherCode, String query,
			String requestJobNumber, int unitId) throws URISyntaxException {
		URIBuilder builder = new URIBuilder(partnerInfo.getEndPoint().concat(query));
		builder.setParameter("deviceCode", "p");
		LOG.info("merchantUid : " + partnerInfo.getUsername());
		builder.setParameter("merchantUid", partnerInfo.getUsername());
		if (AppUtils.isProd(properties.getEnvironmentType())) {
			builder.setParameter("ShopCode", String.valueOf(unitId));
		} else {
			builder.setParameter("ShopCode", "WEBTEST");
		}
		builder.setParameter("voucherNumber", voucherCode);
		LOG.info("Password : " + partnerInfo.getPassCode());
		builder.setParameter("Password", partnerInfo.getPassCode());
		builder.setParameter("requestJobNumber", requestJobNumber);
		builder.setParameter("BillValue", "0");
		String uri = builder.toString().replaceAll("%2C", ",");;
		LOG.info("Gyftr URL : " + uri);
		return uri;
	}

	private void failureRequest(String msg, List<ExternalPartnerCardDetail> cardDetails, String authCode,
			ByteArrayOutputStream baos) throws CardValidationException {
		updateRequestLog(cardDetails, PartnerCardRequestStatus.FAILED.name(), authCode, null, baos);
		throw new CardValidationException(msg);
	}

	private List<ExternalPartnerCardDetail> addRequestLog(Order order, String billNumber, List<String> voucherList) {
		List<ExternalPartnerCardDetail> cardDetails = new ArrayList<>();
		Date date = AppUtils.getCurrentTimestamp();
		for (String voucherCode : voucherList) {
			ExternalPartnerCardDetail cardDetail = new ExternalPartnerCardDetail();
			cardDetail.setExternalOrderId(billNumber);
			cardDetail.setPartnerCardNumber(voucherCode);
			cardDetail.setPartnerCode(GYFTR_CODE);
			cardDetail.setRequestSource(order.getSource());
			cardDetail.setRequestTime(date);
			cardDetail.setCustomerId(order.getCustomerId());
			cardDetail.setCustomerName(order.getCustomerName());
			cardDetail.setRequestStatus(PartnerCardRequestStatus.INITIATED.name());
			cardDetail = (ExternalPartnerCardDetail) partnerCardDao.add(cardDetail);
			cardDetails.add(cardDetail);
		}
		return cardDetails;
	}

	private void updateRequestLog(List<ExternalPartnerCardDetail> cardDetails, String reqStatus, String tarnsId,
			Map<String, GyftrVoucherAction> voucherMap, ByteArrayOutputStream baos) throws CardValidationException {
		Date date = AppUtils.getCurrentTimestamp();
		for (ExternalPartnerCardDetail detail : cardDetails) {
			ExternalPartnerCardDetail cardDetail = partnerCardDao.find(ExternalPartnerCardDetail.class, detail.getCardDetailId());
			cardDetail.setRequestStatus(reqStatus);
			cardDetail.setResponseTime(date);
			cardDetail.setPartnerTransactionId(tarnsId);
			cardDetail.setTransactionAmount(
					voucherMap != null ? new BigDecimal(voucherMap.get(cardDetail.getPartnerCardNumber()).getVALUE())
							: BigDecimal.ZERO);
			if (baos != null) {
				cardDetail.setResponse(baos.toByteArray());
			}
			partnerCardDao.update(cardDetail);
		}
	}

	@Override
	public List<String> verifyVoucher(Order order, boolean consume) throws CardValidationException {
		List<String> voucherList = new ArrayList<>();
		Map<String, OrderItem> voucherItemMap = new HashMap<>();
		StringBuilder voucherString = new StringBuilder();
		String requestJobNumber = getUniquePartnerBillNumber();
		for (OrderItem item : order.getOrders()) {
			validateVoucherCode(item.getItemCode());
			GyftrVoucherQueryResult queryResult = queryGyftrVoucherDetail(item.getItemCode(), requestJobNumber, order);
			item.setValidUpto(AppUtils.parseDate(queryResult.getEndDate(), new SimpleDateFormat("dd-MMM-yyyy")));
			voucherItemMap.put(item.getItemCode(), item);
			voucherList.add(item.getItemCode());
			voucherString.append(item.getItemCode());
			if (voucherList.size() < order.getOrders().size()) {
				voucherString.append(",");
			}
		}
		GyftrVBatchConsumeResult consumeResponse = getGytrCardDetail(order, voucherString.toString(), requestJobNumber,
				voucherList);
		if (consumeResponse != null) {
			Map<String, GyftrVoucherAction> voucherMap = getVoucherMap(consumeResponse);
			BigDecimal amount = BigDecimal.ZERO;
			for (Map.Entry<String, OrderItem> voucher : voucherItemMap.entrySet()) {
				OrderItem item = voucher.getValue();
				BigDecimal value = new BigDecimal(voucherMap.get(voucher.getKey()).getVALUE());
				item.setPrice(value);
				item.setAmount(value);
				item.setTotalAmount(value);
				amount = AppUtils.add(amount, value);
			}
			updateTransactionDetail(amount, order.getTransactionDetail());
			order.setSettlementType(SettlementType.CREDIT);
			order.setSettlements(getSettlementDetail(amount));
			return voucherList;
		}
		return null;
	}

	private List<Settlement> getSettlementDetail(BigDecimal amount) {
		Settlement settlement = new Settlement();
		settlement.setMode(getExternalPartner(GYFTR_CODE).getLinkedPaymentModeId());
		settlement.setAmount(amount);
		settlement.setExternalSettlements(getExternalSettlement(amount));
		List<Settlement> list = new ArrayList<>();
		list.add(settlement);
		return list;
	}

	private List<ExternalSettlement> getExternalSettlement(BigDecimal amount) {
		ExternalSettlement externalSettlement = new ExternalSettlement();
		externalSettlement.setAmount(amount);
		externalSettlement
				.setExternalTransactionId(getExternalPartner(GYFTR_CODE).getLinkedCreditAccountId().toString());
		List<ExternalSettlement> external = new ArrayList<>();
		external.add(externalSettlement);
		return external;
	}

	private void updateTransactionDetail(BigDecimal amount, TransactionDetail transactionDetail) {
		transactionDetail.setPaidAmount(amount);
		transactionDetail.setTaxableAmount(amount);
		transactionDetail.setTotalAmount(amount);
	}

	private Map<String, GyftrVoucherAction> getVoucherMap(GyftrVBatchConsumeResult result) {
		HashMap<String, GyftrVoucherAction> voucherValueMap = new HashMap<>();
		for (GyftrVoucherAction action : result.getVOUCHERACTION()) {
			voucherValueMap.put(action.getVOUCHERNUMBER(), action);
		}
		return voucherValueMap;
	}

	private void validateVoucherCode(String voucherCode) throws CardValidationException {
		List<ExternalPartnerCardDetail> cardDetails = getPartnerCardDetail(voucherCode, GYFTR_CODE,
				PartnerCardRequestStatus.REDEEMED.name());
		if (cardDetails.size() > 0) {
			throw new CardValidationException("Gyftr Voucher Code : " + voucherCode + " is already CONSUMED.");
		}
	}

	/**
	 * ******************** Voucher Status : Voucher Code **************************
	 * ********** CONSUMED : ****************, **************** ****************
	 * VALID : ****************
	 */
	public static void main(String[] args) {
		URIBuilder builder = null;
		HttpGet requestObject = null;
		GyftrVoucherQueryResult result = null;
		try {
			builder = new URIBuilder(
					"https://pos-staging.vouchagram.net/Service/RestServiceImpl.svc".concat("/BatchConsume"));
			builder.setParameter("deviceCode", "p");
			builder.setParameter("merchantUid", "49F79EB8-061D-4A6F-B7FA-E5DC1088DBBE");
			builder.setParameter("ShopCode", "WEBTEST");
			builder.setParameter("voucherNumber", "****************,9451442679774231,3431395535336332");
			builder.setParameter("Password", "DpWB+dGrxKnrWEEXPUZC/A==");
			builder.setParameter("requestJobNumber", "*********");
			builder.setParameter("BillValue", "0");
			String str = builder.toString().replaceAll("%2C", ",");
			LOG.info("Gyftr URL : " + builder.toString().replaceAll("%2C", ","));
			requestObject = new HttpGet(str);
		} catch (URISyntaxException e1) {
			LOG.error("Error in building GYFTR URI ", e1);
		}
		try {

			HttpResponse response = WebServiceHelper.getRequest(requestObject);
			System.out.println("dskjf" + response);

			if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
				GyftrQueryVoucher voucher = WebServiceHelper.convertResponse(response, GyftrQueryVoucher.class, false);
				System.out.println("voucher : " + voucher);
				ByteArrayOutputStream bos = null;
				if (response != null) {
					bos = new ByteArrayOutputStream();
					ObjectOutput out = null;
					try {
						out = new ObjectOutputStream(bos);
						out.writeObject(voucher);
						out.flush();
						byte[] yourBytes = bos.toByteArray();
						System.out.println(yourBytes.length);
						ByteArrayInputStream bis = new ByteArrayInputStream(yourBytes);
						ObjectInput in = null;
						try {
							in = new ObjectInputStream(bis);
							Object o = in.readObject();
							System.out.println("objec " + o);
						} finally {
							try {
								if (in != null) {
									in.close();
								}
							} catch (IOException ex) {
								// ignore close exception
							}
						}
					} finally {
						try {
							bos.close();
						} catch (IOException ex) {
							// ignore close exception
						}
					}
				}
				if (voucher == null) {
					System.out.println("Gytr Service unavailable right now.Please Try Again Later.");
				} else {
					result = voucher.getvQueryVoucherResult().get(0);
					if (result.getResultType().equalsIgnoreCase(PartnerCardRequestStatus.FAILED.name())) {
						System.out.println("Gytr Service unavailable right now.Please Try Again Later.");
					} else if (result.getResultType().equalsIgnoreCase(PartnerCardRequestStatus.SUCCESS.name())) {
						System.out.println("success : " + result.getStatus());
					} else {
						System.out.println("Gytr Service unavailable right now.Please Try Again Later.");
					}
				}

			}
		} catch (Exception e) {
			LOG.error("Error while getting Gyftr Voucher Details :  ", e);
		} finally {
			requestObject.releaseConnection();
			requestObject.abort();
		}
	}

}
