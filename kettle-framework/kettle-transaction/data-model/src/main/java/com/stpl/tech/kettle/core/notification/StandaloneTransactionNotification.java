package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class StandaloneTransactionNotification extends EmailNotification {

	private final StandaloneTransactionReceipt receipt;

	public StandaloneTransactionNotification() {
		this.receipt = null;
	}

	public StandaloneTransactionNotification(StandaloneTransactionReceipt receipt) {
		this.receipt = receipt;
	}

	public String subject() {
		return (TransactionUtils.isDev(receipt.getEnv()) ? EnvType.DEV.name() + " " : "")
				+ getSubject(receipt.getCampaign());
	}

	String getSubject(String campaign) {
		switch (campaign) {
		case StandaloneTransactionReceipt.COVID19_SUCCESS:
			return "#giveback - You did the right thing";
		case StandaloneTransactionReceipt.COVID19_FAILURE:
			return "#giveback - Transaction failed - Please complete your donation";
		}
		return "Standalone Transaction Receipt " + receipt.getDetail().getPaymentId();
	}

	public String body() throws EmailGenerationException {

		try {
			return receipt.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template StandaloneTransactionReceipt", e);
		}
	}

	public String getFromEmail() {
		return receipt.getFromEmail();
	}

	public String[] getToEmails() {
		return receipt.getToEmail().split(",");
	}

	@Override
	public EnvType getEnvironmentType() {
		return receipt.getEnv();
	}

}
