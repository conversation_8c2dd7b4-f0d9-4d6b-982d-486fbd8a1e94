/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.domain.model.InventoryUpdateEvent;
import com.stpl.tech.kettle.domain.model.ProductInventory;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.IdCodeName;

public interface UnitInventoryManagementService {

	public List<ProductInventory> getUnitInventory(int unitId) throws DataNotFoundException;

	public List<ProductInventory> getUnitInventory(int unitId, List<Integer> productIds) throws DataNotFoundException;

	public Map<Integer, Integer> getUnitInventoryForWeb(int unitId) throws DataNotFoundException;

	public List<ProductInventory> getUnitInventoryForProducts(int unitId, List<Integer> productIds)
			throws DataNotFoundException;

	public Map<Integer, Integer> getUnitInventoryForProductsForWeb(int id, List<Integer> list)
			throws DataNotFoundException;

	public boolean stockOutInventory(int unitId, int productId) throws DataUpdationException;

	public boolean updateUnitInventory(InventoryUpdateEvent updateData, boolean generateEvents,
			boolean incrementalUpdate, int employeeId, Integer orderId, boolean isCancellation);

	public Collection<IdCodeName> getDeliveryPartners(int unitId);

}
