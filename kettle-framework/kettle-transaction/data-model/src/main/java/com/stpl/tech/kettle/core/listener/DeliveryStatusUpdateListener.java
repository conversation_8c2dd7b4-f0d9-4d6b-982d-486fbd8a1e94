package com.stpl.tech.kettle.core.listener;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.amazonaws.util.Base64;
import com.stpl.tech.kettle.channelpartner.core.queue.model.OrderDeliveryStatusUpdate;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.util.JSONSerializer;
import lombok.extern.log4j.Log4j2;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;

@Log4j2
public class DeliveryStatusUpdateListener  implements MessageListener {

    private final OrderManagementService service;

    public DeliveryStatusUpdateListener(OrderManagementService orderManagementService){
        service = orderManagementService;
    }

    @Override
    public void onMessage(Message message) {
        try {
            log.info("On DeliveryStatusUpdate Message " + message.getJMSMessageID());
            if (message instanceof SQSObjectMessage) {
                log.info("Message instance of : SQSObjectMessage : " + message.getJMSMessageID());
                SQSObjectMessage object = (SQSObjectMessage) message;
                String response = JSONSerializer.toJSON(object.getObject());
                log.info("SQSObjectMessage : {}", response);
                OrderDeliveryStatusUpdate event = JSONSerializer.toJSON(response, OrderDeliveryStatusUpdate.class);
                service.updateOrderForDeliveryStatusUpdate(event);
                message.acknowledge();
                log.info("Order Acknowledge : SQSObjectMessage : " + message.getJMSMessageID());
            } else if (message instanceof SQSTextMessage) {
                log.info("Message instance of : SQSTextMessage : " + message.getJMSMessageID());
                SQSTextMessage object = (SQSTextMessage) message;
                String response = (String) object.getText();
                OrderDeliveryStatusUpdate statusUpdate =  deserialize(response);
                service.updateOrderForDeliveryStatusUpdate(statusUpdate);
                message.acknowledge();
                log.info("Order Acknowledge : SQSTextMessage : " + message.getJMSMessageID());
            }else {
                log.info("Order Not Acknowledged" + message.getJMSMessageID());
            }

        } catch (JMSException e) {
            log.error("Error while saving the message", e);
        } catch (Exception e) {
            log.error("Error while saving the message", e);
        }
    }


    private static OrderDeliveryStatusUpdate deserialize(String data) throws JMSException {
        if (data == null) {
            return null;
        } else {
            OrderDeliveryStatusUpdate obj;
            try {
                byte[] b = Base64.decode(data.getBytes());
                ByteArrayInputStream bi = new ByteArrayInputStream(b);
                ObjectInputStream si = new ObjectInputStream(bi);
                obj = (OrderDeliveryStatusUpdate) si.readObject();
                return obj;
            } catch (IOException ex) {
                log.error("IOException: cannot serialize objectMessage", ex);
            } catch (Exception ex) {
                log.error("IOException: cannot serialize objectMessage", ex);
            }
        }
        return null;
    }
}
