package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.service.PullTransferSettlementReasonService;
import com.stpl.tech.kettle.data.dao.PullTransferSettlementReasonDao;
import com.stpl.tech.kettle.data.model.UnitPullDetail;
import com.stpl.tech.kettle.data.model.UnitPullTransferSettlementReason;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class PullTransferSettlementReasonServiceImpl implements PullTransferSettlementReasonService {

    @Autowired
    private PullTransferSettlementReasonDao dao;

    private static final Logger LOG = LoggerFactory.getLogger(PullTransferSettlementReasonServiceImpl.class);


    @Override
    @Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean savePullTransferSettlement(List<UnitPullDetail> unitPullDetailList,int dayCloseId,String pullTransferReason,String status,Date date) {
        for (UnitPullDetail ele:unitPullDetailList){
            UnitPullTransferSettlementReason unitPullTransferSettlementReason = new UnitPullTransferSettlementReason();

            unitPullTransferSettlementReason.setDayClosureId(dayCloseId);
            unitPullTransferSettlementReason.setUnitId(ele.getUnitId());
            unitPullTransferSettlementReason.setPullId(ele.getId());
            unitPullTransferSettlementReason.setReason(pullTransferReason);
            unitPullTransferSettlementReason.setStatus(status);
            unitPullTransferSettlementReason.setBusinessDate(date);
            unitPullTransferSettlementReason.setCreatedBy(ele.getCreatedBy());
            unitPullTransferSettlementReason.setWitnessBy(ele.getWitnessedBy());
            unitPullTransferSettlementReason.setPaymentId(ele.getPaymentModeId());
            try {
                dao.addPullTransferSettlementReason(unitPullTransferSettlementReason);
            }catch (Exception e){
                LOG.info("Error Saving unitPullTransferSettlementReason",e);
                return false;
            }
        }
        return true;
    }

}
