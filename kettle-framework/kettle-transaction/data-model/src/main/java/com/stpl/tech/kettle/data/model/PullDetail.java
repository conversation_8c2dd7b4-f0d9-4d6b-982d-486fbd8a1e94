/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "PULL_DETAIL")
public class PullDetail implements Serializable {

	private Integer id;

	private Date creationTime;

	private int createdBy;

	private String witnessedBy;

	private int paymentModeId;

	private BigDecimal pullAmount;

	private String comment;

	private String status;

	private Date pullDate;

	private String pendingReason;

	private String source;

	private int unitId;

	private ClosurePaymentDetails closurePaymentDetails;

	private List<PullDenomination> pullDenominations;

	/* private UnitClosureEntry unitClosure; */

	private SettlementDetail settlementDetail;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "PULL_ID", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "CREATION_TIME", nullable = false)
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public int getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(int createdBy) {
		this.createdBy = createdBy;
	}

	@Column(name = "WITNESSED_BY", nullable = true)
	public String getWitnessedBy() {
		return witnessedBy;
	}

	public void setWitnessedBy(String witnessedBy) {
		this.witnessedBy = witnessedBy;
	}

	@Column(name = "PAYMENT_MODE", nullable = false)
	public int getPaymentModeId() {
		return paymentModeId;
	}

	public void setPaymentModeId(int paymentMode) {
		this.paymentModeId = paymentMode;
	}

	@Column(name = "PULL_AMOUNT", nullable = false)
	public BigDecimal getPullAmount() {
		return pullAmount;
	}

	public void setPullAmount(BigDecimal pullAmount) {
		this.pullAmount = pullAmount;
	}

	@Column(name = "COMMENT", nullable = true)
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "PULL_DATE", nullable = false)
	public Date getPullDate() {
		return pullDate;
	}

	public void setPullDate(Date pullDate) {
		this.pullDate = pullDate;
	}

	@Column(name = "PENDING_REASON", nullable = true)
	public String getPendingReason() {
		return pendingReason;
	}

	public void setPendingReason(String pendingReason) {
		this.pendingReason = pendingReason;
	}

	@Column(name = "SOURCE", nullable = false)
	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	@Column(name = "PULL_UNIT", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unit) {
		this.unitId = unit;
	}/*
		 * 
		 * @ManyToOne(fetch = FetchType.LAZY)
		 * 
		 * @JoinColumn(name = "UNIT_CLOSURE_ENTRY", nullable = false) public
		 * UnitClosureEntry getUnitClosure() { return unitClosure; }
		 * 
		 * public void setUnitClosure(UnitClosureEntry unitClosure) {
		 * this.unitClosure = unitClosure; }
		 */

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SETTLEMENT_DETAIL", nullable = true)
	public SettlementDetail getSettlementDetail() {
		return settlementDetail;
	}

	public void setSettlementDetail(SettlementDetail settlementDetail) {
		this.settlementDetail = settlementDetail;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "pullDetail")
	public List<PullDenomination> getPullDenominations() {
		return pullDenominations;
	}

	public void setPullDenominations(List<PullDenomination> pullDenominations) {
		this.pullDenominations = pullDenominations;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CLOSURE_PAYMENT_DETAIL_ID", nullable = false)
	public ClosurePaymentDetails getClosurePaymentDetails() {
		return closurePaymentDetails;
	}

	public void setClosurePaymentDetails(ClosurePaymentDetails closurePaymentDetails) {
		this.closurePaymentDetails = closurePaymentDetails;
	}

}
