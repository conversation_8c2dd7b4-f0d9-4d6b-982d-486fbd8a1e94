/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class SubscriptionNotification extends EmailNotification {

	private final SubscriptionReceipt receipt;

	public SubscriptionNotification() {
		this.receipt = null;
	}

	public SubscriptionNotification(SubscriptionReceipt receipt) {
		this.receipt = receipt;
	}

	public String subject() {
		return (TransactionUtils.isDev(receipt.getDetail().getEnv()) ? EnvType.DEV.name() + " " : "")
				+ "Chai Subscription Receipt No: " + receipt.getDetail().getOrder().getGenerateOrderId();
	}

	public String body() throws EmailGenerationException {

		try {
			return receipt.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	public String getFromEmail() {
		return receipt.getFromEmail();
	}

	public String[] getToEmails() {
		return new String[] { receipt.getDetail().getCustomer().getEmailId(), receipt.getToEmail() };
	}

	@Override
	public EnvType getEnvironmentType() {
		return receipt.getDetail().getEnv();
	}
}
