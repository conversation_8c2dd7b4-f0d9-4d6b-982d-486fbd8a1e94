package com.stpl.tech.kettle.core.service;

import java.util.List;

import com.stpl.tech.kettle.domain.model.TableResponse;
import com.stpl.tech.kettle.domain.model.TableSettlement;
import com.stpl.tech.kettle.domain.model.UnitTableMapping;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.TemplateRenderingException;

public interface TableService {

	public List<UnitTableMapping> getTablesForUnit(int unitId);

	public UnitTableMapping getTableSummary(int tableRequestId);

	public UnitTableMapping reserveTableForUnit(int unitId, int tableNumber) throws DataNotFoundException;

	public boolean closeTableForUnit(int tableRequestId);

	public void tableCheckout(TableSettlement settlement) throws DataUpdationException;

	public TableResponse generateSettlementReceipt(int tableRequestId) throws TemplateRenderingException;

	public void refreshTableSummary(Integer tableRequestId);

	public UnitTableMapping changeTable(int tableRequestId, int tableNumber) throws DataUpdationException;

	public List<Integer> getTableOrders(int tableRequestId);

}
