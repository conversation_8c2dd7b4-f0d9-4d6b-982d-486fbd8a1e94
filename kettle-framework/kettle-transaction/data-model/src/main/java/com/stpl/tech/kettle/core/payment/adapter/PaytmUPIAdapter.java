package com.stpl.tech.kettle.core.payment.adapter;

import com.stpl.tech.kettle.core.payment.PaymentAdapter;
import com.stpl.tech.kettle.core.service.PayTMPaymentService;
import com.stpl.tech.kettle.core.service.impl.PayTMPaymentServiceImpl;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiQrResponse;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiS2SResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class PaytmUPIAdapter extends PaymentAdapter<OrderPaymentRequest,
        PaytmUpiQrResponse> {

    @Autowired
    private PayTMPaymentService payTMPaymentService ;

    @Override
    public PaytmUpiQrResponse createPaymentRequest(OrderPaymentRequest orderPaymentRequest,
                                                   Map<String, String> map) throws Exception {
        return payTMPaymentService.getPayTmUpiQRCodeId(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification,Integer brandId) {
        PaytmUpiS2SResponse paytmUpiS2SResponse = (PaytmUpiS2SResponse) object;
        return payTMPaymentService.updatePaytmUpiStatus(paytmUpiS2SResponse);
    }
}
