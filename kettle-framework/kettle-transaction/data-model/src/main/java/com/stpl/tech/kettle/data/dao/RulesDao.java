/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import java.util.List;

import com.stpl.tech.kettle.data.model.RulesData;
import com.stpl.tech.kettle.data.model.RulesOptionResultData;
import com.stpl.tech.kettle.offer.model.Option;
import com.stpl.tech.kettle.offer.model.OptionResponseData;
import com.stpl.tech.kettle.offer.model.RecommendationDetail;
import com.stpl.tech.kettle.offer.model.RuleData;

public interface RulesDao {

	public void createRule(RuleData rule);

	public List<RulesData> getRules(String status);

	void updateRuleAvailedData(int optionResultDataId, boolean hasDiscount);

	void updateRuleRecommendationData(int optionResultDataId, boolean hasDiscount);
	
	RecommendationDetail create(OptionResponseData data);
	
	void update(RecommendationDetail data);

	void attach(int optionResultEventId, int orderId);


	/**
	 * @param unitId
	 * @param rule
	 * @param option
	 * @return
	 */
	public RulesOptionResultData getOrCreateRulesOptionResultData(int unitId, RuleData rule, Option option);

}
