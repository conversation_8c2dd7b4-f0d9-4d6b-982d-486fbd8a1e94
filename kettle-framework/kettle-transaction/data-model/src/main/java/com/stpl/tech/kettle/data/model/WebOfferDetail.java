package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "WEB_OFFER_DETAIL")
public class WebOfferDetail implements java.io.Serializable{

    private Integer webOfferDetailId;
    private String offerType;
    private Integer customerId;
    private String customerName;
    private String customerContact;
    private String timeOfDelivery;
    private String dateOfDelivery;
    private String completeAddress;
    private String city;
    private String pinCode;
    private Integer productId;
    private String productName;
    private int quantity;
    private BigDecimal price;
    private String dimension;
    private String variants;
    private String addons;
    private Integer recipeId;
    private Integer brandId;
    private Integer unitId;
    private Date creationTime;

    public WebOfferDetail() { }

    public WebOfferDetail(String offerType, Integer customerId, String customerName,
                          String customerContact, String timeOfDelivery,
                          String dateOfDelivery, String completeAddress, String city,
                          String pinCode, Integer brandId, Integer unitId) {
        this.offerType = offerType;
        this.customerId = customerId;
        this.customerName = customerName;
        this.customerContact = customerContact;
        this.timeOfDelivery = timeOfDelivery;
        this.dateOfDelivery = dateOfDelivery;
        this.completeAddress = completeAddress;
        this.city = city;
        this.pinCode = pinCode;
        this.brandId = brandId;
        this.unitId = unitId;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "WEB_OFFER_DETAIL_ID", unique = true, nullable = false)
    public Integer getWebOfferDetailId() {
        return webOfferDetailId;
    }

    public void setWebOfferDetailId(Integer webOfferDetailId) {
        this.webOfferDetailId = webOfferDetailId;
    }

    @Column(name = "OFFER_TYPE", nullable = false)
    public String getOfferType() {
        return offerType;
    }

    public void setOfferType(String offerType) {
        this.offerType = offerType;
    }

    @Column(name = "CUSTOMER_ID", nullable = false)
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "CUSTOMER_NAME")
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    @Column(name = "CUSTOMER_CONTACT", nullable = false)
    public String getCustomerContact() {
        return customerContact;
    }

    public void setCustomerContact(String customerContact) {
        this.customerContact = customerContact;
    }

    @Column(name = "TIME_OF_DELIVERY", nullable = false)
    public String getTimeOfDelivery() {
        return timeOfDelivery;
    }

    public void setTimeOfDelivery(String timeOfDelivery) {
        this.timeOfDelivery = timeOfDelivery;
    }

    @Column(name = "DATE_OF_DELIVERY", nullable = false)
    public String getDateOfDelivery() {
        return dateOfDelivery;
    }

    public void setDateOfDelivery(String dateOfDelivery) {
        this.dateOfDelivery = dateOfDelivery;
    }

    @Column(name = "COMPLETE_ADDRESS", nullable = false)
    public String getCompleteAddress() {
        return completeAddress;
    }

    public void setCompleteAddress(String completeAddress) {
        this.completeAddress = completeAddress;
    }

    @Column(name = "CITY", nullable = false)
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Column(name = "PIN_CODE", nullable = false)
    public String getPinCode() {
        return pinCode;
    }

    public void setPinCode(String pinCode) {
        this.pinCode = pinCode;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME", nullable = false)
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "QUANTITY", nullable = true)
    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    @Column(name = "PRICE", nullable = true)
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "DIMENSION", nullable = false)
    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    @Column(name = "VARIANTS", nullable = true)
    public String getVariants() {
        return variants;
    }

    public void setVariants(String variants) {
        this.variants = variants;
    }

    @Column(name = "ADDONS", nullable = true)
    public String getAddons() {
        return addons;
    }

    public void setAddons(String addons) {
        this.addons = addons;
    }

    @Column(name = "RECIPE_ID", nullable = true)
    public Integer getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(Integer recipeId) {
        this.recipeId = recipeId;
    }

    @Column(name = "BRAND_ID", nullable = false)
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false, length = 19)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }
}
