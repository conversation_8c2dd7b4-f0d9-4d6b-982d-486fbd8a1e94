package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.data.budget.vo.AdjustmentAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ElectricityStaticData.ElectricityBillType;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.ExpenseDetailData;
import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.domain.model.ExpenseDetail;
import com.stpl.tech.kettle.domain.model.MeterDetail;
import com.stpl.tech.kettle.domain.model.UnitBudgetExceeded;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.MeterDetailEntryException;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import org.springframework.web.servlet.View;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ExpenseManagementService {

	public ExpenseDetail addExpense(ExpenseDetail detail);

	public List<ExpenseDetail> getExpenseDetail(Integer unitId, String expenseCategory, String expenseType,
			Date startDate, Date endDate, String status);

	public Map<ElectricityBillType, Map<Integer, Integer>> getMeterReading(int unitId, int month, int year);

	public boolean updateExpenseStatus(ExpenseDetail expenseDetail);

	public boolean addMeterReading(List<MeterDetail> details) throws DataNotFoundException, MeterDetailEntryException;

	public List<Object[]> getLastMeterReading(int unitId);

	public List<ExpenseDetailData> getPnlAccountableExpenses(int unitId, int month, int year);

	public List<ExpenseDetailData> getUnAccountedPnlExpenses(int unitId, int month, int year);

	public List<PnlAdjustmentDetail> getAdjustmentAggregate(int unitId, int month, int year, Date businessDate);

	List<PnlAdjustmentDetail> getMTDAdjustmentAggregate(int unitId, int month, int year);

    List<VoucherData> getUnAccountedPnlVoucherExpenses(Integer unitId);

    List<VoucherData> getAccountedPnlVoucherExpenses(Integer unitId, int month, int year);

    /**
	 * @param unitId
	 * @param pnlDetailId
	 * @param month
	 * @param year
	 */
	public void setPnLDetailId(int unitId, int pnlDetailId, int month, int year);

	public List<UnitBudgetExceeded> getBudgetExceededDetails(int unitId, String notificationType, Date tillDate);
	
	public boolean updateLastMeterReading(List<MeterDetail> details);

	public List<MeterDetail> getLastMeterDetailList(MeterDetail detail);

	public boolean addExpenses(List<ExpenseDetail> detail);

	public View getMTDPnlDetailView(Integer unitId, Date date);

	public View getFinalizedPnlDetailView(Integer unitId, Integer month, Integer year);

	public View getFinalizedPnlAggregateDetailView(Integer unitId, Integer month, Integer year);

	public List<UnitExpenditureDetail> getMTDPnlDetails(Integer unitId, Date date);

    List<MeterDetail> getAllMeterDetailList(int unitId);

    boolean resetAllMeterReadingForUnit(UserSessionDetail sessionDetail);

	public View getMTDPnlAggregateDetailView(Integer unitId, Date date);

	 View getMTDPnlAggregateDetailViewDAM(List<Integer> unitId, Date date);
}
