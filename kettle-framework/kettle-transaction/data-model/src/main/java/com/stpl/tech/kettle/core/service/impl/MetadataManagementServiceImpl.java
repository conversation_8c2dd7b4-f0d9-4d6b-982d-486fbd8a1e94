/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * 
 */
package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.data.vo.VerificationResponse;
import com.stpl.tech.kettle.core.service.MetadataMangementService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.MetadataManagementDao;
import com.stpl.tech.kettle.data.model.DeliveryPartner;
import com.stpl.tech.kettle.data.model.ManualBillBookData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper;
import com.stpl.tech.kettle.domain.model.ManualBillBookStatus;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.ListTypes;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.refLookup.dao.RefLookupDao;
import com.stpl.tech.master.domain.model.BillBookOrder;
import com.stpl.tech.master.domain.model.ConsumptionCodeData;
import com.stpl.tech.master.domain.model.ConsumptionMetadata;
import com.stpl.tech.master.domain.model.DispenserRecipeVariantKey;
import com.stpl.tech.master.domain.model.ExtendedConsumable;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.ListDataTrim;
import com.stpl.tech.master.domain.model.ManualBillBook;
import com.stpl.tech.master.domain.model.PartnerDetail;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.ProductData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper.KADAK;
import static com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper.PATTI;
import static com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper.REGULAR;
import static com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper.REGULAR_PATTI;
import static com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper.REGULAR_SUGAR;
import static com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper.SUGAR;
import static com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper.getDimensionChar;

@Service
@Transactional(rollbackFor = Exception.class, transactionManager = "TransactionDataSourceTM")
@Log4j2
public class MetadataManagementServiceImpl implements MetadataMangementService {

	@Autowired
	private MetadataManagementDao dao;

	@Autowired
	private MetadataCache metadataCache;

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private OrderManagementService orderManagementService;

	@Autowired
	private RefLookupDao refLookupDao;

	@Autowired
	private RecipeCache recipeCache;

	@Autowired
	private OrderInfoCache orderInfoCache;

	private static final Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> consumableCodes = new HashMap<>();
	private static final Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> desiChaiConsumableCodes = new HashMap<>();

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<IdCodeName> getAllDeliveryPartners() {
		List<IdCodeName> list = new ArrayList<>();
		dao.findAll(DeliveryPartner.class).forEach(p -> list.add(DataConverter.convert(p)));
		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public IdCodeName updateDeliveryPartner(DeliveryPartner deliveryPartner) throws DataNotFoundException {
		IdCodeName partner = DataConverter.convert((DeliveryPartner) dao.update(deliveryPartner));
		metadataCache.updateListData(ListTypes.DELIVERY_PARTNERS);
		return partner;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean changeDeliveryPartnerStatus(int partnerId, String status) throws DataNotFoundException {
		DeliveryPartner partner = dao.find(DeliveryPartner.class, partnerId);
		partner.setPartnerStatus(AppUtils.getCorrectStatus(status));
		partner = (DeliveryPartner) dao.update(partner);
		metadataCache.updateListData(ListTypes.DELIVERY_PARTNERS);
		return partner != null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PartnerDetail> getAllDeliveyPartnersForUnit(int unitId) {
		return dao.getAllDeliveyPartnersForUnit(unitId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateDeliveryPartnerForUnit(Unit unit) {
		return dao.updateDeliveryPartnerForUnit(unit);
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean createManualBillBookEntry(ManualBillBook manualBillBook) {
		UnitCategory category = masterCache.getUnitBasicDetail(manualBillBook.getGeneratedForUnitId().getId())
				.getCategory();
		if (!(category.equals(UnitCategory.CAFE))) {
			return true;
		}
		ManualBillBookData manualBillBookData = new ManualBillBookData();
		manualBillBookData.setCreationTime(AppUtils.getCurrentTimestamp());
		manualBillBookData.setUnitId(manualBillBook.getGeneratedForUnitId().getId());
		manualBillBookData.setTransferOrderId(manualBillBook.getTransferOrderId());
		manualBillBookData.setStartNo(manualBillBook.getStartNo());
		manualBillBookData.setEndNo(manualBillBook.getEndNo());
		manualBillBookData.setStatus(ManualBillBookStatus.CREATED.value());
		manualBillBookData.setStateId(
				masterCache.getUnit(manualBillBook.getGeneratedForUnitId().getId()).getLocation().getState().getId());
		dao.add(manualBillBookData);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateManualBillBookEntry(ManualBillBook manualBillBook) {
		ManualBillBookData manualBillBookData = dao.find(ManualBillBookData.class, manualBillBook.getId());
		List<String> statusList = new ArrayList<>();
		statusList.add(ManualBillBookStatus.ACTIVATED.value());
		List<ManualBillBookData> manualBillBookDataList = dao
				.getManualBillBookDetail(manualBillBook.getGeneratedForUnitId().getId(), false);
		if (manualBillBookDataList != null) {
			for (ManualBillBookData mBillBookData : manualBillBookDataList) {
				if (ManualBillBookStatus.ACTIVATED.name().equals(mBillBookData.getStatus())) {
					mBillBookData.setStatus(ManualBillBookStatus.DEACTIVATED.value());
					mBillBookData.setDeActivationTime(AppUtils.getCurrentTimestamp());
					dao.update(mBillBookData);
				}
			}
		}
		manualBillBookData.setStatus(ManualBillBookStatus.ACTIVATED.value());
		manualBillBookData.setActivationTime(AppUtils.getCurrentTimestamp());
		dao.update(manualBillBookData);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<BillBookOrder> getAllOrdersForBillBook(int billBookId) {
		List<BillBookOrder> list = new ArrayList<>();
		List<OrderDetail> orders = dao.getAllOrdersForBillBook(billBookId);
		if (orders == null || orders.size() == 0) {
			return list;
		}
		for (OrderDetail order : orders) {
			list.add(DataConverter.convert(order, masterCache));
		}
		return list;

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ManualBillBook> getManualBillBookDetail(int unitId, boolean getAll) {
		List<ManualBillBookData> manualBillBookDataList = dao.getManualBillBookDetail(unitId, getAll);
		List<ManualBillBook> billBooks = new ArrayList<>();
		if (manualBillBookDataList != null) {
			for (ManualBillBookData manualBillBookData : manualBillBookDataList) {
				if (getAll || manualBillBookData.getStatus().equals(ManualBillBookStatus.ACTIVATED.value())) {
					int usedBillsCount = orderManagementService.remainingBillCount(unitId,
							manualBillBookData.getStartNo(), manualBillBookData.getEndNo());
					billBooks.add(DataConverter.convert(manualBillBookData, usedBillsCount));
				} else {
					billBooks.add(DataConverter.convert(manualBillBookData, 0));
				}
			}
		} else {
			return new ArrayList<>();
		}
		return billBooks;

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean validateManualBillBookEntry(ManualBillBook manualBillBook) {
		int stateId = masterCache.getUnit(manualBillBook.getGeneratedForUnitId().getId()).getLocation().getState()
				.getId();
		List<ManualBillBookData> list = dao.validateManualBillBookDetail(manualBillBook, stateId);
		if (list.size() == 0) {
			return true;
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public VerificationResponse validateManualBillBookNo(int billBookNo, int unitId) {
		VerificationResponse response = new VerificationResponse();
		List<ManualBillBookData> manualBillBookDataList = dao.validateManualBillBookNo(billBookNo, unitId);
		if (manualBillBookDataList.size() > 0) {
			int count = orderManagementService.validateBillBookNo(unitId, billBookNo);
			if (count == 0) {
				response.setSuccess(true);
			} else {
				response.setReasonForFailure("The Bill Number " + billBookNo + " is already used");
			}
		} else {
			response.setReasonForFailure("There is no Valid Manual Book Available for the unit");
		}
		return response;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean cancelManualBillBookEntry(int transferOrderId) {
		ManualBillBookData manualBillBookData = dao.getBillBook(transferOrderId);
		dao.delete(manualBillBookData);
	
		return true;
	}

	public Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> createConsumableCodes() {
		recipeCache.getRecipeMap();
		Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> consumableCodesData = DesiChaiConsumptionHelper.consumableCodes();
		log.info("::::::: Adding SACHET CONSUMABLE CODES (Recipe Based Chai) :::::::");
		createConsumableCodesForRecipeHotBeverages(consumableCodesData);
		return consumableCodesData;
	}

	@Override
	public List<ListData> getListDataByGroupName(String gName) {
		try{
			return refLookupDao.getAllListData(gName, true);
		}catch (DataNotFoundException e){
			log.info("No list data found for Group name : {}", gName);
		}
		return new ArrayList<>();
	}

	@Override
	public List<ListDataTrim> getListDataByGroupNameAndCat(String gName, String cat) {
		List<ListDataTrim> listDataTrims = new ArrayList<>();
		List<ListData> dataList = getListDataByGroupName(gName);
		for(ListData listData : dataList){
			if(cat.equals(listData.getDetail().getCode())){
				for(IdCodeName idCodeName : listData.getContent()){
					if(!AppConstants.ACTIVE.equals(idCodeName.getStatus())) {
						continue;
					}
					listDataTrims.add(ListDataTrim.builder()
							.name(idCodeName.getName())
							.code(idCodeName.getCode())
							.categoryName(listData.getDetail().getCode())
							.shortCode(idCodeName.getShortCode())
							.build());
				}
				break;
			}
		}
		return listDataTrims;
	}

	@Override
	public Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> getConsumableCodes(String allSachetsFlag) {
		if (Objects.nonNull(allSachetsFlag) && AppConstants.ALL.equalsIgnoreCase(allSachetsFlag)) {
			if (Objects.isNull(consumableCodes) || consumableCodes.isEmpty()) {
				refreshConsumableCodes();
			}
			return consumableCodes;
		} else {
			if (Objects.isNull(desiChaiConsumableCodes) || desiChaiConsumableCodes.isEmpty()) {
				desiChaiConsumableCodes.putAll(DesiChaiConsumptionHelper.consumableCodes());
			}
			return desiChaiConsumableCodes;
		}
	}

	@Override
	@Scheduled(cron = "0 15 06 * * *", zone = "GMT+05:30")
	public boolean refreshConsumableCodes() {
		try {
			consumableCodes.clear();
			log.info("::::::: Adding SACHET CONSUMABLE CODES :::::::");
			consumableCodes.putAll(createConsumableCodes());
			log.info("::::::: SACHET CONSUMABLE CODES creation complete :::::::");
			createDispenserConsumptionCodeData();
		} catch (Exception e) {
			log.info("Error while refreshing consumables codes", e);
			return false;
		}
		return true;
	}

	@Override
	public Map<DispenserRecipeVariantKey, ConsumptionCodeData> getDispenserConsumableCodes() {
		return orderInfoCache.getDispenserConsumptionCodeDataMap();
	}

	@Override
	public Map<DispenserRecipeVariantKey, ConsumptionCodeData> refreshDispenserConsumableCodes() {
		return createDispenserConsumptionCodeData();
	}

	private Map<DispenserRecipeVariantKey, ConsumptionCodeData> createDispenserConsumptionCodeData() {
		try {
			log.info("Creating Dispenser Consumable Codes");
			Map<DispenserRecipeVariantKey, ConsumptionCodeData> dispenserConsumptionCodeDataMap = new HashMap<>();
			Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> consumableCodesData = DesiChaiConsumptionHelper.consumableCodes();
			recipeCache.getRecipeMap().forEach((recipeId, recipe) -> {
				if (Objects.nonNull(recipe) && AppConstants.ACTIVE.equalsIgnoreCase(recipe.getStatus())) {
					ProductData productData = recipe.getProduct();
					if (Objects.nonNull(productData)) {
						Product product = masterCache.getProduct(productData.getProductId());
						if (Objects.nonNull(product)) {
							if (product.getType() == 5) {
								try {
									log.info("Creating Dispenser Consumable Codes for Recipe Id: {} and Product Id : {}", recipeId, product.getId());
									createDispenserConsumptionCodeDataByRecipe(recipe, consumableCodesData, product, dispenserConsumptionCodeDataMap);
								} catch (Exception e) {
									log.error("Error while creating dispenser consumable codes for recipe id: " + recipeId, e);
								}
							}
						} else {
							log.error("Product data not found in master cache for recipe id: {} and Product Id : {} ", recipeId, productData.getProductId());
						}
					} else {
						log.error("Product data not found for recipe id: {} ", recipeId);
					}
				}
			});
			orderInfoCache.clearDispenserConsumptionCodeDataMap();
			orderInfoCache.getDispenserConsumptionCodeDataMap().putAll(dispenserConsumptionCodeDataMap);
			return dispenserConsumptionCodeDataMap;
		} catch (Exception e) {
			log.error("Error while creating dispenser consumable codes", e);
			return new HashMap<>();
		}
	}

	private void createDispenserConsumptionCodeDataByRecipe(RecipeDetail recipe, Map<String, Map<Integer,
			Map<String, ConsumptionMetadata>>> consumableCodesData, Product product, Map<DispenserRecipeVariantKey, ConsumptionCodeData> dispenserConsumptionCodeDataMap) {
		// TODO need to get all Variants and loop on it and keep in the data -> as of now doing for Oat Milk
		for (String milkVariant : DesiChaiConsumptionHelper.getMilkVariantsList()) {
			String recipeProfile = milkVariant + recipe.getProfile();
			if (DesiChaiConsumptionHelper.getInstance(recipeProfile).getProducts().contains(product.getId())) {
				log.info("Desi Chai Consumption helper Trying to add Normal Milk Sachet Data for Recipe Id: {} for Product Id : {} and recipe Profile : {}"	,
						recipe.getRecipeId(), product.getId(), recipeProfile);
				addToDispenserConsumptionCodeData(recipe, consumableCodesData, product, dispenserConsumptionCodeDataMap, recipeProfile);
			}
		}
		ConsumptionMetadata sachetData = createSachetData(recipe);
		if (Objects.isNull(sachetData)) {
			log.info("Sachet Data is NULL for Recipe Id: {}", recipe.getRecipeId());
		} else {
			setConsumptionCodeForAllMilkVariants(recipe, dispenserConsumptionCodeDataMap, sachetData);
		}
	}

	private void setConsumptionCodeForAllMilkVariants(RecipeDetail recipe, Map<DispenserRecipeVariantKey, ConsumptionCodeData> dispenserConsumptionCodeDataMap, ConsumptionMetadata sachetData) {
		for (String milkVariantKey : DesiChaiConsumptionHelper.getMilkVariantsList()) {
			DispenserRecipeVariantKey regularPattiKey = new DispenserRecipeVariantKey(recipe.getRecipeId(), milkVariantKey + recipe.getProfile(), PATTI, REGULAR);
			if (!dispenserConsumptionCodeDataMap.containsKey(regularPattiKey) && Objects.nonNull(sachetData.getPatti().get(REGULAR))) {
				dispenserConsumptionCodeDataMap.put(regularPattiKey, sachetData.getPatti().get(REGULAR));
			}
			DispenserRecipeVariantKey kadakPattiKey = new DispenserRecipeVariantKey(recipe.getRecipeId(), milkVariantKey + recipe.getProfile(), PATTI, KADAK);
			if (!dispenserConsumptionCodeDataMap.containsKey(kadakPattiKey) && Objects.nonNull(sachetData.getPatti().get(KADAK))) {
				dispenserConsumptionCodeDataMap.put(kadakPattiKey, sachetData.getPatti().get(KADAK));
			}

			DispenserRecipeVariantKey regularSugarKey = new DispenserRecipeVariantKey(recipe.getRecipeId(),milkVariantKey + recipe.getProfile(), SUGAR, REGULAR);
			if (!dispenserConsumptionCodeDataMap.containsKey(regularSugarKey) && Objects.nonNull(sachetData.getSugar().get(REGULAR))) {
				dispenserConsumptionCodeDataMap.put(regularSugarKey, sachetData.getSugar().get(REGULAR));
			}
		}
	}

	private static void addToDispenserConsumptionCodeData(RecipeDetail recipe,
														  Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> consumableCodesData,
														  Product product, Map<DispenserRecipeVariantKey, ConsumptionCodeData> dispenserConsumptionCodeDataMap,
														  String recipeProfile) {
		if (consumableCodesData.containsKey(recipeProfile)) {
			if (consumableCodesData.get(recipeProfile).containsKey(product.getId())) {
				if (consumableCodesData.get(recipeProfile).get(product.getId()).containsKey(recipe.getDimension().getCode())) {
					ConsumptionMetadata consumptionMetadata = consumableCodesData.get(recipeProfile).get(product.getId()).get(recipe.getDimension().getCode());
					DispenserRecipeVariantKey regularPattiKey = new DispenserRecipeVariantKey(recipe.getRecipeId(), recipeProfile, PATTI, REGULAR);
					if (!dispenserConsumptionCodeDataMap.containsKey(regularPattiKey) && Objects.nonNull(consumptionMetadata.getPatti().get(REGULAR))) {
						dispenserConsumptionCodeDataMap.put(regularPattiKey, consumptionMetadata.getPatti().get(REGULAR));
					}

					DispenserRecipeVariantKey kadakPattiKey = new DispenserRecipeVariantKey(recipe.getRecipeId(), recipeProfile, PATTI, KADAK);
					if (!dispenserConsumptionCodeDataMap.containsKey(kadakPattiKey) && Objects.nonNull(consumptionMetadata.getPatti().get(KADAK))) {
						dispenserConsumptionCodeDataMap.put(kadakPattiKey, consumptionMetadata.getPatti().get(KADAK));
					}

					DispenserRecipeVariantKey regularSugarKey = new DispenserRecipeVariantKey(recipe.getRecipeId(), recipeProfile, SUGAR, REGULAR);
					if (!dispenserConsumptionCodeDataMap.containsKey(regularSugarKey) && Objects.nonNull(consumptionMetadata.getSugar().get(REGULAR))) {
						dispenserConsumptionCodeDataMap.put(regularSugarKey, consumptionMetadata.getSugar().get(REGULAR));
					}
				}
			}
		}
	}

	private void createConsumableCodesForRecipeHotBeverages(Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> consumableCodes) {
		Map<ProductRecipeKey, RecipeDetail> recipes = recipeCache.getRecipes();
		for (Map.Entry<ProductRecipeKey, RecipeDetail> recipeEntry : recipes.entrySet()) {
			RecipeDetail recipe = recipeEntry.getValue();
			if (Objects.nonNull(recipe) && Objects.nonNull(recipe.getProduct()) && recipe.getProduct().getType() == 5) {
				createConsumableCodeMapByRecipe(recipe, consumableCodes);
			}
		}
	}

	private static void createConsumableCodeMapByRecipe(RecipeDetail recipe, Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> output) {
		String profile = recipe.getProfile();
		int productId = recipe.getProduct().getProductId();
		String dimension = recipe.getDimension().getCode();
		if (output.containsKey(profile)) {
			if (output.get(profile).containsKey(productId)) {
				if (output.get(profile).get(productId).containsKey(dimension)) {
					return;
				} else {
					ConsumptionMetadata sachetData = createSachetData(recipe);
					if (Objects.nonNull(sachetData)) {
						output.get(profile).get(productId).put(dimension, sachetData);
					}
				}
			} else {
				ConsumptionMetadata sachetData = createSachetData(recipe);
				if (Objects.nonNull(sachetData)) {
					output.get(profile).put(productId, new HashMap<String, ConsumptionMetadata>() {{
						put(dimension, sachetData);
					}});
				}
			}
		} else {
			ConsumptionMetadata sachetData = createSachetData(recipe);
			if (Objects.nonNull(sachetData)) {
				output.put(profile, new HashMap<Integer, Map<String, ConsumptionMetadata>>() {{
					put(productId, new HashMap<String, ConsumptionMetadata>() {{
						put(dimension, sachetData);
					}});
				}});
			}
		}
	}

	private static ConsumptionMetadata createSachetData(RecipeDetail recipe) {
		try {
			ConsumptionMetadata sachetData = new ConsumptionMetadata();
			Map<Integer, String> consumableCodeColorMap = DesiChaiConsumptionHelper.getConsumableCodeColorMap();
			Map<Integer, ExtendedConsumable> scmProducts = DesiChaiConsumptionHelper.getScmProducts();

			for (IngredientProductDetail component : recipe.getIngredient().getComponents()) {
				if (consumableCodeColorMap.containsKey(component.getProduct().getProductId())) {
					ExtendedConsumable consumable = scmProducts.get(component.getProduct().getProductId());
					if (Objects.nonNull(consumable)) {
                        ConsumptionCodeData codeData = new ConsumptionCodeData(consumableCodeColorMap.get(consumable.getProductId()),
                                component.getProduct().getName(), consumable.getProductId(), component.getQuantity(),
                                getDimensionChar(recipe.getDimension().getCode(), recipe.getProduct().getProductId()));
						if (PATTI.equalsIgnoreCase(consumable.getType()) && !sachetData.getPatti().containsKey(REGULAR)) {
							sachetData.getPatti().put(REGULAR, codeData);
							logSachetAddData(recipe, REGULAR_PATTI, codeData);
						} else if (SUGAR.equalsIgnoreCase(consumable.getType()) && !sachetData.getSugar().containsKey(REGULAR)) {
							sachetData.getSugar().put(REGULAR, codeData);
							logSachetAddData(recipe, REGULAR_SUGAR, codeData);
						}
					}
				}
			}

			for (IngredientVariant variant : recipe.getIngredient().getVariants()) {
				if (consumableCodeColorMap.containsKey(variant.getProduct().getProductId())) {
					ExtendedConsumable consumable = scmProducts.get(variant.getProduct().getProductId());
					if (Objects.nonNull(consumable)) {
						for (IngredientVariantDetail detail : variant.getDetails()) {
                            ConsumptionCodeData codeData = new ConsumptionCodeData(consumableCodeColorMap.get(consumable.getProductId()),
                                    variant.getProduct().getName(), consumable.getProductId(), detail.getQuantity(),
                                    getDimensionChar(recipe.getDimension().getCode(), recipe.getProduct().getProductId()));
							if (REGULAR_PATTI.equalsIgnoreCase(detail.getAlias()) && !sachetData.getPatti().containsKey(REGULAR)) {
								sachetData.getPatti().put(REGULAR, codeData);
								logSachetAddData(recipe, REGULAR_PATTI, codeData);
							} else if (KADAK.equalsIgnoreCase(detail.getAlias()) && !sachetData.getPatti().containsKey(KADAK)) {
								sachetData.getPatti().put(KADAK, codeData);
								logSachetAddData(recipe, KADAK, codeData);
							} else if (REGULAR_SUGAR.equalsIgnoreCase(detail.getAlias()) && !sachetData.getSugar().containsKey(REGULAR)) {
								sachetData.getSugar().put(REGULAR, codeData);
								logSachetAddData(recipe, REGULAR_SUGAR, codeData);
							}
						}
					}
				}
			}
			addPaidAddonSachets(sachetData, recipe.getDimension().getCode());
			if (sachetData.getPatti().isEmpty() && sachetData.getSugar().isEmpty()) {
				return null;
			}
			return sachetData;
		} catch (Exception e) {
			log.error("Error while creating sachet for recipe id: " + recipe.getRecipeId(), e);
			return null;
		}
	}

	private static void addPaidAddonSachets(ConsumptionMetadata consumptionMetadata, String dimension) {
		consumptionMetadata.setGur((
				Objects.nonNull(DesiChaiConsumptionHelper.getGurConsMetadataMap())
						&& Objects.nonNull(DesiChaiConsumptionHelper.getGurConsMetadataMap().get(dimension))) ?
				DesiChaiConsumptionHelper.getGurConsMetadataMap().get(dimension).getGur()
				: new HashMap<>());
		consumptionMetadata.setHoney(
				(Objects.nonNull(DesiChaiConsumptionHelper.getHoneyConsMetadataMap())
						&& Objects.nonNull(DesiChaiConsumptionHelper.getHoneyConsMetadataMap().get(dimension)))
						? DesiChaiConsumptionHelper.getHoneyConsMetadataMap().get(dimension).getHoney()
						: new HashMap<>());
		consumptionMetadata.setSugarFree(
				(Objects.nonNull(DesiChaiConsumptionHelper.getSugarFreeConsMetadataMap())
						&& Objects.nonNull(DesiChaiConsumptionHelper.getSugarFreeConsMetadataMap().get(dimension)))
						? DesiChaiConsumptionHelper.getSugarFreeConsMetadataMap().get(dimension).getSugarFree()
						: new HashMap<>());
	}

	private static void logSachetAddData(RecipeDetail recipe, String type, ConsumptionCodeData codeData) {
		log.info(String.format("CONSUMABLE CODE SACHET:: %s, %s, %d, %s :::: %s: %s, %s, %s", recipe.getRecipeId(),
				recipe.getProfile(), recipe.getProduct().getProductId(), recipe.getDimension().getCode(),
				type, codeData.getName(), codeData.getColor(), codeData.getQty()));
	}
}
