package com.stpl.tech.kettle.core.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import org.springframework.stereotype.Service;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.stpl.tech.kettle.core.service.QRGenerationService;

@Service
public class QRGenerationServiceImpl implements QRGenerationService {

	@Override
	public byte[] getQRCodeImage(String text, int width, int height) throws WriterException, IOException {
		QRCodeWriter qrCodeWriter = new QRCodeWriter();

		BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height);

		ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();

		MatrixToImageWriter.writeToStream(bitMatrix, "PNG", pngOutputStream);

		byte[] pngData = pngOutputStream.toByteArray();

		return pngData;
	}

	public static void main(String[] args) throws WriterException, IOException {
		QRGenerationServiceImpl impl = new QRGenerationServiceImpl();
		System.out.println(new String(impl.getQRCodeImage("https://cafes.chaayos.com", 100, 100)));
	}
}
