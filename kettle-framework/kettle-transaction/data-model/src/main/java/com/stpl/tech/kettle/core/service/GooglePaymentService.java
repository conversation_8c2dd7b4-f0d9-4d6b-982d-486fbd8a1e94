package com.stpl.tech.kettle.core.service;

import java.io.IOException;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentRequest;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentStatus;
import com.stpl.tech.master.payment.model.gpay.GPayQRResponse;
import com.stpl.tech.util.EnvType;

public interface GooglePaymentService {

	String generateAccessToken(EnvType env) throws IOException;

	GPayPaymentStatus checkPaymentStatus(EnvType env, OrderPaymentRequest request) throws IOException;

	GPayPaymentRequest createRequest(OrderPaymentRequest order);

	GPayQRResponse createGPayQR(EnvType env, GPayPaymentRequest request) throws IOException;

    GPayQRResponse getGPayQRCode(OrderPaymentRequest order) throws PaymentFailureException;

    GPayPaymentStatus checkGPayQRPaymentStatus(OrderPaymentRequest request) throws PaymentFailureException;

    OrderPayment refundRequest(EnvType envType, OrderPayment request) throws PaymentFailureException;
}
