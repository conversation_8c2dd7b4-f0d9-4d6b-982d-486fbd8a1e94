/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.domain.model.OrderStatus;

public class NewOrderNotification {

	private String timestamp;
	private Map<Integer, OrderStatus> orderStatus = new HashMap<>();
	private List<OrderInfo> orders = new ArrayList<OrderInfo>();
	private Map<String, Map<String, String>> orderItemStatus = new HashMap<>();


	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}

	public List<OrderInfo> getOrders() {
		return orders;
	}

	public void setOrders(List<OrderInfo> orders) {
		this.orders = orders;
	}

	public Map<Integer, OrderStatus> getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(Map<Integer, OrderStatus> orderStatus) {
		this.orderStatus = orderStatus;
	}

	public Map<String, Map<String, String>> getOrderItemStatus() {
		return orderItemStatus;
	}

	public void setOrderItemStatus(Map<String, Map<String, String>> orderItemStatus) {
		this.orderItemStatus = orderItemStatus;
	}
}
