/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.service.UnitInventoryManagementService;
import com.stpl.tech.kettle.data.dao.UnitInventoryDao;
import com.stpl.tech.kettle.domain.model.InventoryUpdateEvent;
import com.stpl.tech.kettle.domain.model.ProductInventory;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.IdCodeName;

@Service
public class UnitInventoryManagementServiceImpl implements UnitInventoryManagementService {

	@Autowired
	private UnitInventoryDao dao;

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductInventory> getUnitInventory(int unitId) throws DataNotFoundException {
		return dao.getUnitInventory(unitId);
	}
	
	/* (non-Javadoc)
	 * @see com.stpl.tech.kettle.core.service.UnitInventoryManagementService#getUnitInventory(int, java.util.List)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductInventory> getUnitInventory(int unitId, List<Integer> productIds) throws DataNotFoundException {
		return dao.getUnitInventory(unitId, productIds);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, Integer> getUnitInventoryForWeb(int unitId) throws DataNotFoundException {
		return dao.getUnitInventoryForWeb(unitId);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductInventory> getUnitInventoryForProducts(int unitId, List<Integer> productIds)
			throws DataNotFoundException {
		return dao.getUnitInventoryForProducts(unitId, productIds);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, Integer> getUnitInventoryForProductsForWeb(int unitId, List<Integer> productIds)
			throws DataNotFoundException {
		return dao.getUnitInventoryForProductsForWeb(unitId, productIds);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean stockOutInventory(int unitId, int productId) throws DataUpdationException {
		return dao.stockOutInventory(unitId, productId);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateUnitInventory(InventoryUpdateEvent updateData, boolean generateEvents,
			boolean incrementalUpdate, int employeeId, Integer orderId, boolean isCancellation) {
		boolean inventoryUpdated = dao.updateUnitInventory(updateData, incrementalUpdate, employeeId, orderId, isCancellation);
		if (inventoryUpdated && generateEvents) {
			inventoryUpdated = dao.addInventoryEvent(updateData);
		}
		return inventoryUpdated;
	}

	@Override
	public Collection<IdCodeName> getDeliveryPartners(int unitId) {
		return dao.getUnitToDeliveryMappings(unitId);
	}

}
