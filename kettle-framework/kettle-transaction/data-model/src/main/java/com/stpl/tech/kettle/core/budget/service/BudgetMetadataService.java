package com.stpl.tech.kettle.core.budget.service;

import com.stpl.tech.kettle.core.CalculationType;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.CalculationStatus;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumableBudgetRequest;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface BudgetMetadataService {

    public View getBudgetView();

    public boolean uploadBudgetDocument(MultipartFile file, int userId) throws IOException;

    public List<Integer> getPnLUnits(Date d);

    public boolean savePnLData(List<BudgetDetail> budgetDetails, CalculationStatus status, CalculationType type);

    public Map<String, Map<String, BigDecimal>> applyBudgetConstraintRO(ConsumableBudgetRequest budgetRequest);

    public List<Integer> getFinalizedPnLUnits(Date d);

    boolean uploadManpowerBudgetDocument(MultipartFile file, int userId) throws IOException;

    public View getBudgetView(int year, int month);

    View getManpowerBudgetView(int year, int month);

    View getChannelPartnerChargesBudgetView(int year, int month);

    public boolean uploadChannelPartnerChargesBudgetDocument(MultipartFile file, int userId) throws IOException;

    public View getBankChargesBudgetView(int year, int month);

    public boolean uploadBankChargesBudgetDocument(MultipartFile file, int userId) throws IOException;

    boolean uploadFacilityChargesBudgetDocument(MultipartFile file, int userId) throws IOException;

    boolean uploadServiceChargesBudgetDocument(MultipartFile file, int userId) throws IOException;

    View getFacilityChargesBudgetView(int year, int month);

    View getServiceChargesBudgetView(int year, int month);

}
