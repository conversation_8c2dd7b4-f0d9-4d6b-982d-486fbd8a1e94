package com.stpl.tech.kettle.data.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "RULES_EVENT_DATA")
public class RulesEventData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4914747391121990329L;

	private Integer rulesEventDataId;

	private int optionResultDataId;

	private String productList;
	
	private String stockOutProductList;

	private String availed;

	private String couponCode;

	private String triggeredBy;

	private Date eventTime;

	private Integer orderId;
	
	private Integer quantity;

	private String newCustomer;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "RULES_EVENT_DATA_ID", unique = true, nullable = false)
	public Integer getRulesEventDataId() {
		return rulesEventDataId;
	}

	public void setRulesEventDataId(Integer rulesEventDataId) {
		this.rulesEventDataId = rulesEventDataId;
	}

	@Column(name = "OPTION_RESULT_DATA_ID", nullable = false)
	public int getOptionResultDataId() {
		return optionResultDataId;
	}

	public void setOptionResultDataId(int optionResultDataId) {
		this.optionResultDataId = optionResultDataId;
	}

	@Column(name = "PRODUCT_LIST", nullable = true, length = 1000)
	public String getProductList() {
		return productList;
	}

	public void setProductList(String productList) {
		this.productList = productList;
	}

	@Column(name = "STOCK_OUT_LIST", nullable = true, length = 1000)
	public String getStockOutProductList() {
		return stockOutProductList;
	}

	public void setStockOutProductList(String stockOutProductList) {
		this.stockOutProductList = stockOutProductList;
	}
	
	@Column(name = "AVAILED", nullable = true, length = 1)
	public String getAvailed() {
		return availed;
	}

	public void setAvailed(String availed) {
		this.availed = availed;
	}

	@Column(name = "COUPON_CODE", nullable = true, length = 20)
	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	@Column(name = "TRIGGERED_BY", nullable = true, length = 20)
	public String getTriggeredBy() {
		return triggeredBy;
	}

	public void setTriggeredBy(String triggeredBy) {
		this.triggeredBy = triggeredBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_TIME", nullable = true, length = 19)
	public Date getEventTime() {
		return eventTime;
	}

	public void setEventTime(Date eventTime) {
		this.eventTime = eventTime;
	}

	@Column(name = "ORDER_ID", nullable = true)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "NEW_CUSTOMER", nullable = true, length = 1)
	public String getNewCustomer() {
		return newCustomer;
	}

	public void setNewCustomer(String newCustomer) {
		this.newCustomer = newCustomer;
	}
	
	@Column(name = "QUANTITY", nullable = true)
	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	
}
