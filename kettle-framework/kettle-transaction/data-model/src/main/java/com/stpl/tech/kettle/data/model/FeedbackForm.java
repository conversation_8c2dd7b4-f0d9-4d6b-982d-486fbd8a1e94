/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "FEEDBACK_FORM")
public class FeedbackForm implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -9172973213533192171L;
	
	private Integer formId;
	private String formSourceId;
	private String formTitle;
	private List<FeedbackField> formFields = new ArrayList<FeedbackField>(0);

	public FeedbackForm() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "FORM_ID", unique = true, nullable = false)
	public Integer getFormId() {
		return this.formId;
	}

	public void setFormId(Integer feedbackFieldId) {
		this.formId = feedbackFieldId;
	}


	@Column(name = "FORM_SOURCE_ID", nullable = false, length = 100)
	public String getFormSourceId() {
		return formSourceId;
	}

	public void setFormSourceId(String fieldType) {
		this.formSourceId = fieldType;
	}

	@Column(name = "FORM_TITLE", nullable = false, length = 500)
	public String getFormTitle() {
		return formTitle;
	}

	public void setFormTitle(String fieldTitle) {
		this.formTitle = fieldTitle;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "feedbackForm")
	public List<FeedbackField> getFormFields() {
		return formFields;
	}

	public void setFormFields(List<FeedbackField> formFields) {
		this.formFields = formFields;
	}
	
	

}