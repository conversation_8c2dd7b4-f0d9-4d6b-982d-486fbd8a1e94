/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.impl;

import java.math.BigDecimal;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.core.service.EmployeeMealService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.PaidEmployeeMealDao;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.util.AppConstants;

@Service
public class EmployeeMealServiceImpl implements EmployeeMealService {

	private static final Logger LOG = LoggerFactory.getLogger(EmployeeMealServiceImpl.class);

	@Autowired
	private PaidEmployeeMealDao paidEmpoyeeMealDao;

	@Autowired
	private EnvironmentProperties env;

	@Override
	public BigDecimal getEmployeeAllowanceLimit(int userId) {
		BigDecimal limit = paidEmpoyeeMealDao.getAvailableAllownaceLimit(userId);
		if (BigDecimal.ZERO.compareTo(limit) > 0) {
			String msg = "ERROR: Negative Limits!\n User " + userId + " has achived negative limits."
					+ limit.setScale(2, BigDecimal.ROUND_HALF_UP) + " Please validate";
			SlackNotificationService.getInstance().sendNotification(env.getEnvironmentType(), AppConstants.KETTLE,
					SlackNotification.SYSTEM_ERRORS, msg);
			LOG.error(msg);
			limit = BigDecimal.ZERO;
		}
		return limit;
	}

}
