/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 19 Aug, 2015 4:37:51 PM by Hibernate Tools 4.0.0

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * ReportDefinition generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "REPORT_DEFINITION")
public class ReportDefinition implements java.io.Serializable {

	private Integer reportDefId;
	private String reportName;
	private String reportDescription;
	private String reportType;
	private String autoTriggered;
	private String reportFrequency;
	private String hasMultipleSections;
	private Set<ReportParams> reportParamses = new HashSet<ReportParams>(0);
	private Set<ReportAttributes> reportAttributeses = new HashSet<ReportAttributes>(0);
	private Set<ReportExecutionDetail> reportExecutionDetails = new HashSet<ReportExecutionDetail>(0);

	public ReportDefinition() {
	}

	public ReportDefinition(String reportName, String reportDescription, String reportType, String autoTriggered,
			String reportFrequency, String hasMultipleSections) {
		this.reportName = reportName;
		this.reportDescription = reportDescription;
		this.reportType = reportType;
		this.autoTriggered = autoTriggered;
		this.reportFrequency = reportFrequency;
		this.hasMultipleSections = hasMultipleSections;
	}

	public ReportDefinition(String reportName, String reportDescription, String reportType, String autoTriggered,
			String reportFrequency, String hasMultipleSections, Set<ReportParams> reportParamses,
			Set<ReportAttributes> reportAttributeses, Set<ReportExecutionDetail> reportExecutionDetails) {
		this.reportName = reportName;
		this.reportDescription = reportDescription;
		this.reportType = reportType;
		this.autoTriggered = autoTriggered;
		this.reportFrequency = reportFrequency;
		this.hasMultipleSections = hasMultipleSections;
		this.reportParamses = reportParamses;
		this.reportAttributeses = reportAttributeses;
		this.reportExecutionDetails = reportExecutionDetails;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "REPORT_DEF_ID", unique = true, nullable = false)
	public Integer getReportDefId() {
		return this.reportDefId;
	}

	public void setReportDefId(Integer reportDefId) {
		this.reportDefId = reportDefId;
	}

	@Column(name = "REPORT_NAME", nullable = false, length = 200)
	public String getReportName() {
		return this.reportName;
	}

	public void setReportName(String reportName) {
		this.reportName = reportName;
	}

	@Column(name = "REPORT_DESCRIPTION", nullable = false, length = 2000)
	public String getReportDescription() {
		return this.reportDescription;
	}

	public void setReportDescription(String reportDescription) {
		this.reportDescription = reportDescription;
	}

	@Column(name = "REPORT_TYPE", nullable = false, length = 30)
	public String getReportType() {
		return this.reportType;
	}

	public void setReportType(String reportType) {
		this.reportType = reportType;
	}

	@Column(name = "AUTO_TRIGGERED", nullable = false, length = 1)
	public String getAutoTriggered() {
		return this.autoTriggered;
	}

	public void setAutoTriggered(String autoTriggered) {
		this.autoTriggered = autoTriggered;
	}

	@Column(name = "REPORT_FREQUENCY", nullable = false, length = 20)
	public String getReportFrequency() {
		return this.reportFrequency;
	}

	public void setReportFrequency(String reportFrequency) {
		this.reportFrequency = reportFrequency;
	}

	@Column(name = "HAS_MULTIPLE_SECTIONS", nullable = false, length = 1)
	public String getHasMultipleSections() {
		return this.hasMultipleSections;
	}

	public void setHasMultipleSections(String hasMultipleSections) {
		this.hasMultipleSections = hasMultipleSections;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "reportDefinition")
	public Set<ReportParams> getReportParamses() {
		return this.reportParamses;
	}

	public void setReportParamses(Set<ReportParams> reportParamses) {
		this.reportParamses = reportParamses;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "reportDefinition")
	public Set<ReportAttributes> getReportAttributeses() {
		return this.reportAttributeses;
	}

	public void setReportAttributeses(Set<ReportAttributes> reportAttributeses) {
		this.reportAttributeses = reportAttributeses;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "reportDefinition")
	public Set<ReportExecutionDetail> getReportExecutionDetails() {
		return this.reportExecutionDetails;
	}

	public void setReportExecutionDetails(Set<ReportExecutionDetail> reportExecutionDetails) {
		this.reportExecutionDetails = reportExecutionDetails;
	}

}
