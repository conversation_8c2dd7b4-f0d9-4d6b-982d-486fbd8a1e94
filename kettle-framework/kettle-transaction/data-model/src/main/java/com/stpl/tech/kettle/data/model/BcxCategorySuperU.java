package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "BCX_CATEGORY_SUPERU")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BcxCategorySuperU implements Serializable {

    private static final long serialVersionUID = 7326597795820677814L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BCX_CATEGORY_SUPERU_ID")
    Integer bcxCategorySuperuId;

    @Column(name = "CATEGORY_NAME")
    String categoryName;

    @Column(name = "CATEGORY_ALIAS")
    String categoryAlias;

    @Column(name = "RATING_FACTOR")
    Integer ratingFactor;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "SUPERU_CATEGORY_TO_SUBCATEGORY_MAPPING",
            joinColumns =  @JoinColumn(name  = "BCX_CATEGORY_SUPERU_ID"), inverseJoinColumns = @JoinColumn(name = "BCX_SUB_CATEGORY_SUPERU_ID"))
    List<BcxSubCategorySuperU> subCategorySuperu;

    @Column(name = "FEEDBACK")
    String feedback;

    @Column(name = "PRIORITY_ORDER")
    Integer priorityOrder;

    @Column(name = "DEFAULT_FEEDBACK")
    String defaultFeedBack;

    @Transient
    BigDecimal weight;
    @Transient
    BigDecimal value;
    @Transient
    BigDecimal categoryRating;

    @Transient
    boolean isApplicable = true;

}
