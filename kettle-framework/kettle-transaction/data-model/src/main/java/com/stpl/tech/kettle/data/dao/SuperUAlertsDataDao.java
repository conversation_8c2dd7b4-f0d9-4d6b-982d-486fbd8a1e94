package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.SuperUAlertsData;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SuperUAlertsDataDao extends JpaRepository<SuperUAlertsData,Integer> {

    @Query("select e from SuperUAlertsData e where e.unitId= :unitId ORDER BY e.timestamp DESC")
    Page<SuperUAlertsData> findRecentAlertForUnitId(@Param("unitId") Integer unitId, Pageable pageable);

}
