package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.service.ForwardRequestService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Enumeration;

/**
 * <AUTHOR>
 */
@Service
public class ForwardRequestServiceImpl implements ForwardRequestService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ForwardRequestServiceImpl.class);

    /**
     * This method will forward the request received to the forwardToUrl server for both POST and GET
     * @param forwardToUrl
     * @param method
     * @param request
     * @param response
     * @throws Exception
     */
    @Override
    public void forwardRequest(String forwardToUrl, String method, HttpServletRequest request, HttpServletResponse response) throws Exception{
        boolean hasOutBody = (method.equalsIgnoreCase("POST"));
        LOGGER.info("hasOutBody " + hasOutBody);
        try {
            URL url = new URL(forwardToUrl  // no trailing slash
                    + request.getRequestURI()
                    + (request.getQueryString() != null ? "?" + request.getQueryString() : StringUtils.EMPTY));
            LOGGER.info("url to be forwarded to : " + url);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod(method);
            Enumeration<String> headers = request.getHeaderNames();
            while (headers.hasMoreElements()) {
                String header = headers.nextElement();
                LOGGER.info("header found  " + header);
                Enumeration<String> values = request.getHeaders(header);
                while (values.hasMoreElements()) {
                    String value = values.nextElement();
                    conn.addRequestProperty(header, value);
                    LOGGER.info("Added header:  " + header);
                }
            }
            conn.setUseCaches(false);
            conn.setDoInput(true);
            conn.setDoOutput(hasOutBody);
            conn.connect();
            final byte[] buffer = new byte[16384];
            while (hasOutBody) {
                int read = request.getInputStream().read(buffer);
                if (read <= 0) break;
                conn.getOutputStream().write(buffer, 0, read);
            }
            response.setStatus(conn.getResponseCode());
            for (int i = 1; ; ++i) {
                String header = conn.getHeaderFieldKey(i);
                if (header == null) break;
                String value = conn.getHeaderField(i);
                response.setHeader(header, value);
            }
            while (true) {
                int read = conn.getInputStream().read(buffer);
                if (read <= 0) break;
                response.getOutputStream().write(buffer, 0, read);
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Exception occurred while forwardRequest ", e);
        }
    }
}
