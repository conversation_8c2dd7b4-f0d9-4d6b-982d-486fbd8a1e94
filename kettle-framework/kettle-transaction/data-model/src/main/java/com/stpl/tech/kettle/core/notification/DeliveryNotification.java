/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;

import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class DeliveryNotification extends EmailNotification implements Notification {

	private final DeliveryNotificationReceipt receipt;
	private final String supportEmail;
	private final String secondaryEmail;
	private OrderEmailEntryType type;

	public DeliveryNotification() {
		this.receipt = null;
		this.supportEmail = null;
		this.secondaryEmail = null;
	}

	public DeliveryNotification(OrderEmailEntryType type, String supportEmail, String secondaryEmail,
			DeliveryNotificationReceipt receipt) {
		this.receipt = receipt;
		this.supportEmail = supportEmail;
		this.secondaryEmail = secondaryEmail;
		this.type = type;
	}

	public String subject() {
		String prefix = null;
		switch (type) {
		case DISPATCH_DELAY:
			prefix = "Dispatch Delay Email for ";
			break;
		case DELIVERY_DELAY:
			prefix = "Delivery Delay Email for ";
			break;
		default:
			prefix = "Unknown Email";
		}
		return (TransactionUtils.isDev(receipt.getDetail().getEnv()) ? EnvType.DEV.name() + " " : "") + prefix
				+ receipt.getDetail().getUnit().getName() + " - " + receipt.getDetail().getOrder().getGenerateOrderId();
	}

	public String body() throws EmailGenerationException {
		try {
			return receipt.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	public String getFromEmail() {
		return receipt.getFromEmail();
	}

	public String[] getToEmails() {
		List<String> emails = new ArrayList<>();
		emails.add(secondaryEmail);
		emails.add(supportEmail);
		if (receipt.getToEmail().contains(",")) {
			String[] strs = receipt.getToEmail().split(",");
			for (String s : strs) {
				if (AppUtils.isValidEmail(s)) {
					emails.add(s);
				}
			}
		} else {
			emails.add(receipt.getToEmail());
		}
		return emails.toArray(new String[emails.size()]);
	}


	@Override
	public EnvType getEnvironmentType() {
		return receipt.getDetail().getEnv();
	}

	@Override
	public String getNotificationMessage() {
		return receipt.getNotificationMessage();
	}
}
