package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.RevalidationReason;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

public class RevalidationHelper {

    public static String revalidate(RevalidationReason reason, Order order, RevalidationService revalidationService,
                                    MasterDataCache masterDataCache) {
        switch (reason) {
            case COUPON_MAX_USAGE_AND_BY_CUSTOMER:
                return revalidationService.revalidateCouponMaxUsageAndForCustomer(order, masterDataCache);
            default:
                break;
        }
        return null;
    }
}
