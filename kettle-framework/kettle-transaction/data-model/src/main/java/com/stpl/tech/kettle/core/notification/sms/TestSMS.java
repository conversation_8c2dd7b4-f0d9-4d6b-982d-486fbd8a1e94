package com.stpl.tech.kettle.core.notification.sms;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;

import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.ApplicationName;

public class TestSMS {
	@Autowired
	private static SMSClientProviderService clientProviderService;

	public static void main(String[] args) throws IOException {
		ShortUrlData shortUrl = SolsInfiniWebServiceClient.getOTPClient().getShortUrl(
				"http://172.16.16.151:9595/kettle-admin/feedback.html?name=Mohit&contact=**********&unit=Good Earth&order=**********");
		System.out.println(shortUrl);
		if (shortUrl != null) {
			String message = CustomerSMSNotificationType.FEEDBACK_MESSAGE.getMessage(shortUrl);
			clientProviderService.getSMSClient(CustomerSMSNotificationType.FEEDBACK_MESSAGE.getTemplate().getSMSType(),
					ApplicationName.KETTLE_SERVICE).sendMessage(message, "**********");
		}
	}

}
