package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.payment.model.*;
import com.stpl.tech.master.payment.model.razorpay.RazorPayCreateRequest;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentResponse;
import com.stpl.tech.util.EnvType;

import java.math.BigDecimal;
import java.util.Map;

public interface RazorPayPaymentService {

	public RazorPayCreateRequest createRequest(EnvType type, OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;
	
	public boolean validateResponse(EnvType type, RazorPayPaymentResponse response,Integer brandId);

	public RazorPayPaymentResponse fetchPayment(EnvType type, String paymentId) throws PaymentFailureException;

	public PaymentStatus getPaymentStatus(EnvType type, String paymentId, BigDecimal transactionAmount) throws PaymentFailureException;

	public RazorPayPaymentResponse fetchOrder(EnvType type, String razorPayOrderId,Integer brandId) throws PaymentFailureException;

	public OrderPayment refundRequest(EnvType type, OrderPayment request) throws PaymentFailureException;


	OrderPaymentDetail handleDisassociatedPayment(OrderPaymentDetail paymentDetail) throws PaymentFailureException;

	OrderPaymentDetail handleDisassociatedPaymentForDineIn(OrderPaymentDetail paymentDetail) throws PaymentFailureException;

	Boolean checkRazorpayPayment(OrderPaymentDetail paymentDetail);

	RazorPayPaymentResponse fetchRazorPayPayment(String paymentId) throws PaymentFailureException;

    RazorPayCreateRequest createRazorPayRequest(OrderPaymentRequest order) throws DataNotFoundException, PaymentFailureException;

    Map updateRazorPayResponse(RazorPayPaymentResponse response, Integer brandId);

	Map updateRazorPayResponseWithoutVerification(RazorPayPaymentResponse response);

    Map updateRazorPayResponseForHangingPayment(RazorPayPaymentResponse response);

    void updateRefundedRazorPayPayment(RazorPayPaymentResponse response);

    void updateRefundInitiatedRazorPayPayment(RazorPayPaymentResponse response);

    Map updateRazorPayResponseForHangingPaymentDineIn(RazorPayPaymentResponse response);
}
