/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.monitoring;

import java.util.concurrent.BlockingQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.master.core.service.model.ScreenType;
import com.stpl.tech.master.domain.model.UnitCategory;

public class DelayQueueProducer {

	// Creates an instance of blocking queue using the DelayQueue.
	private BlockingQueue<UnitMonitorData> queue;

	private static final Logger LOG = LoggerFactory.getLogger(DelayQueueConsumer.class);

	public DelayQueueProducer(BlockingQueue<UnitMonitorData> queue) {
		super();
		this.queue = queue;
	}

	private Thread producerThread = new Thread(new Runnable() {
		public void run() {
			while (true) {
				try {
					for (int i = 0; i < 10; i++) {
						UnitMonitorData object = new UnitMonitorData(10000 + i, "test", 1, ScreenType.ASSEMBLY,
								UnitCategory.CAFE, 30000L);
						LOG.info(String.format("Put object = %s%n", object));
						queue.put(object);
					}
					Thread.sleep(10000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		}
	}, "Producer Thread");

	public void start() {
		this.producerThread.start();
	}

}
