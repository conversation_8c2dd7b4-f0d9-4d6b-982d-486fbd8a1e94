package com.stpl.tech.kettle.core.service;

import java.util.List;

import com.stpl.tech.kettle.data.model.RulesData;
import com.stpl.tech.kettle.data.model.RulesOptionResultData;
import com.stpl.tech.kettle.offer.model.Option;
import com.stpl.tech.kettle.offer.model.OptionResponseData;
import com.stpl.tech.kettle.offer.model.OrderData;
import com.stpl.tech.kettle.offer.model.RecommendationDetail;
import com.stpl.tech.kettle.offer.model.RuleData;

public interface RuleService {

	public RuleData getRule(OrderData orderDetail);

	public List<RulesData> getRules(String status);

	public void createRules();

	public RecommendationDetail create(OptionResponseData response);

	public void update(RecommendationDetail response);

	/**
	 * @param unitId
	 * @param option
	 * @return
	 */
	RulesOptionResultData getOrCreateRulesOptionResultData(int unitId,RuleData rule, Option option);
	
}
