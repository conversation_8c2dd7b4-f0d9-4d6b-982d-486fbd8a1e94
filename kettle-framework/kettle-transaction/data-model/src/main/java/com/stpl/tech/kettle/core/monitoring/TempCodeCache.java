/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.monitoring;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.DelayQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class TempCodeCache {

	private static final Logger LOG = LoggerFactory.getLogger(TempCodeCache.class);
	
	private BlockingQueue<TempCodeData> queue = new DelayQueue<TempCodeData>();
	private Map<String, TempCodeData> codeMap = new HashMap<>();

	public synchronized TempCodeData addToCache(TempCodeData data) {
		try {
			queue.put(data);
		} catch (InterruptedException e) {
			LOG.error(String.format("Error while putting %s token to queue", data), e);
			return null;
		}
		codeMap.put(data.getCode(), data);
		LOG.info(String.format("Added %s token to cache", data));
		return data;
	}

	public synchronized void removeFromCache(String code) {
		codeMap.remove(code);
		LOG.info(String.format("Removed %s token from cache", code));
	}

	public boolean hasCode(String code) {
		return codeMap.containsKey(code);
	}

	public BlockingQueue<TempCodeData> getQueue() {
		return queue;
	}

	public Map<String, TempCodeData> getCodeMap() {
		return codeMap;
	}

}
