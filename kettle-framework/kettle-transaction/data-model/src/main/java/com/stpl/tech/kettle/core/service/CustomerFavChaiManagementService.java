package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.domain.model.CustomerFavChaiMappingVO;
import com.stpl.tech.kettle.domain.model.FavouriteChai;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiRequest;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiResponse;

import java.util.List;

public interface CustomerFavChaiManagementService {
    boolean saveCustomerFavChai(int customerId, SaveCustomerFavChaiRequest saveCustomerFavChaiRequest);

    List<CustomerFavChaiMappingVO> getAllActiveCustomerFavChaiMappings(int customerId);

    List<FavouriteChai> convertActiveFavChaiMappingsToFavChaiDineIN(int customerId, int unitId);

    boolean saveAndUpdateFavChaiFromDineIn(FavouriteChai favouriteChai);

    boolean updateIncorrectFavChaiFromDineIn();
    public void saveOrderCountOfSavedChai(Order order, Integer orderId);
}
