/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.CalculationType;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.CalculationStatus;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.DirectCost;
import com.stpl.tech.kettle.core.data.budget.vo.ElectricityAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.InventoryAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.RentAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.RevenueData;
import com.stpl.tech.kettle.core.data.budget.vo.SalesCommissionData;
import com.stpl.tech.kettle.core.data.budget.vo.WastageAggregate;
import com.stpl.tech.kettle.core.data.vo.PnLRepresentation;
import com.stpl.tech.kettle.core.data.vo.PnlRecord;
import com.stpl.tech.kettle.core.service.UnitBudgetService;
import com.stpl.tech.kettle.data.dao.ExpenseManagementDao;
import com.stpl.tech.kettle.data.dao.UnitBudgetDao;
import com.stpl.tech.kettle.data.dao.impl.BudgetUtils;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetoryDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureAggregateDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.domain.model.UnitExpenditure;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class UnitBudgetServiceImpl implements UnitBudgetService {

	private static final Logger LOG = LoggerFactory.getLogger(UnitBudgetServiceImpl.class);

	@Autowired
	private UnitBudgetDao dao;

	@Autowired
	private ExpenseManagementDao expenseDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, RevenueData> getRevenueData(Date businessDate, List<Integer> unitIds) {
		// TODO Auto-generated method stub
		return null;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#
	 * getSalesCommissionData(java.util.Date, java.util.List)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, SalesCommissionData> getSalesCommissionData(Date businessDate, List<Integer> unitIds) {
		// TODO Auto-generated method stub
		return null;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.UnitBudgetService#getInventoryAggregate
	 * (java.util.Date, java.util.List)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, InventoryAggregate> getInventoryAggregate(Date businessDate, List<Integer> unitIds) {
		// TODO Auto-generated method stub
		return null;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#getWastageAggregate(
	 * java.util.Date, java.util.List)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, WastageAggregate> getWastageAggregate(Date businessDate, List<Integer> unitIds) {
		// TODO Auto-generated method stub
		return null;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#
	 * getConsumablesAggregate(java.util.Date, java.util.List)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, ConsumablesAggregate> getConsumablesAggregate(Date businessDate, List<Integer> unitIds) {
		// TODO Auto-generated method stub
		return null;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#getExpenseAggregate(
	 * java.util.Date, java.util.List)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, ExpenseAggregate> getExpenseAggregate(Date businessDate, List<Integer> unitIds) {
		// TODO Auto-generated method stub
		return null;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#
	 * getElectricityAggregate(java.util.Date, java.util.List)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, ElectricityAggregate> getElectricityAggregate(Date businessDate, List<Integer> unitIds) {
		// TODO Auto-generated method stub
		return null;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#getDirectCost(java.
	 * util.Date, java.util.List)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, DirectCost> getDirectCost(Date businessDate, List<Integer> unitIds) {
		// TODO Auto-generated method stub
		return null;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.UnitBudgetService#getRentAggregate(java
	 * .util.Date, java.util.List)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, RentAggregate> getRentAggregate(Date businessDate, List<Integer> unitIds) {
		// TODO Auto-generated method stub
		return null;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#
	 * getCreditCardPercentageForCurrentMonth(int)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public BigDecimal getCreditCardPercentageForCurrentMonth(int unitId, Date businessDate) {
		return dao.getCreditCardPercentageForCurrentMonth(unitId, businessDate);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.UnitBudgetService#savePnL(com.stpl.tech
	 * .kettle.core.data.budget.vo.BudgetDetail)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public int savePnL(BudgetDetail budgetDetail) {
		return dao.savePnL(budgetDetail);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean savePnL(Collection<BudgetDetail> budgetDetail) {
		return dao.savePnL(budgetDetail);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#markPnLAsCancelled(
	 * int)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void markAsCancelled(int pnlDetailId) {
		dao.changePnLStatus(pnlDetailId, CalculationStatus.CANCELLED);
		expenseDao.unMarkPnlAccountableExpenses(pnlDetailId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void markAsBudgetNotAvailable(Integer pnlDetailId) {
		dao.changePnLStatus(pnlDetailId, CalculationStatus.BUDGET_NOT_AVAILABLE);
		expenseDao.unMarkPnlAccountableExpenses(pnlDetailId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void markAsFailed(Integer pnlDetailId) {
		dao.changePnLStatus(pnlDetailId, CalculationStatus.FAILED);
	}



	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public int getPnlDetailId(int unitId, int dayClosureId) {
		return dao.getPnlDetailFromUnitAndDayCloseId(unitId, dayClosureId);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#getUnitBudgetDetail(
	 * int, int, int)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitBudgetoryDetail getUnitBudgetDetail(int unitId, int month, int year) {
		return dao.getUnitBudgetDetail(unitId, month, year);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#getUnitBudgetDetail(
	 * java.util.Date, com.stpl.tech.kettle.core.CalculationStatus)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitExpenditureDetail> getUnitExpenditureDetail(Date businessDate, CalculationStatus status,
			CalculationType calculation) {
		return dao.getUnitExpenditureDetail(businessDate, status, calculation);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#
	 * saveKettleCalculatedExpenses(int,
	 * com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void saveKettleCalculatedExpenses(int detailId, BudgetDetail detail) {
		dao.saveKettleCalculatedExpenses(detailId, detail);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updatePnLInVoucherData(int detailId, Date businessDate, List<VoucherData> voucherDataList)
			throws DataUpdationException {
		dao.updatePnLInVoucherData(detailId, businessDate, voucherDataList);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitExpenditure> getMTDPnlForUnit(Integer unitId, Date businessDate) {
		UnitExpenditureDetail detail = dao.getExpenditureForUnit(businessDate, unitId, CalculationStatus.COMPLETED,
				CalculationType.MTD);
		UnitExpenditureDetail current = dao.getExpenditureForUnit(businessDate, unitId, CalculationStatus.COMPLETED,
				CalculationType.CURRENT);
		UnitBudgetoryDetail budgetoryDetail = dao.getUnitBudgetDetail(unitId, AppUtils.getMonth(businessDate),
				AppUtils.getYear(businessDate));
		return BudgetUtils.getDetails(current, detail, budgetoryDetail, false);
	}

	@Override
	public List<PnlRecord> getPnlRepresentationForUnit(Integer unitId, Date businessDate) throws DataUpdationException {
		UnitBudgetoryDetail budget = dao.getUnitBudgetDetail(unitId, AppUtils.getMonth(businessDate),
				AppUtils.getYear(businessDate));
//        List<Date> businessDates = AppUtils.getDaysBetweenDates(AppUtils.getFirstDayOfMonth(businessDate), businessDate, true);
		UnitExpenditureAggregateDetail aggregateCurrent = dao.getExpenditureAggregateForUnit(businessDate, unitId,
				CalculationStatus.COMPLETED, CalculationType.CURRENT);
		UnitExpenditureAggregateDetail aggregateMtd = dao.getExpenditureAggregateForUnit(businessDate, unitId,
				CalculationStatus.COMPLETED, CalculationType.MTD);
		List<UnitExpenditureAggregateDetail> aggregateMtds = Arrays.asList(aggregateMtd);
		PnLRepresentation pnl = aggregateCurrent.render(aggregateCurrent, aggregateMtds, budget);
		return pnl.getRecords();
	}

	@Override
	public List<PnlRecord> getPnlMtdAggregateViewData(Integer unitId, Date businessDate) throws DataUpdationException {
		UnitBudgetoryDetail budget = dao.getUnitBudgetDetail(unitId, AppUtils.getMonth(businessDate),
				AppUtils.getYear(businessDate));
		List<Date> businessDates = AppUtils.getDaysBetweenDates(AppUtils.getFirstDayOfMonth(businessDate), businessDate,
				true);
		UnitExpenditureAggregateDetail lastDateAggregate = dao.getExpenditureAggregateForUnit(businessDate, unitId,
				CalculationStatus.COMPLETED, CalculationType.MTD);
		List<UnitExpenditureAggregateDetail> aggregateMtds = dao.getExpenditureAggregateForUnit(businessDates, unitId,
				CalculationStatus.COMPLETED, CalculationType.MTD);
		PnLRepresentation pnl = lastDateAggregate.render(lastDateAggregate, aggregateMtds, budget);
		return pnl.getRecords();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitExpenditureDetail> getAllMTDPnlForUnitForMonth(Integer unitId, Date date) {
		List<Date> businessDates = AppUtils.getDaysBetweenDates(AppUtils.getFirstDayOfMonth(date), date, true);
		return dao.getExpenditureForUnit(businessDates, unitId, CalculationStatus.COMPLETED, CalculationType.MTD);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#
	 * getLatestUnitExpenditureDetail(int,
	 * com.stpl.tech.kettle.core.CalculationStatus,
	 * com.stpl.tech.kettle.core.CalculationType)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitExpenditureDetail getLatestUnitExpenditureDetail(int unitId, int month, int year,
			CalculationStatus status, CalculationType calculation) {
		return dao.getLatestUnitExpenditureDetail(unitId, month, year, status, calculation);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.UnitBudgetService#savePnL(com.stpl.tech
	 * .kettle.data.model.UnitExpenditureDetail)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public int savePnL(UnitExpenditureDetail expenseDetail) {
		return dao.savePnL(expenseDetail);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#
	 * getMTDUnitExpenditureDetail(java.util.Date)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitExpenditureDetail> getAllUnitExpenditureDetail(Date businessDate, CalculationType type) {
		return dao.getAllUnitExpenditureDetail(businessDate, type);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#
	 * getUnitExpenditureDetail(int, java.util.Date,
	 * com.stpl.tech.kettle.core.CalculationType)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitExpenditureDetail> getUnitExpenditureDetail(int unitId, Date businessDate,
			CalculationType calculation) {
		return dao.getUnitExpenditureDetail(unitId, businessDate, calculation);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.UnitBudgetService#
	 * getAllUnitExpenditureDetail(int, java.util.Date)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitExpenditureDetail> getAllUnitExpenditureDetail(int unitId, Date businessDate) {
		return dao.getAllUnitExpenditureDetail(unitId, businessDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void createFinalizedEntry(Integer unitId, Date businessDate) {
		UnitExpenditureDetail detail = dao.getExpenditureForUnit(businessDate, unitId, CalculationStatus.COMPLETED,
				CalculationType.MTD);
		if (detail != null) {
			UnitExpenditureDetail copy = JSONSerializer.toJSON(JSONSerializer.toJSON(detail),
					UnitExpenditureDetail.class);
			copy.setDetailId(null);
			copy.setCalculation(CalculationType.FINALIZED.name());
			copy.setStatus(CalculationStatus.PENDING_FINALIZED_CALCULATION.name());
			dao.add(copy);
		} else {
			LOG.info("No MTD entry for unitId {} and business Date {}", unitId, businessDate);
		}
	}

	/**
	 * @param b
	 */
	@Override
	public DirectCost setDirectCostData(UnitBudgetoryDetail b, Integer day, Integer month, Integer year) {
		YearMonth yearMonthObject = YearMonth.of(year, month);
		int daysInMonth = yearMonthObject.lengthOfMonth();
		BigDecimal proRata = AppUtils.divideWithScale10(new BigDecimal(day), new BigDecimal(daysInMonth));
		DirectCost d = new DirectCost();
		d.setSalary(AppUtils.multiply(proRata, b.getSalary()));
		d.setSalaryIncentive(AppUtils.multiply(proRata, b.getSalaryIncentive()));
		// d.setSecurityGuardCharges(b.getSecurityGuardCharges());
		d.setSalesIncentive(AppUtils.multiply(proRata, b.getSalesIncentive()));
		d.setDepreciationOfBike(AppUtils.multiply(proRata, b.getDepreciationOfBike()));
		// d.setLogisticCharges(b.getLogisticCharges());
		d.setWaterCharges(AppUtils.multiply(proRata, b.getWaterCharges()));

		d.setPropertyTax(AppUtils.multiply(proRata, b.getPropertyTax()));
		d.setOpeningLicencesFees(AppUtils.multiply(proRata, b.getOpeningLicencesFees()));
//        d.setRegistrationCharges(AppUtils.multiply(proRata,b.getRegistrationCharges()));
//        d.setStampDutyCharges(AppUtils.multiply(proRata,b.getStampDutyCharges()));
		d.setDesigningFees(AppUtils.multiply(proRata, b.getDesigningFees()));
//		d.setProntoAMC(AppUtils.multiply(proRata, b.getProntoAMC()));
//		d.setDgRental(AppUtils.multiply(proRata, b.getDgRental()));
//		d.setEdcRental(AppUtils.multiply(proRata, b.getEdcRental()));
//		d.setSystemRental(AppUtils.multiply(proRata, b.getSystemRental()));
//		d.setRoRental(AppUtils.multiply(proRata, b.getRoRental()));
		d.setFixCAM(AppUtils.multiply(proRata, b.getFixCAM()));
		d.setChillingCharges(AppUtils.multiply(proRata, b.getChillingCharges()));
		d.setMarketingCharges(AppUtils.multiply(proRata, b.getMarketingCharges()));
//		d.setPettyCashRentals(AppUtils.multiply(proRata, b.getPettyCashRentals()));
//		d.setMusicRentals(AppUtils.multiply(proRata, b.getMusicRentals()));
		d.setInternetPartnerRental(AppUtils.multiply(proRata, b.getInternetPartnerRental()));
//		d.setTechologyPlatformCharges(AppUtils.multiply(proRata, b.getTechologyPlatformCharges()));

//		d.setTechologyOthers(AppUtils.multiply(proRata, b.getTechologyOthers()));
		d.setTechnologyVariable(AppUtils.multiply(proRata, b.getTechnologyVariable()));

		d.setEnergyElectricity(AppUtils.multiply(proRata, b.getEnergyElectricity()));
		d.setEnergyDGRunning(AppUtils.multiply(proRata, b.getEnergyDGRunning()));

//		d.setBadDebtsWrittenOff(AppUtils.multiply(proRata, b.getBadDebtsWrittenOff()));

		d.setServiceChargesPaid(AppUtils.multiply(proRata, b.getServiceChargesPaid()));
		d.setInsuranceVehicle(AppUtils.multiply(proRata, b.getInsuranceVehicle()));
//		d.setOthersAMC(AppUtils.multiply(proRata, b.getOthersAMC()));
//		d.setOthersMaintenance(AppUtils.multiply(proRata, b.getOthersMaintenance()));
		d.setByodCharges(AppUtils.multiply(proRata, b.getByodCharges()));
		d.setCarLease(AppUtils.multiply(proRata, b.getCarLease()));
		d.setDriverSalary(AppUtils.multiply(proRata, b.getDriverSalary()));
		d.setGratuity(AppUtils.multiply(proRata, b.getGratuity()));
//		d.setInsurnaceAccidental(AppUtils.multiply(proRata, b.getInsurnaceAccidental()));
//		d.setInsurnaceMedical(AppUtils.multiply(proRata, b.getInsurnaceMedical()));
		d.setSupportsOpsTurnover(AppUtils.multiply(proRata, b.getSupportsOpsTurnover()));
//		d.setEmployeeFacilitationExpenses(AppUtils.multiply(proRata, b.getEmployeeFacilitationExpenses()));
		d.setTelephoneSR(AppUtils.multiply(proRata, b.getTelephoneSR()));
		d.setVehicleRunningAndMaintSR(AppUtils.multiply(proRata, b.getVehicleRunningAndMaintSR()));
		d.setEmployeeStockOptionExpense(AppUtils.multiply(proRata, b.getEmployeeStockOptionExpense()));
		d.setEmployerContributionLWF(AppUtils.multiply(proRata, b.getEmployerContributionLWF()));
		d.setEsicEmployerCont(AppUtils.multiply(proRata, b.getEsicEmployerCont()));
		d.setLeaveTravelReimbursement(AppUtils.multiply(proRata, b.getLeaveTravelReimbursement()));
		d.setPfAdministrationCharges(AppUtils.multiply(proRata, b.getPfAdministrationCharges()));
		d.setPfEmployerCont(AppUtils.multiply(proRata, b.getPfEmployerCont()));
		d.setQuarterlyIncentive(AppUtils.multiply(proRata, b.getQuarterlyIncentive()));
//		d.setSupportAudit(AppUtils.multiply(proRata, b.getSupportAudit()));
		d.setSupportCCC(AppUtils.multiply(proRata, b.getSupportCCC()));
		d.setSupportIT(AppUtils.multiply(proRata, b.getSupportIT()));
//		d.setSupportMaintenance(AppUtils.multiply(proRata, b.getSupportMaintenance()));
		d.setSupportCommWH(AppUtils.multiply(proRata, b.getSupportCommWH()));

		d.setPreOpeningConsumable(AppUtils.multiply(proRata, b.getPreOpeningConsumable()));
		d.setPreOpeningOthers(AppUtils.multiply(proRata, b.getPreOpeningOthers()));
		d.setPreOpeningRent(AppUtils.multiply(proRata, b.getPreOpeningRent()));
		d.setPreOpeningSalary(AppUtils.multiply(proRata, b.getPreOpeningSalary()));
		d.setBankCharges(AppUtils.multiply(proRata, b.getBankCharges()));
		d.setInterestOnLoan(AppUtils.multiply(proRata, b.getInterestOnLoan()));
		d.setIntrestOnTDSorGST(AppUtils.multiply(proRata, b.getIntrestOnTDSorGST()));
		d.setInterestOnFDR(AppUtils.multiply(proRata, b.getInterestOnFDR()));
		d.setProfitSaleMutualFunds(AppUtils.multiply(proRata, b.getProfitSaleMutualFunds()));
		d.setInterestIncomeTaxRefund(AppUtils.multiply(proRata, b.getInterestIncomeTaxRefund()));
		d.setMiscIncome(AppUtils.multiply(proRata, b.getMiscIncome()));
		d.setDiscountReceived(AppUtils.multiply(proRata, b.getDiscountReceived()));
		d.setInteriorDesigningCharge(AppUtils.multiply(proRata, b.getInteriorDesigningCharge()));
		d.setScrape(AppUtils.multiply(proRata, b.getScrape()));
		d.setServiceCharges(AppUtils.multiply(proRata, b.getServiceCharges()));
		d.setServiceChargesFICO(AppUtils.multiply(proRata, b.getServiceChargesFICO()));
		d.setSupportOpsManagement(AppUtils.multiply(proRata, b.getSupportOpsManagement()));

		d.setBusinessPromotion(AppUtils.multiply(proRata, b.getBusinessPromotion()));
		d.setBusinessPromotionSR(AppUtils.multiply(proRata, b.getBusinessPromotionSR()));
		d.setRoundedOff(AppUtils.multiply(proRata, b.getRoundedOff()));
//		d.setShortAndExcess(AppUtils.multiply(proRata, b.getShortAndExcess()));

		d.setRoAMC(AppUtils.multiply(proRata, b.getRoAMC()));
//		d.setInsuranceAssets(AppUtils.multiply(proRata, b.getInsuranceAssets()));
//		d.setInsuranceCGL(AppUtils.multiply(proRata, b.getInsuranceCGL()));
		d.setInsuranceDnO(AppUtils.multiply(proRata, b.getInsuranceDnO()));

		d.setFuelChargesCafe(AppUtils.multiply(proRata, b.getFuelChargesCafe()));

		d.setBonusAttendance(AppUtils.multiply(proRata, b.getBonusAttendance()));
		d.setBonusJoining(AppUtils.multiply(proRata, b.getBonusJoining()));
		d.setBonusReferral(AppUtils.multiply(proRata, b.getBonusReferral()));
		d.setBonusHoliday(AppUtils.multiply(proRata, b.getBonusHoliday()));
		d.setBonusOthers(AppUtils.multiply(proRata, b.getBonusOthers()));
		d.setAllowanceRemoteLocation(AppUtils.multiply(proRata, b.getAllowanceRemoteLocation()));
		d.setAllowanceCityCompensatory(AppUtils.multiply(proRata, b.getAllowanceCityCompensatory()));
		d.setAllowanceEmployeeBenefit(AppUtils.multiply(proRata, b.getAllowanceEmployeeBenefit()));
		d.setAllowanceMonk(AppUtils.multiply(proRata, b.getAllowanceMonk()));
		d.setAllowanceOthers(AppUtils.multiply(proRata, b.getAllowanceOthers()));
		d.setNoticePeriodBuyout(AppUtils.multiply(proRata, b.getNoticePeriodBuyout()));
		d.setNoticePeriodDeduction(AppUtils.multiply(proRata, b.getNoticePeriodDeduction()));
		d.setRelocationExpenses(AppUtils.multiply(proRata, b.getRelocationExpenses()));
		d.setStipendExpenses(AppUtils.multiply(proRata, b.getStipendExpenses()));
		d.setTrainingCostRecovery(AppUtils.multiply(proRata, b.getTrainingCostRecovery()));
		d.setSeverancePay(AppUtils.multiply(proRata, b.getSeverancePay()));
		d.setLabourCharges(AppUtils.multiply(proRata, b.getLabourCharges()));

		d.setCancellationChargesChannelPartners(AppUtils.multiply(proRata, b.getCancellationChargesChannelPartners()));

		d.setFixedParkingCharges(AppUtils.multiply(proRata, b.getFixedParkingCharges()));
		d.setCogsLogistics(AppUtils.multiply(proRata, b.getCogsLogistics()));
		d.setPreOpeningCamEleWater(AppUtils.multiply(proRata, b.getPreOpeningCamEleWater()));
		d.setPreOpeningRegistrationCharges(AppUtils.multiply(proRata, b.getPreOpeningRegistrationCharges()));
		d.setPreOpeningStampDutyCharges(AppUtils.multiply(proRata, b.getPreOpeningStampDutyCharges()));
		d.setPreOpeningConsumableTax(AppUtils.multiply(proRata, b.getPreOpeningConsumableTax()));
		d.setSupport(AppUtils.multiply(proRata, b.getSupport()));
//		d.setCorporateMarketingChannelPartner(AppUtils.multiply(proRata, b.getCorporateMarketingChannelPartner()));
		d.setPreOpeningEleWater(AppUtils.multiply(proRata, b.getPreOpeningEleWater()));
		d.setPreOpeningCam(AppUtils.multiply(proRata, b.getPreOpeningCam()));
		d.setInterestOnFixedDepositFICO(AppUtils.multiply(proRata, b.getInterestOnFixedDepositFICO()));

		return d;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void createClosedPnlEntry(Integer unitId, Date businessDate) {
		UnitExpenditureDetail detail = dao.getExpenditureForUnit(businessDate, unitId, CalculationStatus.COMPLETED,
				CalculationType.FINALIZED);
		if (detail != null) {
			UnitExpenditureDetail copy = JSONSerializer.toJSON(JSONSerializer.toJSON(detail),
					UnitExpenditureDetail.class);
			UnitBudgetoryDetail budget = dao.getUnitBudgetDetail(detail.getUnitId(), detail.getMonth(),
					detail.getYear());
			copy.setDetailId(null);
			copy.setCalculation(CalculationType.CLOSED.name());
			copy.setStatus(CalculationStatus.COMPLETED.name());
			DirectCost cost = setDirectCostData(budget, copy.getDay(), copy.getMonth(), copy.getYear());
			BudgetUtils.setDirectCost(copy, cost);
			dao.add(copy);
			calculateAggregatePnLData(copy);
		} else {
			LOG.info("No MTD entry for unitId {} and business Date {}", unitId, businessDate);
		}
	}

	private void calculateAggregatePnLData(UnitExpenditureDetail mtd) {
        LOG.info("adding finalized aggregate entry");
		UnitExpenditureAggregateDetail aggregateDetail = BudgetUtils.setAggregateCalculatedData(mtd);
		aggregateDetail.setUnitId(mtd.getUnitId());
		aggregateDetail.setUnitName(mtd.getUnitName());
		aggregateDetail.setUnitExpenditureDetailId(mtd.getDetailId());
		aggregateDetail.setStatus(mtd.getStatus());
		aggregateDetail.setDay(mtd.getDay());
		aggregateDetail.setMonth(mtd.getMonth());
		aggregateDetail.setYear(mtd.getYear());
		aggregateDetail.setBusinessDate(mtd.getBusinessDate());
		aggregateDetail.setCalculation(mtd.getCalculation());
		dao.add(aggregateDetail);

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getUnitsWithFinalizedEntries(Date businessDate) {
		return dao.getUnitsWithFinalizedEntries(businessDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getUnitsWithClosedEntries(Date businessDate) {
		return dao.getUnitsWithClosedEntries(businessDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitExpenditureDetail> getFinalizedPnlForUnitForMonth(Integer unitId, Integer month, Integer year) {
		List<UnitExpenditureDetail> l = new ArrayList<UnitExpenditureDetail>();
		UnitExpenditureDetail detail = dao.getLatestUnitExpenditureDetail(unitId, month, year,
				CalculationStatus.COMPLETED, CalculationType.FINALIZED);
		l.add(detail);
		return l;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PnlRecord> getFinalizedPnlAggregateForUnitForMonth(Integer unitId, Integer month, Integer year)
			throws DataUpdationException {
		UnitExpenditureAggregateDetail detail = dao.getLatestUnitExpenditureAggregateDetail(unitId, month, year,
				CalculationStatus.COMPLETED, CalculationType.FINALIZED);
		UnitBudgetoryDetail budget = dao.getUnitBudgetDetail(unitId, month, year);
		List<UnitExpenditureAggregateDetail> mtds = Arrays.asList(detail);
		PnLRepresentation record = detail.render(detail, mtds, budget);
		return record.getRecords();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitExpenditureAggregateDetail checkFinalizedPnlAggregateForUnitForMonth(Integer unitId, Integer month, Integer year) {
		return dao.getLatestUnitExpenditureAggregateDetail(unitId,month,year, CalculationStatus.COMPLETED, CalculationType.FINALIZED);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateGiftCardOfferData(int expenseId, int unitId, Date businessDate) {
		BigDecimal offerAmount = BigDecimal.ZERO;
		List<CashCardDetail> list = dao.getCashCardsForUnitForDay(unitId, businessDate);
		for (CashCardDetail d : list) {
			offerAmount = AppUtils.add(offerAmount, d.getInitialOffer());
		}
		UnitExpenditureDetail ud = dao.find(UnitExpenditureDetail.class, expenseId);
		ud.setGiftCardOffer(offerAmount);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void markAsCompleted(Integer detailId) {
		dao.changePnLStatus(detailId, CalculationStatus.COMPLETED);
		UnitExpenditureDetail detail = dao.find(UnitExpenditureDetail.class, detailId);
		calculateAggregatePnLData(detail);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<UnitExpenditureAggregateDetail> getAllUnitExpenditureAggregateDetail(Integer unitId, Date businessDate){
//		dao.changePnLStatus(pnlDetailId, CalculationStatus.CANCELLED);
//		expenseDao.unMarkPnlAccountableExpenses(pnlDetailId);
		return dao.getAllUnitExpenditureAggregateDetail(unitId, businessDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void markAggregateAsCancelled(int detailId){
		dao.changePnLAggregateStatus(detailId, CalculationStatus.CANCELLED);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void cancelAllPnLs(Date minimumDate, Set<Integer> keySet) {
		List<Integer> unitIds= new ArrayList<>(keySet);
		dao.cancelAllUnitExpenditureDetail(unitIds,minimumDate);
		dao.cancelAllUnitExpenditureAggregateDetail(unitIds,minimumDate);
	}

}
