/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.CustomerRepeatType;
import com.stpl.tech.kettle.core.SignUpTimeSlotCount;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.data.model.CustomerCampaignJourney;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.WebOfferCouponRedemptionDetail;
import com.stpl.tech.kettle.domain.model.CreateNextOfferRequest;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerOneViewData;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewData;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.SubscriptionInfoDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CustomerWinbackOfferInfo;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.data.model.DeliveryOfferDetailData;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CustomerWinbackOfferInfoDomain;
import com.stpl.tech.master.domain.model.OfferResponse;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import org.springframework.web.servlet.View;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CustomerOfferManagementService {

    public OfferOrder applyCoupoun(OfferOrder offerOrder, BigDecimal offerValue) throws OfferValidationException, DataNotFoundException;

    public boolean addCouponMappingTypeCustomer(String coupon, Customer customer);

    /**
     * @param businessDate
     * @return
     */
    public CouponDetail getFreeItemOffer(Date businessDate);

    public boolean availedCashOffer(Integer customerId, Date startOfBusinessDay, Collection<String> offerCodes, int maxUsageAllowed);
    Boolean hasSignUpOfferAvailed(Integer customerId) throws Exception;

    boolean availSignUpOffer(String customerId, String customerName, String timeOfDelivery,
                             String dateOfDelivery, String completeAddress, String city, String pinCode, OrderItem product,
                             Integer unitId, Integer brandId) throws Exception;

    WebOfferCouponRedemptionDetail addCouponRedemptionDetail(WebOfferCouponRedemptionDetail redemptionDetail);

    SignUpTimeSlotCount fetchSignUpOfferTimeSlots(String dateOfDelivery) throws Exception;

	Boolean hasSubscription(Integer customerId, String offerCode);

    SubscriptionInfoDetail getSubscriptionInfoDetail(int customerId);

    void addSubscriptionSaving(int customerId, Order overAllSaving, Pair<CouponDetail, Product> subscriptionObj);

	public boolean hasCustomerReceivedPostOrderOffer(int customerId, Integer lastNDays, Integer campaignId);

	boolean updateOfferApplicationDetails(String couponCode, Integer customerId, Integer orderId, BigDecimal savings);

	CustomerOneViewData getCustomerOneViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	CustomerTransactionViewData getCustomerTransactionViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	CustomerEmailData getCustomerEmailData(int customerId, Integer brandId);

	CustomerCampaignOfferDetail createPostOrderOffer(Integer brandId, Integer unitId, Integer campaignId,
													 int customerId, Integer orderId, String contactNumber, String countryCode, CouponData response,
													 String firstName, String description, String cloneCopunCode, CampaignDetail campaignDetail, CustomerRepeatType type, Integer journeyNumber, CreateNextOfferRequest request);

	CustomerCampaignOfferDetail createDeliveryPostOrderOffer(Integer brandId, Integer unitId, Integer campaignId,
															 Customer customer, Integer orderId, String contactNumber, String countryCode, CouponData response,
															 String firstName, String description, String cloneCouponCode, DeliveryCouponDetailData deliveryCoupon,
															 Boolean isCouponClone, CampaignDetail campaignDetail, CustomerRepeatType type, Integer journeyNumber, CreateNextOfferRequest request);

	List<CustomerCampaignOfferDetail> createPostOrderOffer(Integer brandId, Integer unitId, Integer campaignId,
			String countryCode, CouponCloneResponse response, Map<String, Pair<Integer, String>> customerNumberToIdMapping,
														   CampaignDetail campaignDetail, CustomerRepeatType type, Integer journey);

	CouponCloneResponse getCloneCode(String startDay, List<String> contactNumbers, CustomerRepeatType type,
			String cloneCode, int usageCount, int validityInDays, String prefix, Integer applicableUnitId,
			String applicableRegion) throws DataUpdationException;

	CustomerCampaignOfferDetail getActiveCustomerOffer(int customerId, String strategy);

	Boolean setCustomerJourneyState(CustomerCampaignJourney journeyDetail);

	void addCustomerMappingCouponData(CouponDetailData couponDetail, String contactNumber);

	CouponCloneResponse getCloneCodeForDefault(List<String> contactNumbers, CustomerRepeatType type, String code, Integer couponDelay,
											   Integer usageCount, Integer validityInDays, String prefix, Integer applicableUnitId, String applicableRegion, CouponDetailData clone) throws DataUpdationException;

    CustomerCampaignOfferDetail getActiveOfferCampaign(int customerId, Integer campaignId);

    boolean hasCustomerReceivedDNBO(int customerId, Integer dinePostOrderOfferCheckLastNDaysValue, Integer campaignId);

	DeliveryCouponDetailData getDeliveryCloneCode(String code, Integer brandId, Boolean getClonedCoupon);

	CouponDetailData getCouponDetailData(String code);

    boolean cancelSubscription(Integer customerId) throws DataUpdationException;

	SubscriptionInfoDetail getSubscriptionInfoDetailForCustomer (int customerId);

	List<OfferResponse> getWinbackOffers();
	List<DeliveryOfferDetailData> getWinbackOfferForDelivery();

	CustomerWinbackOfferInfo generateWinbackCoupon(CustomerWinbackOfferInfoDomain domain, Integer customerId);

	CustomerWinbackOfferInfo markNotified(Integer id);

	List<CustomerWinbackOfferInfo> getWinbackInfo();

	View getWinbackSheet();

	public View getWinbackSheet(Date startDate,Date endDate);

	public CouponDetailData getCouponCustomerMapping(String couponCode, String contactNumber);
	public void updateCustomerCouponMapping(CouponDetailData couponDetailData);
	public void updateCustomerCouponOfferDetail(CouponDetailData couponDetailData, Integer customerId);
}
