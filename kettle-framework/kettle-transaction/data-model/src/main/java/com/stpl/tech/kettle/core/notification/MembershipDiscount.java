/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class MembershipDiscount {
	private Integer unitId;
	private List<Integer> productId;
	private BigDecimal totalAmount;
	private BigDecimal paidAmount;
	Map<Integer, MembershipProductDiscount> applicableDiscounts;

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public BigDecimal getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(BigDecimal paidAmount) {
		this.paidAmount = paidAmount;
	}

	public List<Integer> getProductId() {
		return productId;
	}

	public void setProductId(List<Integer> productId) {
		this.productId = productId;
	}

	public Map<Integer, MembershipProductDiscount> getApplicableDiscounts() {
		return applicableDiscounts;
	}

	public void setApplicableDiscounts(Map<Integer, MembershipProductDiscount> applicableDiscounts) {
		this.applicableDiscounts = applicableDiscounts;
	}

	@Override
	public String toString() {
		return "MembershipDiscount [unitId=" + unitId + ", productId=" + productId + ", totalAmount=" + totalAmount
				+ ", paidAmount=" + paidAmount + ", applicableDiscounts=" + applicableDiscounts + "]";
	}

}