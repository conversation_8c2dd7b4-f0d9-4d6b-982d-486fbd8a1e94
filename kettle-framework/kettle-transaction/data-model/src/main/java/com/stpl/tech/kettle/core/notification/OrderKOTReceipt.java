/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.KOTType;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OrderKOTReceipt extends OrderReceipt {

	private KOTType type;

	private final List<OrderItem> items = new ArrayList<OrderItem>();

	public OrderKOTReceipt(String urlBasePath, Unit unit, MasterDataCache metadataCache, OrderInfo detail, KOTType type, String basePath)
			throws DataNotFoundException {
		super(urlBasePath, unit, detail, basePath, null,false);
		this.type = type;
		for (OrderItem item : getDetail().getOrder().getOrders()) {
			Product product = metadataCache.getProduct(item.getProductId());
			if (product.getType() == type.getId() && !(product.getTaxCode().equals(AppConstants.GIFT_CARD_TAX_CODE))) {
				this.items.add(item);
			}
			if (product.getType() == AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE) {
				for (OrderItem addon : item.getComposition().getMenuProducts()) {
					product = metadataCache.getProduct(addon.getProductId());
					if (product.getType() == type.getId()) {
						this.items.add(addon);
					}
				}
			}
		}
	}

	@Override
	public String getTemplatePath() {
		return "template/OrderKOTPrint.html";
	}

	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getDetail().getOrder().getOrderId()
				+ "/OrderKOTPrint-" + type.getDescription() + "-" + getDetail().getOrder().getOrderId() + ".html";
	}

	public Map<String, Object> getData() {
		// Build the data-model
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("order", getDetail().getOrder());
		data.put("customer", getDetail().getCustomer());
		data.put("orderItems", items);
		data.put("description", type.getDescription());
		data.put("unit", getUnit());
		return data;
	}

	public String getContent() throws TemplateRenderingException {
		return items.size() > 0 ? super.getContent() : null;
	}
}
