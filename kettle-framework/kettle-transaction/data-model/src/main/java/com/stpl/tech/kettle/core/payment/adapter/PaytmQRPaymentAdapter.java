package com.stpl.tech.kettle.core.payment.adapter;

import com.stpl.tech.kettle.core.payment.PaymentAdapter;
import com.stpl.tech.kettle.core.service.PayTMPaymentService;
import com.stpl.tech.kettle.core.service.impl.PayTMPaymentServiceImpl;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class PaytmQRPaymentAdapter extends PaymentAdapter<OrderPaymentRequest,
        PaytmCreateRequest> {

    @Autowired
    PayTMPaymentService payTMPaymentService;

    @Override
    public PaytmCreateRequest createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {
        return payTMPaymentService.getPayTMQRCodeIdForKIOSK(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification,Integer brandId) {
        return null;
    }
}
