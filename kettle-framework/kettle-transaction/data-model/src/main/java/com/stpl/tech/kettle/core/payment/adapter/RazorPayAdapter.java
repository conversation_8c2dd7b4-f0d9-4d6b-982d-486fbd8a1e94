package com.stpl.tech.kettle.core.payment.adapter;

import com.stpl.tech.kettle.core.payment.PaymentAdapter;
import com.stpl.tech.kettle.core.service.RazorPayPaymentService;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.razorpay.RazorPayCreateRequest;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class RazorPayAdapter extends PaymentAdapter<OrderPaymentRequest , RazorPayCreateRequest> {

    @Autowired
    RazorPayPaymentService razorPayPaymentService ;

    @Override
    public RazorPayCreateRequest createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {
        return razorPayPaymentService.createRazorPayRequest(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification,Integer brandId) {
        RazorPayPaymentResponse response = (RazorPayPaymentResponse)object;
        if(skipSignatureVerification){
            return razorPayPaymentService.updateRazorPayResponseWithoutVerification(response);
        } else {
            return razorPayPaymentService.updateRazorPayResponse(response,brandId);
        }
    }

}
