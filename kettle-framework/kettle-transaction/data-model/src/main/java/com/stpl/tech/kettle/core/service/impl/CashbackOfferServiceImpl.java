package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.customer.service.CashBackOfferCache;
import com.stpl.tech.kettle.core.service.CashbackOfferService;
import com.stpl.tech.kettle.customer.dao.CashBackOfferDao;
import com.stpl.tech.kettle.data.model.CashBackOfferData;
import com.stpl.tech.kettle.offer.model.CashBackOfferDTO;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class CashbackOfferServiceImpl implements CashbackOfferService {

    @Autowired
    private CashBackOfferDao cashBackOfferDao;

    @Autowired
    private CashBackOfferCache cashBackOfferCache;

    @Override
    public boolean addCashbackOffer(CashBackOfferDTO offerDTO) {
        if(Objects.isNull(offerDTO.getUnitIds())  || Objects.isNull(offerDTO.getOfferStartDate())
         || Objects.isNull(offerDTO.getOfferStatus()) || Objects.isNull(offerDTO.getCashbackPercentage())
                || Objects.isNull(offerDTO.getOfferStartDate()) || Objects.isNull(offerDTO.getMaxNumberOfOrder())){
            log.info("Can't add cashback offer dto : {}", JSONSerializer.toJSON(offerDTO));
            return false;
        }
        List<CashBackOfferData> cashBackOfferDataList = cashBackOfferDao.findAllByUnitIdIn(offerDTO.getUnitIds());
        Map<Integer, CashBackOfferData> cashBackOfferDataMap = new HashMap<>();
        for(CashBackOfferData cashBackOfferData : cashBackOfferDataList){
            cashBackOfferDataMap.put(cashBackOfferData.getUnitId(), cashBackOfferData);
        }
        Map<Integer, CashBackOfferDTO> cashBackOfferDTOMap = cashBackOfferCache.getCashBackOfferCache();
        List<CashBackOfferData> dataToSave = new ArrayList<>();
        for(Integer unitId : offerDTO.getUnitIds()){
            cashBackOfferDTOMap.remove(unitId);
            if(cashBackOfferDataMap.containsKey(unitId)){
                dataToSave.add(getUpdatedCashBackOfferData(offerDTO, cashBackOfferDataMap.get(unitId)));
            }else{
                dataToSave.add(getNewCashBackOfferData(offerDTO, unitId));
            }
        }
        try{
            cashBackOfferDao.saveAll(dataToSave);
            cashBackOfferCache.updateCashBackOfferCache(cashBackOfferDTOMap);
            return true;
        }catch (Exception e){
            log.error("Error while saving cashback offer data");
        }
        return false;
    }

    @Override
    public List<CashBackOfferDTO> getCashbackOffers(CashBackOfferDTO offerDTO) {
        try{
            List<CashBackOfferData> cashBackOfferDataList = cashBackOfferDao.findAllByUnitIdIn(offerDTO.getUnitIds());
            List<CashBackOfferDTO> dtos = new ArrayList<>();
            for(CashBackOfferData data : cashBackOfferDataList){
                dtos.add(cashBackOfferCache.convertToCahBackOfferDTO(data));
            }
            return dtos;
        }catch (Exception e){
            log.info("Error while fetching cashback offer");
        }
        return new ArrayList<>();
    }

    private CashBackOfferData getNewCashBackOfferData(CashBackOfferDTO dto,Integer unitId){
        CashBackOfferData data = new CashBackOfferData();
        data.setUnitId(unitId);
        data.setOfferStartDate(AppUtils.getDate(dto.getOfferStartDate()));
        data.setOfferEndDate(AppUtils.getDate(dto.getOfferEndDate()));
        data.setLagDays(dto.getLagDays());
        data.setValidityInDays(dto.getValidityInDays());
        data.setCashbackPercentage(dto.getCashbackPercentage());
        data.setOfferStatus(dto.getOfferStatus());
        data.setMaxNumberOfOrder(dto.getMaxNumberOfOrder());
        return data;
    }

    private CashBackOfferData getUpdatedCashBackOfferData(CashBackOfferDTO dto,CashBackOfferData data){
        data.setOfferStartDate(AppUtils.getDate(dto.getOfferStartDate()));
        data.setOfferEndDate(AppUtils.getDate(dto.getOfferEndDate()));
        data.setLagDays(dto.getLagDays());
        data.setValidityInDays(dto.getValidityInDays());
        data.setCashbackPercentage(dto.getCashbackPercentage());
        data.setOfferStatus(dto.getOfferStatus());
        data.setMaxNumberOfOrder(dto.getMaxNumberOfOrder());
        return data;
    }
}
