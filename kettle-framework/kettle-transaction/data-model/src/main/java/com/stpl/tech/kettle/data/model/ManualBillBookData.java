package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "UNIT_MANUAL_BILL_BOOK_DETAIL", uniqueConstraints = {@UniqueConstraint(columnNames = {"START_NO", "END_NO"})})
public class ManualBillBookData implements  Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private int billBookId;
	private int startNo;
	private int endNo;
	private int unitId;
	private int stateId;
	private int transferOrderId;
	private String status;
	private Date creationTime;
	private Date activationTime;
	private Date deActivationTime;
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_MANUAL_BILL_BOOK_DETAIL_ID", unique = true, nullable = false)
	public int getBillBookId() {
		return billBookId;
	}
	
	public void setBillBookId(int billBookId) {
		this.billBookId = billBookId;
	}
	
	@Column(name = "START_NO", nullable = false)
	public int getStartNo() {
		return startNo;
	}
	public void setStartNo(int startNo) {
		this.startNo = startNo;
	}
	
	@Column(name = "END_NO", nullable = false)
	public int getEndNo() {
		return endNo;
	}
	public void setEndNo(int endNo) {
		this.endNo = endNo;
	}
	
	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}
	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}
	
	@Column(name = "TRANSFER_ORDER_ID", nullable = false)
	public int getTransferOrderId() {
		return transferOrderId;
	}

	public void setTransferOrderId(int transferOrderId) {
		this.transferOrderId = transferOrderId;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	
	@Column(name = "CREATION_TIME", nullable = false)
	public Date getCreationTime() {
		return creationTime;
	}
	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}
	
	@Column(name = "ACTIVATION_TIME")
	public Date getActivationTime() {
		return activationTime;
	}
	public void setActivationTime(Date activationTime) {
		this.activationTime = activationTime;
	}
	
	@Column(name = "DEACTIVATION_TIME")
	public Date getDeActivationTime() {
		return deActivationTime;
	}
	public void setDeActivationTime(Date deActivationTime) {
		this.deActivationTime = deActivationTime;
	}
	
	@Column(name = "STATE_ID")
	public int getStateId() {
		return stateId;
	}

	public void setStateId(int stateId) {
		this.stateId = stateId;
	}
	
	

}
