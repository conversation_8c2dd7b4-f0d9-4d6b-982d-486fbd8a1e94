package com.stpl.tech.kettle.core.notification;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.util.notification.AbstractTemplate;

public class CharityReceipt extends AbstractTemplate{

	private OrderInfo detail;

	private String basePath;

	private String fromEmail;

	private String toEmail;

	Map<String, Object> data = new HashMap<String, Object>();

	public CharityReceipt(OrderInfo detail, String basePath, String fromEmail, String toEmail) {
		super();
		this.detail = detail;
		this.basePath = basePath;
		this.fromEmail = fromEmail;
		this.toEmail = toEmail;
	}

	public OrderInfo getDetail() {
		return detail;
	}

	public void setDetail(OrderInfo detail) {
		this.detail = detail;
	}

	@Override
	public String getTemplatePath() {
		return "template/CharityOrderReceipt.html";
	}

	public Map<String, Object> getData() {
		data.put("order", detail.getOrder());
		data.put("unitName", detail.getUnit().getName());
		return data;
	}

	@Override
	public String getFilepath() {
		return basePath + "/" + "/charityorder/" + "/CharityReceipt-"
				+ detail.getOrder().getGenerateOrderId() + ".html";
	}

	public String getBasePath() {
		return basePath;
	}

	public String getFromEmail() {
		return fromEmail;
	}

	public String getToEmail() {
		return toEmail;
	}


}
