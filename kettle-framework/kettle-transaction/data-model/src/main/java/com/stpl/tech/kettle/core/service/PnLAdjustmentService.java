package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.domain.model.AdjustmentFieldData;
import com.stpl.tech.kettle.domain.model.AdjustmentFieldsResponse;
import com.stpl.tech.kettle.domain.model.PnlAdjustment;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.PnlAdjustmentStatus;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface PnLAdjustmentService {



    AdjustmentFieldsResponse getAdjustmentFields(String type);

    Map<String,AdjustmentFieldData> getAdjustmentMaps(String type);

    public PnlAdjustment createPnLAdjustment(PnlAdjustmentDetail adjustmentDetail);

    public PnlAdjustment updatePnLAdjustmentStatus(PnlAdjustmentDetail pnlAdjustmentDetail,PnlAdjustmentStatus status);

    Map<String ,ListData> getAdjustmentReasons() throws DataNotFoundException;

    List<PnlAdjustment> getAdjustments(PnlAdjustmentStatus status,List<Integer> unitIds,
				int startMonth,int startYear);

    List<PnlAdjustment> getAdjustmentImpact(String headerName, List<Integer> unitId,
                                            BigDecimal value, int month, int year);
}
