package com.stpl.tech.kettle.core.budget.dao;

import java.util.List;

import com.stpl.tech.kettle.data.model.UnitBankChargesBudgetDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetoryDetail;
import com.stpl.tech.kettle.data.model.UnitChannelPartnerChargesBudgetDetail;
import com.stpl.tech.kettle.data.model.UnitFacilityChargesBudgetDetail;
import com.stpl.tech.kettle.data.model.UnitManpowerBudgetDetail;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface BudgetMetadataDao extends AbstractDao {

	/**
	 * @param month
	 * @param year
	 */
	void cancelBudgetRecords(int month, int year);

	void cancelManpowerBudgetRecords(int month, int year);

	void updateBudgetRecords(List<UnitManpowerBudgetDetail> entityList);

	List<UnitBudgetoryDetail> getAllActiveBudgets(int year, int month);

	void cancelChannelPartnerChargesBudgetRecords(int month, int year);

	void updateChannelPartnerChargesBudgetRecords(List<UnitChannelPartnerChargesBudgetDetail> entityList);

	void cancelBankChargesBudgetRecords(int month, int year);

	void updateBankChargesBudgetRecords(List<UnitBankChargesBudgetDetail> entityList);

	void cancelFacilityChargesBudgetRecords(int month, int year);

	void updateFacilityChargesBudgetRecords(List<UnitFacilityChargesBudgetDetail> entityList);

}
